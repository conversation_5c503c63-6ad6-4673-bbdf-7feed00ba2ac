import{r as b,R as l,j as t,w as $}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as H}from"./react-redux-B5osdedR.js";import{w as U}from"./index-C8v-0ELK.js";import{G as k}from"./iconBase-2PDVWRGH.js";import{c as v}from"./utils-KsbccAr1.js";import{u as M}from"./useQuery-Cu2nkJ8V.js";import{O as L}from"./open-hands-Ce72Fmtl.js";import{u as A}from"./use-conversation-id-0JHAicdF.js";import{r as T}from"./retrieve-axios-error-message-CYr77e_f.js";import{u as V}from"./use-runtime-is-ready-CsTtc0dU.js";import{I as a}from"./declaration-xyc84-tJ.js";import{R as B}from"./agent-state-CFaY3go2.js";import{u as N}from"./useTranslation-BG59QWH_.js";import"./open-hands-axios-CtirLpss.js";import"./use-active-conversation-B8Aw3kE2.js";import"./i18nInstance-DBIXdvxg.js";function W(e){return k({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"},child:[]},{tag:"path",attr:{d:"M9 10h6"},child:[]},{tag:"path",attr:{d:"M12 13V7"},child:[]},{tag:"path",attr:{d:"M9 17h6"},child:[]}]})(e)}function q(e){return k({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"},child:[]},{tag:"path",attr:{d:"M14 2v4a2 2 0 0 0 2 2h4"},child:[]},{tag:"path",attr:{d:"M9 15h6"},child:[]}]})(e)}function K(e){return k({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"},child:[]},{tag:"path",attr:{d:"M14 2v4a2 2 0 0 0 2 2h4"},child:[]},{tag:"path",attr:{d:"M9 15h6"},child:[]},{tag:"path",attr:{d:"M12 18v-6"},child:[]}]})(e)}const Z=e=>{switch(e.split(".").pop()?.toLowerCase()){case"js":case"jsx":return"javascript";case"ts":case"tsx":return"typescript";case"py":return"python";case"html":return"html";case"css":return"css";case"json":return"json";case"md":return"markdown";case"yml":case"yaml":return"yaml";case"sh":case"bash":return"bash";case"dockerfile":return"dockerfile";case"rs":return"rust";case"go":return"go";case"java":return"java";case"cpp":case"cc":case"cxx":return"cpp";case"c":return"c";case"rb":return"ruby";case"php":return"php";case"sql":return"sql";default:return"text"}},z=e=>b.createElement("svg",{width:13,height:8,viewBox:"0 0 13 8",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},b.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.259766 6.17427L6.26425 0.715628L12.2688 6.17427L11.2597 7.28418L6.26425 2.74282L1.26878 7.28418L0.259766 6.17427Z",fill:"currentColor"})),Q=e=>{const{conversationId:s}=A();return M({queryKey:["file_diff",s,e.filePath,e.type],queryFn:()=>L.getGitChangeDiff(s,e.filePath),enabled:e.enabled,staleTime:1e3*60*5,gcTime:1e3*60*15})};function Y({className:e}){return t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:v("animate-spin rounded-full border-4 border-gray-200 border-t-blue-500",e),role:"status","aria-label":"Loading"})})}const w={A:K,D:q,M:W,R:"Renamed",U:"Untracked"};function J({path:e,type:s}){const[d,f]=l.useState(!0),[u,n]=l.useState(400),o=l.useRef(null),c=s==="A"||s==="U",m=s==="D",h=l.useMemo(()=>{if(s==="R"){const i=e.split(/\s+/).slice(1);return i[i.length-1]}return e},[e,s]),{data:p,isLoading:r,isSuccess:P,isRefetching:_}=Q({filePath:h,type:s,enabled:!d}),x=l.useCallback(()=>{if(o.current){const i=o.current.getOriginalEditor(),g=o.current.getModifiedEditor();if(i&&g){const S=i.getContentHeight(),F=g.getContentHeight(),G=Math.max(S,F);n(G+20)}}},[]),O=i=>{i.editor.defineTheme("custom-diff-theme",{base:"vs-dark",inherit:!0,rules:[{token:"comment",foreground:"6a9955"},{token:"keyword",foreground:"569cd6"},{token:"string",foreground:"ce9178"},{token:"number",foreground:"b5cea8"}],colors:{"diffEditor.insertedTextBackground":"#014b01AA","diffEditor.removedTextBackground":"#750000AA","diffEditor.insertedLineBackground":"#003f00AA","diffEditor.removedLineBackground":"#5a0000AA","diffEditor.border":"#444444","editorUnnecessaryCode.border":"#00000000","editorUnnecessaryCode.opacity":"#00000077"}})},D=i=>{o.current=i,x();const g=i.getOriginalEditor(),S=i.getModifiedEditor();g.onDidContentSizeChange(x),S.onDidContentSizeChange(x)},E=(s==="U"?w.A:w[s])||"?";let I;if(typeof E=="string")I=t.jsx("span",{children:E});else{const i=E;I=t.jsx(i,{className:"w-5 h-5"})}const y=r||_;return t.jsxs("div",{"data-testid":"file-diff-viewer-outer",className:"w-full flex flex-col",children:[t.jsx("div",{className:v("flex justify-between items-center px-2.5 py-3.5 border border-neutral-600 rounded-xl hover:cursor-pointer",!d&&!r&&"border-b-0 rounded-b-none"),onClick:()=>f(i=>!i),children:t.jsxs("span",{className:"text-sm w-full text-content flex items-center gap-2",children:[y&&t.jsx(Y,{className:"w-5 h-5"}),!y&&I,t.jsx("strong",{className:"w-full truncate",children:h}),t.jsx("button",{"data-testid":"collapse",type:"button",children:t.jsx(z,{className:v("w-4 h-4 transition-transform",d&&"transform rotate-180")})})]})}),P&&!d&&t.jsx("div",{className:"w-full border border-neutral-600 overflow-hidden",style:{height:`${u}px`},children:t.jsx(U,{"data-testid":"file-diff-viewer",className:"w-full h-full",language:Z(h),original:c?"":p.original,modified:m?"":p.modified,theme:"custom-diff-theme",onMount:D,beforeMount:O,options:{renderValidationDecorations:"off",readOnly:!0,renderSideBySide:!c&&!m,scrollBeyondLastLine:!1,minimap:{enabled:!1},hideUnchangedRegions:{enabled:!0},automaticLayout:!0,scrollbar:{alwaysConsumeMouseWheel:!1}}})})]})}const X=()=>{const{conversationId:e}=A(),[s,d]=l.useState([]),f=l.useRef(null),u=V(),n=M({queryKey:["file_changes",e],queryFn:()=>L.getGitChanges(e),retry:!1,staleTime:1e3*60*5,gcTime:1e3*60*15,enabled:u&&!!e,meta:{disableToast:!0}});return l.useEffect(()=>{if(!n.isFetching&&n.isSuccess&&n.data){const o=n.data;if(o!==f.current)if(f.current=o,Array.isArray(o)){const c=new Set(o.map(r=>r.path)),m=new Set(s.map(r=>r.path)),h=o.filter(r=>!m.has(r.path)),p=s.filter(r=>c.has(r.path));d([...h,...p])}else d([o])}},[n.isFetching,n.isSuccess,n.data]),{data:s,isLoading:n.isLoading,isSuccess:n.isSuccess,isError:n.isError,error:n.error}},j=[{key:a.TIPS$CUSTOMIZE_MICROAGENT,link:"https://docs.all-hands.dev/usage/prompting/microagents-repo"},{key:a.TIPS$SETUP_SCRIPT,link:"https://docs.all-hands.dev/usage/prompting/repository#setup-script"},{key:a.TIPS$VSCODE_INSTANCE},{key:a.TIPS$SAVE_WORK},{key:a.TIPS$SPECIFY_FILES,link:"https://docs.all-hands.dev/usage/prompting/prompting-best-practices"},{key:a.TIPS$HEADLESS_MODE,link:"https://docs.all-hands.dev/usage/how-to/headless-mode"},{key:a.TIPS$CLI_MODE,link:"https://docs.all-hands.dev/usage/how-to/cli-mode"},{key:a.TIPS$GITHUB_HOOK,link:"https://docs.all-hands.dev/usage/cloud/github-installation#working-on-github-issues-and-pull-requests-using-openhands"},{key:a.TIPS$BLOG_SIGNUP,link:"https://www.all-hands.dev/blog"},{key:a.TIPS$API_USAGE,link:"https://docs.all-hands.dev/api-reference/health-check"}];function R(){const e=Math.floor(Math.random()*j.length);return j[e]}function ee(){const{t:e}=N(),[s,d]=l.useState(R());return l.useEffect(()=>{d(R())},[]),t.jsxs("div",{children:[t.jsxs("h4",{className:"font-bold",children:[e(a.TIPS$PROTIP),":"]}),t.jsx("p",{children:e(s.key)}),s.link&&t.jsxs(t.Fragment,{children:[" ",t.jsx("a",{href:s.link,target:"_blank",rel:"noopener noreferrer",className:"underline",children:e(a.TIPS$LEARN_MORE)})]})]})}const C=/not a git repository/i;function te({children:e}){return t.jsx("div",{className:"w-full h-full flex flex-col items-center text-center justify-center text-2xl text-tertiary-light",children:e})}function se(){const{t:e}=N(),{data:s,isSuccess:d,isError:f,error:u,isLoading:n}=X(),[o,c]=l.useState(null),{curAgentState:m}=H(r=>r.agent),h=!B.includes(m),p=u&&C.test(T(u));return l.useEffect(()=>{if(!h)c([a.DIFF_VIEWER$WAITING_FOR_RUNTIME]);else if(u){const r=T(u);C.test(r)?c([a.DIFF_VIEWER$NOT_A_GIT_REPO,a.DIFF_VIEWER$ASK_OH]):c([r])}else c(n?[a.DIFF_VIEWER$LOADING]:null)},[h,p,n,u,c]),t.jsx("main",{className:"h-full overflow-y-scroll px-4 py-3 gap-3 flex flex-col items-center",children:!d||!s.length?t.jsxs("div",{className:"relative flex h-full w-full items-center",children:[t.jsx("div",{className:"absolute inset-x-0 top-1/2 -translate-y-1/2",children:o&&t.jsx(te,{children:o.map(r=>t.jsx("span",{children:e(r)},r))})}),t.jsx("div",{className:"absolute inset-x-0 bottom-0",children:!f&&s?.length===0&&t.jsx("div",{className:"max-w-2xl mb-4 text-m bg-tertiary rounded-xl p-4 text-left mx-auto",children:t.jsx(ee,{})})})]}):s.map(r=>t.jsx(J,{path:r.path,type:r.status},r.path))})}const Ie=$(se);export{Ie as default};
