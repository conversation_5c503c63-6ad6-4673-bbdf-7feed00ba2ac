import{R as u,r as d,j as e,d as b,i as le,u as ce,b as P,N as de,H as ue,w as me,O as he,I as fe,l as pe}from"./chunk-C37GKA54-CBbYr_fP.js";import{I as r}from"./declaration-xyc84-tJ.js";import"./index-DbrbOxWj.js";import{D as xe,a as U,u as Y}from"./use-settings-CSlhfPqo.js";import{u as L,a as Ee}from"./use-config-jdwF3W4-.js";import{u as J}from"./useQuery-Cu2nkJ8V.js";import{p as ee}from"./module-5laXsVNO.js";import{O as j}from"./open-hands-Ce72Fmtl.js";import{u as Ce}from"./use-user-providers-CVWOd-tS.js";import{L as W}from"./loading-spinner-D6qcLhqr.js";import{c as te}from"./utils-KsbccAr1.js";import{u as m}from"./useTranslation-BG59QWH_.js";import{T as A}from"./tooltip-button-Bd3YrFXr.js";import{u as se,C as ge,a as Ne,B as Ie,b as $,c as Q,M as q,d as ve}from"./conversation-card-BnTfWkKs.js";import{S as k}from"./all-hands-logo-D_ipIZjH.js";import{S as Se}from"./plus-VpD79E1k.js";import{S as Ae}from"./settings-BJuJgO6o.js";import{a as _e}from"./index-Do49u1Ze.js";import{M as Te,o as Oe,H as Me,u as Le}from"./help-link-CHYsvCwj.js";import{M as v}from"./modal-backdrop-ve4Sk5I2.js";import{e as je}from"./settings-utils-C--CGkWg.js";import{B as I,u as G}from"./brand-button-3Z8FN4qR.js";import{K as we}from"./key-status-icon-DbpY1ViG.js";import{S as ye}from"./settings-input-i4i_IemM.js";import{u as Z}from"./use-save-settings-Bb4p0cGE.js";import{u as z}from"./open-hands-axios-CtirLpss.js";import{M as _}from"./constants-DCYeMzG2.js";import{d as ne,a as ae}from"./custom-toast-handlers-CR9P-jKI.js";import{R as be}from"./index-yKbcr7Pf.js";import{u as Re,g as De,L as D,s as Pe,a as X}from"./use-logout-Bpg8Vogf.js";import{h as oe}from"./handle-capture-consent-L0_Qc6gK.js";import{u as Ue}from"./use-balance-BpOxWBAr.js";import{T as $e}from"./Trans-CI4prNur.js";import{h as ke}from"./index-DvLMSsrd.js";import{i as Ge}from"./i18next-CO45VQzB.js";import"./preload-helper-BXl3LOEh.js";import"./i18nInstance-DBIXdvxg.js";import"./chunk-BOOVDPB6-Dt9AQnxs.js";import"./chunk-S6H5EOGR-Bwn62IP6.js";import"./react-redux-B5osdedR.js";import"./use-conversation-id-0JHAicdF.js";import"./agent-state-CFaY3go2.js";import"./vscode-url-helper-5Tt6LlE2.js";import"./ws-client-provider-Dmsj8lkD.js";import"./store-Bya9Reqe.js";import"./browser-slice-DabBaamq.js";import"./query-client-config-CJn-5u6A.js";import"./retrieve-axios-error-message-CYr77e_f.js";import"./mutation-B9dSlWD-.js";import"./use-active-conversation-B8Aw3kE2.js";import"./use-optimistic-user-message-tdysaQ5t.js";import"./iconBase-2PDVWRGH.js";import"./map-provider-g8SgAMsv.js";import"./optional-tag-e1gRgM9y.js";import"./index-cxP66Ws3.js";const Be=(t,n)=>{const a=`${n.hostname==="localhost"?n.protocol:"https:"}//${n.host}/oauth/keycloak/callback`;let o=n.hostname.replace(/(^|\.)staging\.all-hands\.dev$/,"$1auth.staging.all-hands.dev").replace(/(^|\.)app\.all-hands\.dev$/,"auth.app.all-hands.dev").replace(/(^|\.)localhost$/,"auth.staging.all-hands.dev");o===n.hostname&&n.hostname!=="localhost"&&(o=`auth.${n.hostname}`);const i="openid email profile",c=n.search?"&":"?",l=`${n.href.replace(/\/$/,"")}${c}login_method=${t}`;return`https://${o}/realms/allhands/protocol/openid-connect/auth?client_id=allhands&kc_idp_hint=${t}&response_type=code&redirect_uri=${encodeURIComponent(a)}&scope=${encodeURIComponent(i)}&state=${encodeURIComponent(l)}`},M=t=>t.appMode==="saas"?Be(t.identityProvider,new URL(window.location.href)):null,Fe=t=>M({appMode:t.appMode,identityProvider:"github"}),He=()=>{const{providers:t}=Ce(),{data:n}=L(),s=J({queryKey:["user"],queryFn:j.getGitUser,enabled:!!n?.APP_MODE&&t.length>0,retry:!1,staleTime:1e3*60*5,gcTime:1e3*60*15});return u.useEffect(()=>{s.data&&ee.identify(s.data.login,{company:s.data.company,name:s.data.name,email:s.data.email,user:s.data.login,mode:n?.APP_MODE||"oss"})},[s.data]),s},Ve=t=>d.createElement("svg",{width:27,height:26,viewBox:"0 0 27 26",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t},d.createElement("g",{clipPath:"url(#clip0_40000158_426)"},d.createElement("path",{d:"M13.5381 1.83496C7.32618 1.83496 2.29074 6.8704 2.29074 13.0823C2.29074 19.2942 7.32618 24.3297 13.5381 24.3297C19.75 24.3297 24.7855 19.2942 24.7855 13.0823C24.7855 6.8704 19.75 1.83496 13.5381 1.83496Z",stroke:"currentColor",strokeWidth:1.8,strokeLinecap:"round",strokeLinejoin:"round"}),d.createElement("path",{d:"M4.84641 20.22C4.84641 20.22 7.35344 17.019 13.5395 17.019C19.7255 17.019 22.2337 20.22 22.2337 20.22M13.5395 13.0824C14.4344 13.0824 15.2926 12.7269 15.9254 12.0941C16.5582 11.4613 16.9137 10.6031 16.9137 9.70819C16.9137 8.8133 16.5582 7.95505 15.9254 7.32227C15.2926 6.68948 14.4344 6.33398 13.5395 6.33398C12.6446 6.33398 11.7863 6.68948 11.1536 7.32227C10.5208 7.95505 10.1653 8.8133 10.1653 9.70819C10.1653 10.6031 10.5208 11.4613 11.1536 12.0941C11.7863 12.7269 12.6446 13.0824 13.5395 13.0824Z",stroke:"currentColor",strokeWidth:1.8,strokeLinecap:"round",strokeLinejoin:"round"})),d.createElement("defs",null,d.createElement("clipPath",{id:"clip0_40000158_426"},d.createElement("rect",{width:26,height:26,fill:"white",transform:"translate(0.5)"}))));function Ke({src:t}){const{t:n}=m();return e.jsx("img",{src:t,alt:n(r.AVATAR$ALT_TEXT),className:"w-full h-full rounded-full"})}function Ye({onClick:t,avatarUrl:n,isLoading:s}){const{t:a}=m();return e.jsxs(A,{testId:"user-avatar",tooltip:a(r.USER$ACCOUNT_SETTINGS),ariaLabel:a(r.USER$ACCOUNT_SETTINGS),onClick:t,className:te("w-8 h-8 rounded-full flex items-center justify-center cursor-pointer",s&&"bg-transparent"),children:[!s&&n&&e.jsx(Ke,{src:n}),!s&&!n&&e.jsx(Ve,{"aria-label":a(r.USER$AVATAR_PLACEHOLDER),width:28,height:28,className:"text-[#9099AC]"}),s&&e.jsx(W,{size:"small"})]})}function We({onLogout:t,onClose:n}){const s=se(n),{t:a}=m();return e.jsx(ge,{testId:"account-settings-context-menu",ref:s,className:"absolute right-full md:left-full -top-1 z-10 w-fit",children:e.jsx(Ne,{onClick:t,children:a(r.ACCOUNT_SETTINGS$LOGOUT)})})}function Qe({onLogout:t,user:n,isLoading:s}){const[a,o]=u.useState(!1),i=()=>{o(l=>!l)},c=()=>{o(!1)},f=()=>{t(),c()};return e.jsxs("div",{"data-testid":"user-actions",className:"w-8 h-8 relative cursor-pointer",children:[e.jsx(Ye,{avatarUrl:n?.avatar_url,onClick:i,isLoading:s}),a&&!!n&&e.jsx(We,{onLogout:f,onClose:c})]})}function Ze(){const{t}=m();return e.jsx(A,{tooltip:t(r.BRANDING$ALL_HANDS_AI),ariaLabel:t(r.BRANDING$ALL_HANDS_LOGO),navLinkTo:"/",children:e.jsx(k,{width:34,height:34})})}const ze=t=>d.createElement("svg",{width:27,height:27,viewBox:"0 0 27 27",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t},d.createElement("path",{d:"M24.5719 9.17199V14.8456M15.7404 3.4984C14.9543 3.12524 14.095 2.93164 13.2248 2.93164C12.3546 2.93164 11.4952 3.12524 10.7091 3.4984L3.11671 7.05801C1.46456 7.83189 1.46456 10.5121 3.11671 11.286L10.708 14.8456C11.4943 15.2189 12.3538 15.4126 13.2242 15.4126C14.0946 15.4126 14.9541 15.2189 15.7404 14.8456L23.3328 11.286C24.985 10.5121 24.985 7.83189 23.3328 7.05801L15.7404 3.4984Z",stroke:"currentColor",strokeWidth:1.8,strokeLinecap:"round",strokeLinejoin:"round"}),d.createElement("path",{d:"M5.28183 12.5762V18.3916C5.28183 21.7027 10.6082 23.356 13.2249 23.356C15.8415 23.356 21.1679 21.7027 21.1679 18.3916V12.5762",stroke:"currentColor",strokeWidth:1.8,strokeLinecap:"round",strokeLinejoin:"round"}));function qe({disabled:t=!1}){const{t:n}=m();return e.jsx(A,{tooltip:n(r.SIDEBAR$DOCS),ariaLabel:n(r.SIDEBAR$DOCS),href:"https://docs.all-hands.dev",disabled:t,children:e.jsx(ze,{width:28,height:28,className:`text-[#9099AC] ${t?"opacity-50":""}`})})}function Xe({disabled:t=!1}){const{t:n}=m(),s=n(r.CONVERSATION$START_NEW);return e.jsx(A,{tooltip:s,ariaLabel:s,navLinkTo:"/",testId:"new-project-button",disabled:t,children:e.jsx(Se,{width:28,height:28})})}function Je({onClick:t,disabled:n=!1}){const{t:s}=m(),{data:a}=L(),o=a?.APP_MODE==="saas"?"/settings/user":"/settings";return e.jsx(A,{testId:"settings-button",tooltip:s(r.SETTINGS$TITLE),ariaLabel:s(r.SETTINGS$TITLE),onClick:t,navLinkTo:o,disabled:n,children:e.jsx(Ae,{width:28,height:28})})}function et({isOpen:t,onClick:n,disabled:s=!1}){const{t:a}=m();return e.jsx(A,{testId:"toggle-conversation-panel",tooltip:a(r.SIDEBAR$CONVERSATIONS),ariaLabel:a(r.SIDEBAR$CONVERSATIONS),onClick:n,disabled:s,children:e.jsx(_e,{size:22,className:te("cursor-pointer",t?"text-white":"text-[#9099AC]",s&&"opacity-50")})})}function tt({testId:t,title:n,description:s,buttons:a}){return e.jsx(Ie,{testId:t,title:n,description:s,buttons:[{text:a.danger.text,onClick:a.danger.onClick,className:"bg-danger"},{text:a.cancel.text,onClick:a.cancel.onClick,className:"bg-neutral-500"}]})}function st({settings:t,models:n,onClose:s}){const{mutate:a}=Z(),o=b(),{t:i}=m(),c=u.useRef(null),[f,l]=u.useState(!1),p=async N=>{const E=je(N);await a(E,{onSuccess:()=>{s(),ee.capture("settings_saved",{LLM_MODEL:E.LLM_MODEL,LLM_API_KEY_SET:E.LLM_API_KEY_SET?"SET":"UNSET",SEARCH_API_KEY_SET:E.SEARCH_API_KEY?"SET":"UNSET",REMOTE_RUNTIME_RESOURCE_FACTOR:E.REMOTE_RUNTIME_RESOURCE_FACTOR})}})},C=()=>{const N=new FormData(c.current??void 0);p(N)},x=N=>{N.preventDefault();const E=new FormData(N.currentTarget);o.pathname.startsWith("/conversations/")?l(!0):p(E)},g=t.LLM_API_KEY_SET;return e.jsxs("div",{children:[e.jsxs("form",{ref:c,"data-testid":"settings-form",className:"flex flex-col gap-6",onSubmit:x,children:[e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx(Te,{models:Oe(n),currentModel:t.LLM_MODEL}),e.jsx(ye,{testId:"llm-api-key-input",name:"llm-api-key-input",label:i(r.SETTINGS_FORM$API_KEY),type:"password",className:"w-full",placeholder:g?"<hidden>":"",startContent:g&&e.jsx(we,{isSet:g})}),e.jsx(Me,{testId:"llm-api-key-help-anchor",text:i(r.SETTINGS$DONT_KNOW_API_KEY),linkText:i(r.SETTINGS$CLICK_FOR_INSTRUCTIONS),href:"https://docs.all-hands.dev/usage/local-setup#getting-an-api-key"})]}),e.jsx("div",{className:"flex flex-col gap-2",children:e.jsx(I,{testId:"save-settings-button",type:"submit",variant:"primary",className:"w-full",children:i(r.BUTTON$SAVE)})})]}),f&&e.jsx(v,{children:e.jsx(tt,{title:i(r.MODAL$END_SESSION_TITLE),description:i(r.MODAL$END_SESSION_MESSAGE),buttons:{danger:{text:i(r.BUTTON$END_SESSION),onClick:C},cancel:{text:i(r.BUTTON$CANCEL),onClick:()=>l(!1)}}})})]})}function nt({onClose:t,settings:n}){const s=Le(),{t:a}=m();return e.jsx(v,{children:e.jsxs("div",{"data-testid":"ai-config-modal",className:"bg-base-secondary min-w-[384px] m-4 p-6 rounded-xl flex flex-col gap-2 border border-tertiary",children:[s.error&&e.jsx("p",{className:"text-danger text-xs",children:s.error.message}),e.jsx("span",{className:"text-xl leading-6 font-semibold -tracking-[0.01em]",children:a(r.AI_SETTINGS$TITLE)}),e.jsxs("p",{className:"text-xs text-[#A3A3A3]",children:[a(r.SETTINGS$DESCRIPTION)," ",a(r.SETTINGS$FOR_OTHER_OPTIONS),e.jsx(le,{"data-testid":"advanced-settings-link",to:"/settings",className:"underline underline-offset-2 text-white",children:a(r.SETTINGS$SEE_ADVANCED_SETTINGS)})]}),s.isLoading&&e.jsx("div",{className:"flex justify-center",children:e.jsx(W,{size:"small"})}),s.data&&e.jsx(st,{settings:n||xe,models:s.data?.models,onClose:t})]})})}const at=()=>{const{data:t}=U();return J({queryKey:["user","conversations"],queryFn:j.getUserConversations,enabled:!!t})},ot=()=>{const t=z();return G({mutationFn:n=>j.deleteUserConversation(n.conversationId),onMutate:async n=>{await t.cancelQueries({queryKey:["user","conversations"]});const s=t.getQueryData(["user","conversations"]);return t.setQueryData(["user","conversations"],a=>a?.filter(o=>o.conversation_id!==n.conversationId)),{previousConversations:s}},onError:(n,s,a)=>{a?.previousConversations&&t.setQueryData(["user","conversations"],a.previousConversations)},onSettled:()=>{t.invalidateQueries({queryKey:["user","conversations"]})}})},rt=()=>{const t=z();return G({mutationFn:n=>j.stopConversation(n.conversationId),onMutate:async()=>(await t.cancelQueries({queryKey:["user","conversations"]}),{previousConversations:t.getQueryData(["user","conversations"])}),onError:(n,s,a)=>{a?.previousConversations&&t.setQueryData(["user","conversations"],a.previousConversations)},onSettled:()=>{t.invalidateQueries({queryKey:["user","conversations"]})}})};function it({onConfirm:t,onCancel:n}){const{t:s}=m();return e.jsx(v,{children:e.jsxs(_,{className:"items-start border border-tertiary",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx($,{title:s(r.CONVERSATION$CONFIRM_DELETE)}),e.jsx(Q,{description:s(r.CONVERSATION$DELETE_WARNING)})]}),e.jsxs("div",{className:"flex flex-col gap-2 w-full",onClick:a=>a.stopPropagation(),children:[e.jsx(I,{type:"button",variant:"primary",onClick:t,className:"w-full","data-testid":"confirm-button",children:s(r.ACTION$CONFIRM)}),e.jsx(I,{type:"button",variant:"secondary",onClick:n,className:"w-full","data-testid":"cancel-button",children:s(r.BUTTON$CANCEL)})]})]})})}function lt({onConfirm:t,onCancel:n}){const{t:s}=m();return e.jsx(v,{children:e.jsxs(_,{className:"items-start border border-tertiary",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx($,{title:s(r.CONVERSATION$CONFIRM_STOP)}),e.jsx(Q,{description:s(r.CONVERSATION$STOP_WARNING)})]}),e.jsxs("div",{className:"flex flex-col gap-2 w-full",onClick:a=>a.stopPropagation(),children:[e.jsx(I,{type:"button",variant:"primary",onClick:t,className:"w-full","data-testid":"confirm-button",children:s(r.ACTION$CONFIRM)}),e.jsx(I,{type:"button",variant:"secondary",onClick:n,className:"w-full","data-testid":"cancel-button",children:s(r.BUTTON$CANCEL)})]})]})})}function ct({onConfirm:t,onClose:n}){const{t:s}=m();return e.jsx(v,{children:e.jsxs(_,{testID:"confirm-new-conversation-modal",children:[e.jsx($,{title:s(r.CONVERSATION$EXIT_WARNING)}),e.jsxs("div",{className:"flex w-full gap-2",children:[e.jsx(q,{text:s(r.ACTION$CONFIRM),onClick:t,className:"bg-[#C63143] flex-1"}),e.jsx(q,{text:s(r.BUTTON$CANCEL),onClick:n,className:"bg-tertiary flex-1"})]})]})})}const dt=()=>{const t=z();return G({mutationFn:n=>j.updateConversation(n.conversationId,{title:n.newTitle}),onMutate:async n=>{await t.cancelQueries({queryKey:["user","conversations"]});const s=t.getQueryData(["user","conversations"]);return t.setQueryData(["user","conversations"],a=>a?.map(o=>o.conversation_id===n.conversationId?{...o,title:n.newTitle}:o)),{previousConversations:s}},onError:(n,s,a)=>{a?.previousConversations&&t.setQueryData(["user","conversations"],a.previousConversations)},onSettled:(n,s,a)=>{t.invalidateQueries({queryKey:["user","conversations"]}),t.invalidateQueries({queryKey:["user","conversation",a.conversationId]})}})};function ut({onClose:t}){const{t:n}=m(),{conversationId:s}=ce(),a=se(t),o=P(),[i,c]=u.useState(!1),[f,l]=u.useState(!1),[p,C]=u.useState(!1),[x,g]=u.useState(null),[N,E]=u.useState(null),{data:T,isFetching:S,error:w}=at(),{mutate:y}=ot(),{mutate:B}=rt(),{mutate:F}=dt(),O=h=>{c(!0),g(h)},R=h=>{l(!0),g(h)},H=async(h,V)=>{F({conversationId:h,newTitle:V},{onSuccess:()=>{ne(n(r.CONVERSATION$TITLE_UPDATED))}})},re=()=>{x&&y({conversationId:x},{onSuccess:()=>{x===s&&o("/")}})},ie=()=>{x&&B({conversationId:x},{onSuccess:()=>{x===s&&o("/")}})};return e.jsxs("div",{ref:a,"data-testid":"conversation-panel",className:"w-[350px] h-full border border-neutral-700 bg-base-secondary rounded-xl overflow-y-auto absolute",children:[S&&e.jsx("div",{className:"w-full h-full absolute flex justify-center items-center",children:e.jsx(W,{size:"small"})}),w&&e.jsx("div",{className:"flex flex-col items-center justify-center h-full",children:e.jsx("p",{className:"text-danger",children:w.message})}),T?.length===0&&e.jsx("div",{className:"flex flex-col items-center justify-center h-full",children:e.jsx("p",{className:"text-neutral-400",children:n(r.CONVERSATION$NO_CONVERSATIONS)})}),T?.map(h=>e.jsx(de,{to:`/conversations/${h.conversation_id}`,onClick:t,children:({isActive:V})=>e.jsx(ve,{isActive:V,onDelete:()=>O(h.conversation_id),onStop:()=>R(h.conversation_id),onChangeTitle:K=>H(h.conversation_id,K),title:h.title,selectedRepository:{selected_repository:h.selected_repository,selected_branch:h.selected_branch,git_provider:h.git_provider},lastUpdatedAt:h.last_updated_at,createdAt:h.created_at,conversationStatus:h.status,conversationId:h.conversation_id,contextMenuOpen:N===h.conversation_id,onContextMenuToggle:K=>E(K?h.conversation_id:null)})},h.conversation_id)),i&&e.jsx(it,{onConfirm:()=>{re(),c(!1)},onCancel:()=>c(!1)}),f&&e.jsx(lt,{onConfirm:()=>{ie(),l(!1)},onCancel:()=>l(!1)}),p&&e.jsx(ct,{onConfirm:()=>{t()},onClose:()=>C(!1)})]})}function mt({isOpen:t,children:n}){if(!t)return null;const s=document.getElementById("root-outlet");return s?be.createPortal(e.jsx("div",{className:"absolute h-full w-full left-0 top-0 z-20 bg-black/80 rounded-xl",children:n}),s):null}const ht=t=>d.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:21,viewBox:"0 0 24 21",fill:"none",...t},d.createElement("path",{d:"M3 1.5C2.17157 1.5 1.5 2.17157 1.5 3V15C1.5 15.8284 2.17157 16.5 3 16.5H11.25C11.6642 16.5 12 16.8358 12 17.25C12 17.6642 11.6642 18 11.25 18H3C1.34315 18 0 16.6569 0 15V3C0 1.34315 1.34315 0 3 0H19.5C21.1569 0 22.5 1.34315 22.5 3V9C22.5 9.41421 22.1642 9.75 21.75 9.75C21.3358 9.75 21 9.41421 21 9V3C21 2.17157 20.3284 1.5 19.5 1.5H3Z",fill:"currentColor"}),d.createElement("path",{d:"M4.71967 4.71967C5.01256 4.42678 5.48744 4.42678 5.78033 4.71967L7.76517 6.7045C8.2045 7.14384 8.2045 7.85616 7.76517 8.2955L5.78033 10.2803C5.48744 10.5732 5.01256 10.5732 4.71967 10.2803C4.42678 9.98744 4.42678 9.51256 4.71967 9.21967L6.43934 7.5L4.71967 5.78033C4.42678 5.48744 4.42678 5.01256 4.71967 4.71967Z",fill:"currentColor"}),d.createElement("path",{d:"M8.25 10.5C8.25 10.0858 8.58579 9.75 9 9.75H12C12.4142 9.75 12.75 10.0858 12.75 10.5C12.75 10.9142 12.4142 11.25 12 11.25H9C8.58579 11.25 8.25 10.9142 8.25 10.5Z",fill:"currentColor"}),d.createElement("path",{d:"M24 15.75C24 18.6495 21.6495 21 18.75 21C15.8505 21 13.5 18.6495 13.5 15.75C13.5 12.8505 15.8505 10.5 18.75 10.5C21.6495 10.5 24 12.8505 24 15.75ZM18.75 12.75C18.3358 12.75 18 13.0858 18 13.5V15H16.5C16.0858 15 15.75 15.3358 15.75 15.75C15.75 16.1642 16.0858 16.5 16.5 16.5H18V18C18 18.4142 18.3358 18.75 18.75 18.75C19.1642 18.75 19.5 18.4142 19.5 18V16.5H21C21.4142 16.5 21.75 16.1642 21.75 15.75C21.75 15.3358 21.4142 15 21 15H19.5V13.5C19.5 13.0858 19.1642 12.75 18.75 12.75Z",fill:"currentColor"}));function ft({disabled:t=!1}){const{t:n}=m(),s=n(r.MICROAGENT_MANAGEMENT$TITLE);return e.jsx(A,{tooltip:s,ariaLabel:s,navLinkTo:"/microagent-management",testId:"microagent-management-button",disabled:t,children:e.jsx(ht,{})})}function pt(){const t=b(),n=He(),{data:s}=L(),{data:a,error:o,isError:i,isFetching:c}=Y(),{mutate:f}=Re(),[l,p]=u.useState(!1),[C,x]=u.useState(!1),g=s?.FEATURE_FLAGS.HIDE_LLM_SETTINGS&&s?.APP_MODE==="saas",N=s?.FEATURE_FLAGS.HIDE_MICROAGENT_MANAGEMENT;return u.useEffect(()=>{g||(t.pathname==="/settings"?p(!1):!c&&i&&o?.status!==404?ae("Something went wrong while fetching settings. Please reload the page."):s?.APP_MODE==="oss"&&o?.status===404&&p(!0))},[o?.status,o,c,t.pathname]),e.jsxs(e.Fragment,{children:[e.jsxs("aside",{className:"h-[40px] md:h-auto px-1 flex flex-row md:flex-col gap-1",children:[e.jsxs("nav",{className:"flex flex-row md:flex-col items-center justify-between w-full h-auto md:w-auto md:h-full",children:[e.jsxs("div",{className:"flex flex-row md:flex-col items-center gap-[26px]",children:[e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(Ze,{})}),e.jsx(Xe,{disabled:a?.EMAIL_VERIFIED===!1}),e.jsx(et,{isOpen:C,onClick:()=>a?.EMAIL_VERIFIED===!1?null:x(E=>!E),disabled:a?.EMAIL_VERIFIED===!1}),!N&&e.jsx(ft,{disabled:a?.EMAIL_VERIFIED===!1})]}),e.jsxs("div",{className:"flex flex-row md:flex-col md:items-center gap-[26px] md:mb-4",children:[e.jsx(qe,{disabled:a?.EMAIL_VERIFIED===!1}),e.jsx(Je,{disabled:a?.EMAIL_VERIFIED===!1}),e.jsx(Qe,{user:n.data?{avatar_url:n.data.avatar_url}:void 0,onLogout:f,isLoading:n.isFetching})]})]}),C&&e.jsx(mt,{isOpen:C,children:e.jsx(ut,{onClose:()=>x(!1)})})]}),l&&e.jsx(nt,{settings:a,onClose:()=>p(!1)})]})}const xt=t=>d.createElement("svg",{width:22,height:22,viewBox:"0 0 22 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t},d.createElement("path",{d:"M12 2.25C6.624 2.25 2.25 6.624 2.25 12C2.25 16.376 5.115 20.073 9.057 21.428C9.563 21.514 9.747 21.211 9.747 20.95C9.747 20.713 9.738 19.991 9.738 19.153C7 19.713 6.4 18.45 6.4 18.45C5.952 17.387 5.328 17.084 5.328 17.084C4.476 16.487 5.387 16.487 5.387 16.487C6.328 16.546 6.85 17.481 6.85 17.481C7.675 18.862 9.057 18.487 9.776 18.226C9.867 17.603 10.133 17.179 10.419 16.94C8.287 16.713 6.05 15.85 6.05 11.9C6.05 10.837 6.4 9.975 6.859 9.3C6.759 9.05 6.45 8.038 6.95 6.65C6.95 6.65 7.734 6.4 9.738 7.775C10.483 7.534 11.25 7.413 12.017 7.413C12.784 7.413 13.55 7.534 14.296 7.775C16.3 6.4 17.084 6.65 17.084 6.65C17.584 8.038 17.275 9.05 17.175 9.3C17.634 9.975 17.984 10.837 17.984 11.9C17.984 15.85 15.747 16.7 13.615 16.94C13.975 17.237 14.296 17.813 14.296 18.7C14.296 19.975 14.287 20.6 14.287 20.95C14.287 21.211 14.471 21.514 14.977 21.428C18.919 20.073 21.784 16.376 21.784 12C21.784 6.624 17.376 2.25 12 2.25Z",fill:"currentColor"})),Et=t=>d.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"97 99 186 182",...t},d.createElement("defs",null,d.createElement("style",null,".cls-1{fill:currentColor;}")),d.createElement("g",{id:"LOGO"},d.createElement("path",{className:"cls-1",d:"M282.83,170.73l-.27-.69-26.14-68.22a6.81,6.81,0,0,0-2.69-3.24,7,7,0,0,0-8,.43,7,7,0,0,0-2.32,3.52l-17.65,54H154.29l-17.65-54A6.86,6.86,0,0,0,134.32,99a7,7,0,0,0-8-.43,6.87,6.87,0,0,0-2.69,3.24L97.44,170l-.26.69a48.54,48.54,0,0,0,16.1,56.1l.09.07.24.17,39.82,29.82,19.7,14.91,12,9.06a8.07,8.07,0,0,0,9.76,0l12-9.06,19.7-14.91,40.06-30,.1-.08A48.56,48.56,0,0,0,282.83,170.73Z"}))),Ct=t=>d.createElement("svg",{role:"img",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",...t},d.createElement("title",null,"Bitbucket"),d.createElement("path",{d:"M.778 1.213a.768.768 0 00-.768.892l3.263 19.81c.084.5.515.868 1.022.873H19.95a.772.772 0 00.77-.646l3.27-20.03a.768.768 0 00-.768-.891zM14.52 15.53H9.522L8.17 8.466h7.561z"}));function gt({githubAuthUrl:t,appMode:n,providersConfigured:s}){const{t:a}=m(),o=M({appMode:n||null,identityProvider:"gitlab"}),i=M({appMode:n||null,identityProvider:"bitbucket"}),c=()=>{t&&(window.location.href=t)},f=()=>{o&&(window.location.href=o)},l=()=>{i&&(window.location.href=i)},p=s&&s.length>0&&s.includes("github"),C=s&&s.length>0&&s.includes("gitlab"),x=s&&s.length>0&&s.includes("bitbucket"),g=!s||s.length===0;return e.jsx(v,{children:e.jsxs(_,{className:"border border-tertiary",children:[e.jsx(k,{width:68,height:46}),e.jsx("div",{className:"flex flex-col gap-2 w-full items-center text-center",children:e.jsx("h1",{className:"text-2xl font-bold",children:a(r.AUTH$SIGN_IN_WITH_IDENTITY_PROVIDER)})}),e.jsx("div",{className:"flex flex-col gap-3 w-full",children:g?e.jsx("div",{className:"text-center p-4 text-muted-foreground",children:a(r.AUTH$NO_PROVIDERS_CONFIGURED)}):e.jsxs(e.Fragment,{children:[p&&e.jsx(I,{type:"button",variant:"primary",onClick:c,className:"w-full",startContent:e.jsx(xt,{width:20,height:20}),children:a(r.GITHUB$CONNECT_TO_GITHUB)}),C&&e.jsx(I,{type:"button",variant:"primary",onClick:f,className:"w-full",startContent:e.jsx(Et,{width:20,height:20}),children:a(r.GITLAB$CONNECT_TO_GITLAB)}),x&&e.jsx(I,{type:"button",variant:"primary",onClick:l,className:"w-full",startContent:e.jsx(Ct,{width:20,height:20}),children:a(r.BITBUCKET$CONNECT_TO_BITBUCKET)})]})}),e.jsxs("p",{className:"mt-4 text-xs text-center text-muted-foreground","data-testid":"auth-modal-terms-of-service",children:[a(r.AUTH$BY_SIGNING_UP_YOU_AGREE_TO_OUR)," ",e.jsx("a",{href:"https://www.all-hands.dev/tos",target:"_blank",className:"underline hover:text-primary",rel:"noopener noreferrer",children:a(r.COMMON$TERMS_OF_SERVICE)})," ",a(r.COMMON$AND)," ",e.jsx("a",{href:"https://www.all-hands.dev/privacy",target:"_blank",className:"underline hover:text-primary",rel:"noopener noreferrer",children:a(r.COMMON$PRIVACY_POLICY)}),"."]})]})})}function Nt(){const{t}=m();return e.jsx(v,{children:e.jsxs(_,{className:"border border-tertiary",children:[e.jsx(k,{width:68,height:46}),e.jsx("div",{className:"flex flex-col gap-2 w-full items-center text-center",children:e.jsx("h1",{className:"text-2xl font-bold",children:t(r.AUTH$LOGGING_BACK_IN)})})]})})}function It({onClose:t}){const{t:n}=m(),{mutate:s}=Z(),a=async o=>{o.preventDefault();const c=new FormData(o.currentTarget).get("analytics")==="on";s({user_consents_to_analytics:c},{onSuccess:()=>{oe(c),t()}})};return e.jsx(v,{children:e.jsx("form",{"data-testid":"user-capture-consent-form",onSubmit:a,className:"flex flex-col gap-2",children:e.jsxs(_,{className:"border border-tertiary",children:[e.jsx($,{title:n(r.ANALYTICS$TITLE)}),e.jsx(Q,{children:n(r.ANALYTICS$DESCRIPTION)}),e.jsxs("label",{className:"flex gap-2 items-center self-start",children:[e.jsx("input",{name:"analytics",type:"checkbox",defaultChecked:!0}),n(r.ANALYTICS$SEND_ANONYMOUS_DATA)]}),e.jsx(I,{testId:"confirm-preferences",type:"submit",variant:"primary",className:"w-full",children:n(r.ANALYTICS$CONFIRM_PREFERENCES)})]})})})}const vt=()=>{const{mutate:t}=Z();return{migrateUserConsent:u.useCallback(async s=>{const a=localStorage.getItem("analytics-consent");a&&(s?.handleAnalyticsWasPresentInLocalStorage(),await t({user_consents_to_analytics:a==="true"},{onSuccess:()=>{oe(a==="true")}}),localStorage.removeItem("analytics-consent"))},[])}};function St(){const{t}=m(),{mutate:n,isPending:s}=G({mutationFn:j.createBillingSessionResponse,onSuccess:a=>{window.location.href=a},onError:()=>{ae(t(r.BILLING$ERROR_WHILE_CREATING_SESSION))}});return e.jsx(v,{children:e.jsxs(_,{className:"border border-tertiary",children:[e.jsx(k,{width:68,height:46}),e.jsxs("div",{className:"flex flex-col gap-2 w-full items-center text-center",children:[e.jsx("h1",{className:"text-2xl font-bold",children:t(r.BILLING$YOUVE_GOT_50)}),e.jsx("p",{children:e.jsx($e,{i18nKey:"BILLING$CLAIM_YOUR_50",components:{b:e.jsx("strong",{})}})})]}),e.jsx(I,{testId:"proceed-to-stripe-button",type:"submit",variant:"primary",className:"w-full",isDisabled:s,onClick:n,children:t(r.BILLING$PROCEED_TO_STRIPE)})]})})}const At=()=>{const{data:t,isLoading:n}=L(),{data:s,isLoading:a}=U(),o=De(),i=M({appMode:t?.APP_MODE||null,identityProvider:"github"}),c=M({appMode:t?.APP_MODE||null,identityProvider:"gitlab"}),f=M({appMode:t?.APP_MODE||null,identityProvider:"bitbucket"});d.useEffect(()=>{if(t?.APP_MODE!=="saas"||n||a||s||!o)return;let l=null;if(o===D.GITHUB?l=i:o===D.GITLAB?l=c:o===D.BITBUCKET&&(l=f),l){const p=new URL(l);p.searchParams.append("login_method",o),window.location.href=p.toString()}},[t?.APP_MODE,s,n,a,o,i,c,f])},_t=()=>{const t=b(),{data:n,isLoading:s}=U(),{data:a}=L(),o=P();d.useEffect(()=>{if(a?.APP_MODE!=="saas"||s||!n)return;const i=new URLSearchParams(t.search),c=i.get("login_method");if(Object.values(D).includes(c)){Pe(c),i.delete("login_method");const f=`${t.pathname}${i.toString()?`?${i.toString()}`:""}`;o(f,{replace:!0})}},[n,s,t.search,a?.APP_MODE])};function Tt({children:t}){const{data:n,isLoading:s}=Y(),a=P(),{pathname:o}=b();return u.useEffect(()=>{s||n?.EMAIL_VERIFIED===!1&&o!=="/settings/user"&&a("/settings/user",{replace:!0})},[n?.EMAIL_VERIFIED,o,a,s]),t}function Ot({startTime:t}){const{t:n}=m(),a=(o=>{try{let i;if(o.includes("T")&&(o.includes("-05:00")||o.includes("-04:00")||o.includes("EST")||o.includes("EDT")))i=new Date(o);else{const c=new Date(o);if(Number.isNaN(c.getTime()))throw new Error("Invalid date");i=c}return i.toLocaleString(void 0,{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",timeZoneName:"short"})}catch(i){return console.warn("Failed to parse maintenance time:",i),o}})(t);return e.jsx("div",{className:"bg-primary text-[#0D0F11] p-4 rounded",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(ke,{className:"text-white align-middle"})}),e.jsx("div",{className:"ml-3",children:e.jsx("p",{className:"text-sm font-medium",children:n("MAINTENANCE$SCHEDULED_MESSAGE",{time:a})})})]})})}const Ls=ue(function(){const n=fe(),{t:s}=m();return pe(n)?e.jsxs("div",{children:[e.jsx("h1",{children:n.status}),e.jsx("p",{children:n.statusText}),e.jsx("pre",{children:n.data instanceof Object?JSON.stringify(n.data):n.data})]}):n instanceof Error?e.jsxs("div",{children:[e.jsx("h1",{children:s(r.ERROR$GENERIC)}),e.jsx("pre",{children:n.message})]}):e.jsx("div",{children:e.jsx("h1",{children:s(r.ERROR$UNKNOWN)})})}),js=me(function(){const n=P(),{pathname:s}=b(),a=Ee(),{data:o}=Y(),{error:i}=Ue(),{migrateUserConsent:c}=vt(),{t:f}=m(),l=L(),{data:p,isFetching:C,isError:x}=U(),g=Fe({appMode:l.data?.APP_MODE||null,gitHubClientId:l.data?.GITHUB_CLIENT_ID||null}),N=a?null:g,[E,T]=u.useState(!1);At(),_t(),u.useEffect(()=>{!a&&o?.LANGUAGE&&Ge.changeLanguage(o.LANGUAGE)},[o?.LANGUAGE,a]),u.useEffect(()=>{if(!a){const O=o?.USER_CONSENTS_TO_ANALYTICS===null;T(O)}},[o,a]),u.useEffect(()=>{a||c({handleAnalyticsWasPresentInLocalStorage:()=>{T(!1)}})},[a]),u.useEffect(()=>{o?.IS_NEW_USER&&l.data?.APP_MODE==="saas"&&ne(f(r.BILLING$YOURE_IN))},[o?.IS_NEW_USER,l.data?.APP_MODE]),u.useEffect(()=>{!a&&i?.status===402&&s!=="/"&&n("/")},[i?.status,s,a]);const S=u.useCallback(()=>typeof window<"u"&&window.localStorage?localStorage.getItem(X.LOGIN_METHOD)!==null:!1,[]),[w,y]=u.useState(S());u.useEffect(()=>{const O=H=>{H.key===X.LOGIN_METHOD&&y(S())},R=()=>{y(S())};return window.addEventListener("storage",O),window.addEventListener("focus",R),()=>{window.removeEventListener("storage",O),window.removeEventListener("focus",R)}},[S]),u.useEffect(()=>{y(S())},[p,S]);const B=!p&&!x&&!C&&!a&&l.data?.APP_MODE==="saas"&&!w,F=!p&&!x&&!C&&!a&&l.data?.APP_MODE==="saas"&&w;return e.jsxs("div",{"data-testid":"root-layout",className:"bg-base p-3 h-screen lg:min-w-[1024px] flex flex-col md:flex-row gap-3",children:[e.jsx(pt,{}),e.jsxs("div",{id:"root-outlet",className:"h-[calc(100%-50px)] md:h-full w-full relative overflow-auto",children:[l.data?.MAINTENANCE&&e.jsx(Ot,{startTime:l.data.MAINTENANCE.startTime}),e.jsx(Tt,{children:e.jsx(he,{})})]}),B&&e.jsx(gt,{githubAuthUrl:N,appMode:l.data?.APP_MODE,providersConfigured:l.data?.PROVIDERS_CONFIGURED}),F&&e.jsx(Nt,{}),l.data?.APP_MODE==="oss"&&E&&e.jsx(It,{onClose:()=>{T(!1)}}),l.data?.FEATURE_FLAGS.ENABLE_BILLING&&l.data?.APP_MODE==="saas"&&o?.IS_NEW_USER&&e.jsx(St,{})]})});export{Ls as ErrorBoundary,js as default};
