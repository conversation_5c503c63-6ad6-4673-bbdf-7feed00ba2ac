window.__reactRouterManifest={"entry":{"module":"/assets/entry.client-VFLPI5E5.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/index-yKbcr7Pf.js","/assets/react-redux-B5osdedR.js","/assets/module-5laXsVNO.js","/assets/index-DbrbOxWj.js","/assets/open-hands-axios-CtirLpss.js","/assets/store-Bya9Reqe.js","/assets/open-hands-Ce72Fmtl.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/query-client-config-CJn-5u6A.js","/assets/i18next-CO45VQzB.js","/assets/preload-helper-BXl3LOEh.js","/assets/i18nInstance-DBIXdvxg.js","/assets/browser-slice-DabBaamq.js","/assets/agent-state-CFaY3go2.js","/assets/index-cxP66Ws3.js","/assets/declaration-xyc84-tJ.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/mutation-B9dSlWD-.js"],"css":[]},"routes":{"root":{"id":"root","path":"","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/root-CCJzRLNc.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/index-yKbcr7Pf.js","/assets/react-redux-B5osdedR.js","/assets/module-5laXsVNO.js","/assets/index-DbrbOxWj.js","/assets/open-hands-axios-CtirLpss.js","/assets/store-Bya9Reqe.js","/assets/open-hands-Ce72Fmtl.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/query-client-config-CJn-5u6A.js","/assets/i18next-CO45VQzB.js","/assets/preload-helper-BXl3LOEh.js","/assets/i18nInstance-DBIXdvxg.js","/assets/browser-slice-DabBaamq.js","/assets/agent-state-CFaY3go2.js","/assets/index-cxP66Ws3.js","/assets/declaration-xyc84-tJ.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/mutation-B9dSlWD-.js"],"css":["/assets/root-CPcSsqtz.css"]},"routes/root-layout":{"id":"routes/root-layout","parentId":"root","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":true,"module":"/assets/root-layout-MbIUQXp1.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/declaration-xyc84-tJ.js","/assets/index-DbrbOxWj.js","/assets/use-settings-CSlhfPqo.js","/assets/use-config-jdwF3W4-.js","/assets/useQuery-Cu2nkJ8V.js","/assets/module-5laXsVNO.js","/assets/open-hands-Ce72Fmtl.js","/assets/use-user-providers-CVWOd-tS.js","/assets/loading-spinner-D6qcLhqr.js","/assets/utils-KsbccAr1.js","/assets/useTranslation-BG59QWH_.js","/assets/tooltip-button-Bd3YrFXr.js","/assets/conversation-card-BnTfWkKs.js","/assets/all-hands-logo-D_ipIZjH.js","/assets/plus-VpD79E1k.js","/assets/settings-BJuJgO6o.js","/assets/index-Do49u1Ze.js","/assets/help-link-CHYsvCwj.js","/assets/modal-backdrop-ve4Sk5I2.js","/assets/settings-utils-C--CGkWg.js","/assets/brand-button-3Z8FN4qR.js","/assets/key-status-icon-DbpY1ViG.js","/assets/settings-input-i4i_IemM.js","/assets/use-save-settings-Bb4p0cGE.js","/assets/open-hands-axios-CtirLpss.js","/assets/constants-DCYeMzG2.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/index-yKbcr7Pf.js","/assets/use-logout-Bpg8Vogf.js","/assets/handle-capture-consent-L0_Qc6gK.js","/assets/use-balance-BpOxWBAr.js","/assets/Trans-CI4prNur.js","/assets/index-DvLMSsrd.js","/assets/i18next-CO45VQzB.js","/assets/preload-helper-BXl3LOEh.js","/assets/i18nInstance-DBIXdvxg.js","/assets/chunk-BOOVDPB6-Dt9AQnxs.js","/assets/chunk-S6H5EOGR-Bwn62IP6.js","/assets/react-redux-B5osdedR.js","/assets/use-conversation-id-0JHAicdF.js","/assets/agent-state-CFaY3go2.js","/assets/vscode-url-helper-5Tt6LlE2.js","/assets/ws-client-provider-Dmsj8lkD.js","/assets/store-Bya9Reqe.js","/assets/browser-slice-DabBaamq.js","/assets/query-client-config-CJn-5u6A.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/mutation-B9dSlWD-.js","/assets/use-active-conversation-B8Aw3kE2.js","/assets/use-optimistic-user-message-tdysaQ5t.js","/assets/iconBase-2PDVWRGH.js","/assets/map-provider-g8SgAMsv.js","/assets/optional-tag-e1gRgM9y.js","/assets/index-cxP66Ws3.js"],"css":[]},"routes/home":{"id":"routes/home","parentId":"routes/root-layout","index":true,"hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/home-BFU5UmRD.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/use-create-conversation-IQqGjJUl.js","/assets/open-hands-axios-CtirLpss.js","/assets/brand-button-3Z8FN4qR.js","/assets/useTranslation-BG59QWH_.js","/assets/use-settings-CSlhfPqo.js","/assets/branch-error-state-BSd8PBsv.js","/assets/useQuery-Cu2nkJ8V.js","/assets/open-hands-Ce72Fmtl.js","/assets/settings-dropdown-input-Did5iUTK.js","/assets/declaration-xyc84-tJ.js","/assets/chunk-S6H5EOGR-Bwn62IP6.js","/assets/use-config-jdwF3W4-.js","/assets/use-user-providers-CVWOd-tS.js","/assets/index-Do49u1Ze.js","/assets/index-DvLMSsrd.js","/assets/utils-KsbccAr1.js","/assets/use-optimistic-user-message-tdysaQ5t.js","/assets/tooltip-button-Bd3YrFXr.js","/assets/module-5laXsVNO.js","/assets/mutation-B9dSlWD-.js","/assets/i18nInstance-DBIXdvxg.js","/assets/optional-tag-e1gRgM9y.js","/assets/index-yKbcr7Pf.js","/assets/preload-helper-BXl3LOEh.js","/assets/iconBase-2PDVWRGH.js","/assets/chunk-BOOVDPB6-Dt9AQnxs.js"],"css":[]},"routes/accept-tos":{"id":"routes/accept-tos","parentId":"routes/root-layout","path":"accept-tos","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/accept-tos-CnHLR_9C.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/brand-button-3Z8FN4qR.js","/assets/declaration-xyc84-tJ.js","/assets/all-hands-logo-D_ipIZjH.js","/assets/useTranslation-BG59QWH_.js","/assets/handle-capture-consent-L0_Qc6gK.js","/assets/open-hands-axios-CtirLpss.js","/assets/mutation-B9dSlWD-.js","/assets/utils-KsbccAr1.js","/assets/i18nInstance-DBIXdvxg.js","/assets/module-5laXsVNO.js"],"css":[]},"routes/settings":{"id":"routes/settings","parentId":"routes/root-layout","path":"settings","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":true,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/settings-CDjZSuLa.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/settings-BJuJgO6o.js","/assets/utils-KsbccAr1.js","/assets/use-config-jdwF3W4-.js","/assets/declaration-xyc84-tJ.js","/assets/open-hands-Ce72Fmtl.js","/assets/query-client-config-CJn-5u6A.js","/assets/useTranslation-BG59QWH_.js","/assets/useQuery-Cu2nkJ8V.js","/assets/open-hands-axios-CtirLpss.js","/assets/i18next-CO45VQzB.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/index-cxP66Ws3.js","/assets/mutation-B9dSlWD-.js","/assets/i18nInstance-DBIXdvxg.js"],"css":[]},"routes/llm-settings":{"id":"routes/llm-settings","parentId":"routes/settings","index":true,"hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/llm-settings-CO-V84Lg.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/help-link-CHYsvCwj.js","/assets/use-settings-CSlhfPqo.js","/assets/use-save-settings-Bb4p0cGE.js","/assets/switch-skeleton-BbiAGd10.js","/assets/declaration-xyc84-tJ.js","/assets/settings-input-i4i_IemM.js","/assets/brand-button-3Z8FN4qR.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/settings-dropdown-input-Did5iUTK.js","/assets/use-config-jdwF3W4-.js","/assets/input-skeleton-FOAHYnV9.js","/assets/subtext-skeleton-DyZMS3uA.js","/assets/key-status-icon-DbpY1ViG.js","/assets/map-provider-g8SgAMsv.js","/assets/useTranslation-BG59QWH_.js","/assets/useQuery-Cu2nkJ8V.js","/assets/open-hands-axios-CtirLpss.js","/assets/open-hands-Ce72Fmtl.js","/assets/chunk-S6H5EOGR-Bwn62IP6.js","/assets/utils-KsbccAr1.js","/assets/index-yKbcr7Pf.js","/assets/preload-helper-BXl3LOEh.js","/assets/module-5laXsVNO.js","/assets/optional-tag-e1gRgM9y.js","/assets/mutation-B9dSlWD-.js","/assets/index-cxP66Ws3.js","/assets/i18nInstance-DBIXdvxg.js"],"css":[]},"routes/mcp-settings":{"id":"routes/mcp-settings","parentId":"routes/settings","path":"mcp","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/mcp-settings-T3Cjkic9.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/module-5laXsVNO.js","/assets/use-settings-CSlhfPqo.js","/assets/use-save-settings-Bb4p0cGE.js","/assets/declaration-xyc84-tJ.js","/assets/useTranslation-BG59QWH_.js","/assets/brand-button-3Z8FN4qR.js","/assets/utils-KsbccAr1.js","/assets/Trans-CI4prNur.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/useQuery-Cu2nkJ8V.js","/assets/open-hands-axios-CtirLpss.js","/assets/open-hands-Ce72Fmtl.js","/assets/use-config-jdwF3W4-.js","/assets/i18nInstance-DBIXdvxg.js","/assets/mutation-B9dSlWD-.js","/assets/index-cxP66Ws3.js"],"css":[]},"routes/user-settings":{"id":"routes/user-settings","parentId":"routes/settings","path":"user","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/user-settings-DJp_1ZPr.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/open-hands-axios-CtirLpss.js","/assets/use-settings-CSlhfPqo.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/useTranslation-BG59QWH_.js","/assets/useQuery-Cu2nkJ8V.js","/assets/open-hands-Ce72Fmtl.js","/assets/module-5laXsVNO.js","/assets/use-config-jdwF3W4-.js","/assets/index-cxP66Ws3.js","/assets/i18nInstance-DBIXdvxg.js"],"css":[]},"routes/git-settings":{"id":"routes/git-settings","parentId":"routes/settings","path":"integrations","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/git-settings-CCvXJ9za.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/use-config-jdwF3W4-.js","/assets/use-settings-CSlhfPqo.js","/assets/brand-button-3Z8FN4qR.js","/assets/use-logout-Bpg8Vogf.js","/assets/declaration-xyc84-tJ.js","/assets/settings-input-i4i_IemM.js","/assets/useTranslation-BG59QWH_.js","/assets/Trans-CI4prNur.js","/assets/key-status-icon-DbpY1ViG.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/input-skeleton-FOAHYnV9.js","/assets/subtext-skeleton-DyZMS3uA.js","/assets/open-hands-axios-CtirLpss.js","/assets/secrets-service-CrMMoq9G.js","/assets/use-user-providers-CVWOd-tS.js","/assets/useQuery-Cu2nkJ8V.js","/assets/open-hands-Ce72Fmtl.js","/assets/module-5laXsVNO.js","/assets/mutation-B9dSlWD-.js","/assets/utils-KsbccAr1.js","/assets/optional-tag-e1gRgM9y.js","/assets/i18nInstance-DBIXdvxg.js","/assets/index-cxP66Ws3.js"],"css":[]},"routes/app-settings":{"id":"routes/app-settings","parentId":"routes/settings","path":"app","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/app-settings-BJ-GWmF6.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/use-save-settings-Bb4p0cGE.js","/assets/use-settings-CSlhfPqo.js","/assets/index-DbrbOxWj.js","/assets/brand-button-3Z8FN4qR.js","/assets/switch-skeleton-BbiAGd10.js","/assets/settings-input-i4i_IemM.js","/assets/declaration-xyc84-tJ.js","/assets/settings-dropdown-input-Did5iUTK.js","/assets/useTranslation-BG59QWH_.js","/assets/handle-capture-consent-L0_Qc6gK.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/input-skeleton-FOAHYnV9.js","/assets/use-config-jdwF3W4-.js","/assets/settings-utils-C--CGkWg.js","/assets/open-hands-axios-CtirLpss.js","/assets/module-5laXsVNO.js","/assets/open-hands-Ce72Fmtl.js","/assets/useQuery-Cu2nkJ8V.js","/assets/i18next-CO45VQzB.js","/assets/preload-helper-BXl3LOEh.js","/assets/i18nInstance-DBIXdvxg.js","/assets/mutation-B9dSlWD-.js","/assets/utils-KsbccAr1.js","/assets/optional-tag-e1gRgM9y.js","/assets/chunk-S6H5EOGR-Bwn62IP6.js","/assets/index-yKbcr7Pf.js","/assets/index-cxP66Ws3.js","/assets/map-provider-g8SgAMsv.js"],"css":[]},"routes/billing":{"id":"routes/billing","parentId":"routes/settings","path":"billing","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/billing-D2WTnrSJ.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/brand-button-3Z8FN4qR.js","/assets/open-hands-Ce72Fmtl.js","/assets/use-balance-BpOxWBAr.js","/assets/utils-KsbccAr1.js","/assets/settings-input-i4i_IemM.js","/assets/loading-spinner-D6qcLhqr.js","/assets/declaration-xyc84-tJ.js","/assets/useTranslation-BG59QWH_.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/mutation-B9dSlWD-.js","/assets/open-hands-axios-CtirLpss.js","/assets/useQuery-Cu2nkJ8V.js","/assets/use-config-jdwF3W4-.js","/assets/optional-tag-e1gRgM9y.js","/assets/i18nInstance-DBIXdvxg.js","/assets/index-cxP66Ws3.js"],"css":[]},"routes/secrets-settings":{"id":"routes/secrets-settings","parentId":"routes/settings","path":"secrets","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/secrets-settings-Cvu5GlJ-.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/open-hands-axios-CtirLpss.js","/assets/useQuery-Cu2nkJ8V.js","/assets/secrets-service-CrMMoq9G.js","/assets/use-user-providers-CVWOd-tS.js","/assets/use-config-jdwF3W4-.js","/assets/brand-button-3Z8FN4qR.js","/assets/declaration-xyc84-tJ.js","/assets/settings-input-i4i_IemM.js","/assets/utils-KsbccAr1.js","/assets/optional-tag-e1gRgM9y.js","/assets/useTranslation-BG59QWH_.js","/assets/index-DvLMSsrd.js","/assets/modal-backdrop-ve4Sk5I2.js","/assets/open-hands-Ce72Fmtl.js","/assets/use-settings-CSlhfPqo.js","/assets/module-5laXsVNO.js","/assets/mutation-B9dSlWD-.js","/assets/i18nInstance-DBIXdvxg.js","/assets/iconBase-2PDVWRGH.js"],"css":[]},"routes/api-keys":{"id":"routes/api-keys","parentId":"routes/settings","path":"api-keys","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/api-keys-BEsi9C3T.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/index-DvLMSsrd.js","/assets/declaration-xyc84-tJ.js","/assets/brand-button-3Z8FN4qR.js","/assets/loading-spinner-D6qcLhqr.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/settings-input-i4i_IemM.js","/assets/modal-backdrop-ve4Sk5I2.js","/assets/open-hands-axios-CtirLpss.js","/assets/useQuery-Cu2nkJ8V.js","/assets/use-config-jdwF3W4-.js","/assets/useTranslation-BG59QWH_.js","/assets/Trans-CI4prNur.js","/assets/iconBase-2PDVWRGH.js","/assets/mutation-B9dSlWD-.js","/assets/utils-KsbccAr1.js","/assets/index-cxP66Ws3.js","/assets/optional-tag-e1gRgM9y.js","/assets/open-hands-Ce72Fmtl.js","/assets/i18nInstance-DBIXdvxg.js"],"css":[]},"routes/conversation":{"id":"routes/conversation","parentId":"routes/root-layout","path":"conversations/:conversationId","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/conversation-DPgPCErq.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/react-redux-B5osdedR.js","/assets/use-conversation-id-0JHAicdF.js","/assets/declaration-xyc84-tJ.js","/assets/event-handler-CNbFJRld.js","/assets/agent-state-CFaY3go2.js","/assets/ws-client-provider-Dmsj8lkD.js","/assets/chunk-BOOVDPB6-Dt9AQnxs.js","/assets/useTranslation-BG59QWH_.js","/assets/use-settings-CSlhfPqo.js","/assets/use-active-conversation-B8Aw3kE2.js","/assets/iconBase-2PDVWRGH.js","/assets/conversation-card-BnTfWkKs.js","/assets/store-Bya9Reqe.js","/assets/module-5laXsVNO.js","/assets/paragraph-D4ROHliG.js","/assets/utils-KsbccAr1.js","/assets/scroll-to-bottom-button-DXWtkqaP.js","/assets/index-Do49u1Ze.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/modal-backdrop-ve4Sk5I2.js","/assets/constants-DCYeMzG2.js","/assets/index-cxP66Ws3.js","/assets/brand-button-3Z8FN4qR.js","/assets/open-hands-Ce72Fmtl.js","/assets/index-yKbcr7Pf.js","/assets/index-DbrbOxWj.js","/assets/i18next-CO45VQzB.js","/assets/Trans-CI4prNur.js","/assets/chunk-S6H5EOGR-Bwn62IP6.js","/assets/open-hands-axios-CtirLpss.js","/assets/use-config-jdwF3W4-.js","/assets/useQuery-Cu2nkJ8V.js","/assets/use-optimistic-user-message-tdysaQ5t.js","/assets/index-DvLMSsrd.js","/assets/settings-dropdown-input-Did5iUTK.js","/assets/use-user-providers-CVWOd-tS.js","/assets/loading-spinner-D6qcLhqr.js","/assets/use-runtime-is-ready-CsTtc0dU.js","/assets/index-C8v-0ELK.js","/assets/use-active-host-CByqDCZz.js","/assets/preload-helper-BXl3LOEh.js","/assets/vscode-url-helper-5Tt6LlE2.js","/assets/use-create-conversation-IQqGjJUl.js","/assets/browser-slice-DabBaamq.js","/assets/query-client-config-CJn-5u6A.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/mutation-B9dSlWD-.js","/assets/i18nInstance-DBIXdvxg.js","/assets/optional-tag-e1gRgM9y.js"],"css":[]},"routes/changes-tab":{"id":"routes/changes-tab","parentId":"routes/conversation","index":true,"hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/changes-tab-DVVv3J2U.js","imports":["/assets/changes-tab-D76KUg73.js","/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/react-redux-B5osdedR.js","/assets/index-C8v-0ELK.js","/assets/iconBase-2PDVWRGH.js","/assets/utils-KsbccAr1.js","/assets/useQuery-Cu2nkJ8V.js","/assets/open-hands-axios-CtirLpss.js","/assets/open-hands-Ce72Fmtl.js","/assets/use-conversation-id-0JHAicdF.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/use-runtime-is-ready-CsTtc0dU.js","/assets/agent-state-CFaY3go2.js","/assets/use-active-conversation-B8Aw3kE2.js","/assets/declaration-xyc84-tJ.js","/assets/useTranslation-BG59QWH_.js","/assets/i18nInstance-DBIXdvxg.js"],"css":[]},"routes/browser-tab":{"id":"routes/browser-tab","parentId":"routes/conversation","path":"browser","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/browser-tab-Dp340Bhj.js","imports":["/assets/browser-tab-DQj3XIBz.js","/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/react-redux-B5osdedR.js","/assets/declaration-xyc84-tJ.js","/assets/useTranslation-BG59QWH_.js","/assets/i18nInstance-DBIXdvxg.js","/assets/iconBase-2PDVWRGH.js","/assets/use-conversation-id-0JHAicdF.js","/assets/browser-slice-DabBaamq.js"],"css":[]},"routes/jupyter-tab":{"id":"routes/jupyter-tab","parentId":"routes/conversation","path":"jupyter","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/jupyter-tab-CGT2ublw.js","imports":["/assets/jupyter-tab-BN0K47b7.js","/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/react-redux-B5osdedR.js","/assets/scroll-to-bottom-button-DXWtkqaP.js","/assets/paragraph-D4ROHliG.js","/assets/declaration-xyc84-tJ.js","/assets/useTranslation-BG59QWH_.js","/assets/i18nInstance-DBIXdvxg.js","/assets/agent-state-CFaY3go2.js"],"css":[]},"routes/served-tab":{"id":"routes/served-tab","parentId":"routes/conversation","path":"served","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/served-tab-fQZil9b6.js","imports":["/assets/served-tab-jFMthLLa.js","/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/index-DvLMSsrd.js","/assets/iconBase-2PDVWRGH.js","/assets/index-Do49u1Ze.js","/assets/use-active-host-CByqDCZz.js","/assets/useQuery-Cu2nkJ8V.js","/assets/open-hands-axios-CtirLpss.js","/assets/open-hands-Ce72Fmtl.js","/assets/use-conversation-id-0JHAicdF.js","/assets/use-runtime-is-ready-CsTtc0dU.js","/assets/react-redux-B5osdedR.js","/assets/agent-state-CFaY3go2.js","/assets/use-active-conversation-B8Aw3kE2.js","/assets/declaration-xyc84-tJ.js","/assets/useTranslation-BG59QWH_.js","/assets/i18nInstance-DBIXdvxg.js"],"css":[]},"routes/terminal-tab":{"id":"routes/terminal-tab","parentId":"routes/conversation","path":"terminal","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/terminal-tab-DInAQZge.js","imports":["/assets/terminal-tab-Ea_WHo_u.js","/assets/preload-helper-BXl3LOEh.js","/assets/chunk-C37GKA54-CBbYr_fP.js"],"css":[]},"routes/vscode-tab":{"id":"routes/vscode-tab","parentId":"routes/conversation","path":"vscode","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":false,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/vscode-tab-DeFLWBXc.js","imports":["/assets/vscode-tab-UrEftLrh.js","/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/react-redux-B5osdedR.js","/assets/declaration-xyc84-tJ.js","/assets/agent-state-CFaY3go2.js","/assets/useQuery-Cu2nkJ8V.js","/assets/open-hands-axios-CtirLpss.js","/assets/open-hands-Ce72Fmtl.js","/assets/use-conversation-id-0JHAicdF.js","/assets/vscode-url-helper-5Tt6LlE2.js","/assets/use-runtime-is-ready-CsTtc0dU.js","/assets/use-active-conversation-B8Aw3kE2.js","/assets/useTranslation-BG59QWH_.js","/assets/i18nInstance-DBIXdvxg.js"],"css":[]},"routes/microagent-management":{"id":"routes/microagent-management","parentId":"routes/root-layout","path":"microagent-management","hasAction":false,"hasLoader":false,"hasClientAction":false,"hasClientLoader":true,"hasClientMiddleware":false,"hasErrorBoundary":false,"module":"/assets/microagent-management-BHWYHcyp.js","imports":["/assets/chunk-C37GKA54-CBbYr_fP.js","/assets/query-client-config-CJn-5u6A.js","/assets/open-hands-Ce72Fmtl.js","/assets/react-redux-B5osdedR.js","/assets/declaration-xyc84-tJ.js","/assets/constants-DCYeMzG2.js","/assets/useTranslation-BG59QWH_.js","/assets/store-Bya9Reqe.js","/assets/utils-KsbccAr1.js","/assets/useQuery-Cu2nkJ8V.js","/assets/chunk-S6H5EOGR-Bwn62IP6.js","/assets/index-DvLMSsrd.js","/assets/plus-VpD79E1k.js","/assets/tooltip-button-Bd3YrFXr.js","/assets/branch-error-state-BSd8PBsv.js","/assets/preload-helper-BXl3LOEh.js","/assets/features-animation-CM7oex5Y.js","/assets/chunk-BOOVDPB6-Dt9AQnxs.js","/assets/brand-button-3Z8FN4qR.js","/assets/event-handler-CNbFJRld.js","/assets/paragraph-D4ROHliG.js","/assets/modal-backdrop-ve4Sk5I2.js","/assets/agent-state-CFaY3go2.js","/assets/ws-client-provider-Dmsj8lkD.js","/assets/i18next-CO45VQzB.js","/assets/retrieve-axios-error-message-CYr77e_f.js","/assets/custom-toast-handlers-CR9P-jKI.js","/assets/index-cxP66Ws3.js","/assets/open-hands-axios-CtirLpss.js","/assets/mutation-B9dSlWD-.js","/assets/i18nInstance-DBIXdvxg.js","/assets/browser-slice-DabBaamq.js","/assets/index-yKbcr7Pf.js","/assets/iconBase-2PDVWRGH.js","/assets/settings-dropdown-input-Did5iUTK.js","/assets/optional-tag-e1gRgM9y.js","/assets/use-create-conversation-IQqGjJUl.js","/assets/module-5laXsVNO.js","/assets/use-user-providers-CVWOd-tS.js","/assets/use-settings-CSlhfPqo.js","/assets/use-config-jdwF3W4-.js","/assets/index-Do49u1Ze.js","/assets/use-active-conversation-B8Aw3kE2.js","/assets/use-conversation-id-0JHAicdF.js","/assets/use-optimistic-user-message-tdysaQ5t.js"],"css":[]}},"url":"/assets/manifest-e839cd4d.js","version":"e839cd4d"};