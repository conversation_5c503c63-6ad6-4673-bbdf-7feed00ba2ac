import {
  require_react
} from "./chunk-BQYK6RGN.js";
import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// node_modules/@microlink/react-json-view/dist/main.js
var require_main = __commonJS({
  "node_modules/@microlink/react-json-view/dist/main.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "object" == typeof module ? module.exports = t(require_react()) : "function" == typeof define && define.amd ? define(["react"], t) : "object" == typeof exports ? exports.reactJsonView = t(require_react()) : e.reactJsonView = t(e.React);
    }(exports, (e) => (() => {
      var t = { 9735: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "apathy", author: "jann<PERSON> sieb<PERSON> (https://github.com/janniks)", base00: "#031A16", base01: "#0B342D", base02: "#184E45", base03: "#2B685E", base04: "#5F9C92", base05: "#81B5AC", base06: "#A7CEC8", base07: "#D2E7E4", base08: "#3E9688", base09: "#3E7996", base0A: "#3E4C96", base0B: "#883E96", base0C: "#963E4C", base0D: "#96883E", base0E: "#4C963E", base0F: "#3E965B" }, e2.exports = t2.default;
      }, 294: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "ashes", author: "jannik siebert (https://github.com/janniks)", base00: "#1C2023", base01: "#393F45", base02: "#565E65", base03: "#747C84", base04: "#ADB3BA", base05: "#C7CCD1", base06: "#DFE2E5", base07: "#F3F4F5", base08: "#C7AE95", base09: "#C7C795", base0A: "#AEC795", base0B: "#95C7AE", base0C: "#95AEC7", base0D: "#AE95C7", base0E: "#C795AE", base0F: "#C79595" }, e2.exports = t2.default;
      }, 1733: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "atelier dune", author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/dune)", base00: "#20201d", base01: "#292824", base02: "#6e6b5e", base03: "#7d7a68", base04: "#999580", base05: "#a6a28c", base06: "#e8e4cf", base07: "#fefbec", base08: "#d73737", base09: "#b65611", base0A: "#cfb017", base0B: "#60ac39", base0C: "#1fad83", base0D: "#6684e1", base0E: "#b854d4", base0F: "#d43552" }, e2.exports = t2.default;
      }, 8974: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "atelier forest", author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/forest)", base00: "#1b1918", base01: "#2c2421", base02: "#68615e", base03: "#766e6b", base04: "#9c9491", base05: "#a8a19f", base06: "#e6e2e0", base07: "#f1efee", base08: "#f22c40", base09: "#df5320", base0A: "#d5911a", base0B: "#5ab738", base0C: "#00ad9c", base0D: "#407ee7", base0E: "#6666ea", base0F: "#c33ff3" }, e2.exports = t2.default;
      }, 6933: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "atelier heath", author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/heath)", base00: "#1b181b", base01: "#292329", base02: "#695d69", base03: "#776977", base04: "#9e8f9e", base05: "#ab9bab", base06: "#d8cad8", base07: "#f7f3f7", base08: "#ca402b", base09: "#a65926", base0A: "#bb8a35", base0B: "#379a37", base0C: "#159393", base0D: "#516aec", base0E: "#7b59c0", base0F: "#cc33cc" }, e2.exports = t2.default;
      }, 523: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "atelier lakeside", author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/lakeside/)", base00: "#161b1d", base01: "#1f292e", base02: "#516d7b", base03: "#5a7b8c", base04: "#7195a8", base05: "#7ea2b4", base06: "#c1e4f6", base07: "#ebf8ff", base08: "#d22d72", base09: "#935c25", base0A: "#8a8a0f", base0B: "#568c3b", base0C: "#2d8f6f", base0D: "#257fad", base0E: "#5d5db1", base0F: "#b72dd2" }, e2.exports = t2.default;
      }, 1223: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "atelier seaside", author: "bram de haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/seaside/)", base00: "#131513", base01: "#242924", base02: "#5e6e5e", base03: "#687d68", base04: "#809980", base05: "#8ca68c", base06: "#cfe8cf", base07: "#f0fff0", base08: "#e6193c", base09: "#87711d", base0A: "#c3c322", base0B: "#29a329", base0C: "#1999b3", base0D: "#3d62f5", base0E: "#ad2bee", base0F: "#e619c3" }, e2.exports = t2.default;
      }, 1233: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "bespin", author: "jan t. sott", base00: "#28211c", base01: "#36312e", base02: "#5e5d5c", base03: "#666666", base04: "#797977", base05: "#8a8986", base06: "#9d9b97", base07: "#baae9e", base08: "#cf6a4c", base09: "#cf7d34", base0A: "#f9ee98", base0B: "#54be0d", base0C: "#afc4db", base0D: "#5ea6ea", base0E: "#9b859d", base0F: "#937121" }, e2.exports = t2.default;
      }, 2847: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "brewer", author: "timothée poisot (http://github.com/tpoisot)", base00: "#0c0d0e", base01: "#2e2f30", base02: "#515253", base03: "#737475", base04: "#959697", base05: "#b7b8b9", base06: "#dadbdc", base07: "#fcfdfe", base08: "#e31a1c", base09: "#e6550d", base0A: "#dca060", base0B: "#31a354", base0C: "#80b1d3", base0D: "#3182bd", base0E: "#756bb1", base0F: "#b15928" }, e2.exports = t2.default;
      }, 8120: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "bright", author: "chris kempson (http://chriskempson.com)", base00: "#000000", base01: "#303030", base02: "#505050", base03: "#b0b0b0", base04: "#d0d0d0", base05: "#e0e0e0", base06: "#f5f5f5", base07: "#ffffff", base08: "#fb0120", base09: "#fc6d24", base0A: "#fda331", base0B: "#a1c659", base0C: "#76c7b7", base0D: "#6fb3d2", base0E: "#d381c3", base0F: "#be643c" }, e2.exports = t2.default;
      }, 6305: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "chalk", author: "chris kempson (http://chriskempson.com)", base00: "#151515", base01: "#202020", base02: "#303030", base03: "#505050", base04: "#b0b0b0", base05: "#d0d0d0", base06: "#e0e0e0", base07: "#f5f5f5", base08: "#fb9fb1", base09: "#eda987", base0A: "#ddb26f", base0B: "#acc267", base0C: "#12cfc0", base0D: "#6fc2ef", base0E: "#e1a3ee", base0F: "#deaf8f" }, e2.exports = t2.default;
      }, 525: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "codeschool", author: "brettof86", base00: "#232c31", base01: "#1c3657", base02: "#2a343a", base03: "#3f4944", base04: "#84898c", base05: "#9ea7a6", base06: "#a7cfa3", base07: "#b5d8f6", base08: "#2a5491", base09: "#43820d", base0A: "#a03b1e", base0B: "#237986", base0C: "#b02f30", base0D: "#484d79", base0E: "#c59820", base0F: "#c98344" }, e2.exports = t2.default;
      }, 4124: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "colors", author: "mrmrs (http://clrs.cc)", base00: "#111111", base01: "#333333", base02: "#555555", base03: "#777777", base04: "#999999", base05: "#bbbbbb", base06: "#dddddd", base07: "#ffffff", base08: "#ff4136", base09: "#ff851b", base0A: "#ffdc00", base0B: "#2ecc40", base0C: "#7fdbff", base0D: "#0074d9", base0E: "#b10dc9", base0F: "#85144b" }, e2.exports = t2.default;
      }, 7167: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "default", author: "chris kempson (http://chriskempson.com)", base00: "#181818", base01: "#282828", base02: "#383838", base03: "#585858", base04: "#b8b8b8", base05: "#d8d8d8", base06: "#e8e8e8", base07: "#f8f8f8", base08: "#ab4642", base09: "#dc9656", base0A: "#f7ca88", base0B: "#a1b56c", base0C: "#86c1b9", base0D: "#7cafc2", base0E: "#ba8baf", base0F: "#a16946" }, e2.exports = t2.default;
      }, 4582: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "eighties", author: "chris kempson (http://chriskempson.com)", base00: "#2d2d2d", base01: "#393939", base02: "#515151", base03: "#747369", base04: "#a09f93", base05: "#d3d0c8", base06: "#e8e6df", base07: "#f2f0ec", base08: "#f2777a", base09: "#f99157", base0A: "#ffcc66", base0B: "#99cc99", base0C: "#66cccc", base0D: "#6699cc", base0E: "#cc99cc", base0F: "#d27b53" }, e2.exports = t2.default;
      }, 7096: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "embers", author: "jannik siebert (https://github.com/janniks)", base00: "#16130F", base01: "#2C2620", base02: "#433B32", base03: "#5A5047", base04: "#8A8075", base05: "#A39A90", base06: "#BEB6AE", base07: "#DBD6D1", base08: "#826D57", base09: "#828257", base0A: "#6D8257", base0B: "#57826D", base0C: "#576D82", base0D: "#6D5782", base0E: "#82576D", base0F: "#825757" }, e2.exports = t2.default;
      }, 9887: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "flat", author: "chris kempson (http://chriskempson.com)", base00: "#2C3E50", base01: "#34495E", base02: "#7F8C8D", base03: "#95A5A6", base04: "#BDC3C7", base05: "#e0e0e0", base06: "#f5f5f5", base07: "#ECF0F1", base08: "#E74C3C", base09: "#E67E22", base0A: "#F1C40F", base0B: "#2ECC71", base0C: "#1ABC9C", base0D: "#3498DB", base0E: "#9B59B6", base0F: "#be643c" }, e2.exports = t2.default;
      }, 7199: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "google", author: "seth wright (http://sethawright.com)", base00: "#1d1f21", base01: "#282a2e", base02: "#373b41", base03: "#969896", base04: "#b4b7b4", base05: "#c5c8c6", base06: "#e0e0e0", base07: "#ffffff", base08: "#CC342B", base09: "#F96A38", base0A: "#FBA922", base0B: "#198844", base0C: "#3971ED", base0D: "#3971ED", base0E: "#A36AC7", base0F: "#3971ED" }, e2.exports = t2.default;
      }, 1985: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "grayscale", author: "alexandre gavioli (https://github.com/alexx2/)", base00: "#101010", base01: "#252525", base02: "#464646", base03: "#525252", base04: "#ababab", base05: "#b9b9b9", base06: "#e3e3e3", base07: "#f7f7f7", base08: "#7c7c7c", base09: "#999999", base0A: "#a0a0a0", base0B: "#8e8e8e", base0C: "#868686", base0D: "#686868", base0E: "#747474", base0F: "#5e5e5e" }, e2.exports = t2.default;
      }, 8093: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "green screen", author: "chris kempson (http://chriskempson.com)", base00: "#001100", base01: "#003300", base02: "#005500", base03: "#007700", base04: "#009900", base05: "#00bb00", base06: "#00dd00", base07: "#00ff00", base08: "#007700", base09: "#009900", base0A: "#007700", base0B: "#00bb00", base0C: "#005500", base0D: "#009900", base0E: "#00bb00", base0F: "#005500" }, e2.exports = t2.default;
      }, 1615: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "harmonic16", author: "jannik siebert (https://github.com/janniks)", base00: "#0b1c2c", base01: "#223b54", base02: "#405c79", base03: "#627e99", base04: "#aabcce", base05: "#cbd6e2", base06: "#e5ebf1", base07: "#f7f9fb", base08: "#bf8b56", base09: "#bfbf56", base0A: "#8bbf56", base0B: "#56bf8b", base0C: "#568bbf", base0D: "#8b56bf", base0E: "#bf568b", base0F: "#bf5656" }, e2.exports = t2.default;
      }, 9063: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "hopscotch", author: "jan t. sott", base00: "#322931", base01: "#433b42", base02: "#5c545b", base03: "#797379", base04: "#989498", base05: "#b9b5b8", base06: "#d5d3d5", base07: "#ffffff", base08: "#dd464c", base09: "#fd8b19", base0A: "#fdcc59", base0B: "#8fc13e", base0C: "#149b93", base0D: "#1290bf", base0E: "#c85e7c", base0F: "#b33508" }, e2.exports = t2.default;
      }, 9446: (e2, t2, a2) => {
        "use strict";
        function r2(e3) {
          return e3 && e3.__esModule ? e3.default : e3;
        }
        t2.__esModule = true;
        var n2 = a2(1308);
        t2.threezerotwofour = r2(n2);
        var o = a2(9735);
        t2.apathy = r2(o);
        var s = a2(294);
        t2.ashes = r2(s);
        var i = a2(1733);
        t2.atelierDune = r2(i);
        var l = a2(8974);
        t2.atelierForest = r2(l);
        var c = a2(6933);
        t2.atelierHeath = r2(c);
        var u = a2(523);
        t2.atelierLakeside = r2(u);
        var d = a2(1223);
        t2.atelierSeaside = r2(d);
        var b = a2(1233);
        t2.bespin = r2(b);
        var p = a2(2847);
        t2.brewer = r2(p);
        var f = a2(8120);
        t2.bright = r2(f);
        var h = a2(6305);
        t2.chalk = r2(h);
        var m = a2(525);
        t2.codeschool = r2(m);
        var v = a2(4124);
        t2.colors = r2(v);
        var g = a2(7167);
        t2.default = r2(g);
        var y = a2(4582);
        t2.eighties = r2(y);
        var k = a2(7096);
        t2.embers = r2(k);
        var E = a2(9887);
        t2.flat = r2(E);
        var j = a2(7199);
        t2.google = r2(j);
        var w = a2(1985);
        t2.grayscale = r2(w);
        var x = a2(8093);
        t2.greenscreen = r2(x);
        var C = a2(1615);
        t2.harmonic = r2(C);
        var O = a2(9063);
        t2.hopscotch = r2(O);
        var M = a2(9033);
        t2.isotope = r2(M);
        var S = a2(4112);
        t2.marrakesh = r2(S);
        var _ = a2(9600);
        t2.mocha = r2(_);
        var A = a2(1240);
        t2.monokai = r2(A);
        var F = a2(9768);
        t2.ocean = r2(F);
        var P = a2(8293);
        t2.paraiso = r2(P);
        var D = a2(3093);
        t2.pop = r2(D);
        var I = a2(1951);
        t2.railscasts = r2(I);
        var R = a2(6368);
        t2.shapeshifter = r2(R);
        var z = a2(2317);
        t2.solarized = r2(z);
        var B = a2(1091);
        t2.summerfruit = r2(B);
        var N = a2(6943);
        t2.tomorrow = r2(N);
        var L = a2(5670);
        t2.tube = r2(L);
        var q = a2(2536);
        t2.twilight = r2(q);
      }, 9033: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "isotope", author: "jan t. sott", base00: "#000000", base01: "#404040", base02: "#606060", base03: "#808080", base04: "#c0c0c0", base05: "#d0d0d0", base06: "#e0e0e0", base07: "#ffffff", base08: "#ff0000", base09: "#ff9900", base0A: "#ff0099", base0B: "#33ff00", base0C: "#00ffff", base0D: "#0066ff", base0E: "#cc00ff", base0F: "#3300ff" }, e2.exports = t2.default;
      }, 4112: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "marrakesh", author: "alexandre gavioli (http://github.com/alexx2/)", base00: "#201602", base01: "#302e00", base02: "#5f5b17", base03: "#6c6823", base04: "#86813b", base05: "#948e48", base06: "#ccc37a", base07: "#faf0a5", base08: "#c35359", base09: "#b36144", base0A: "#a88339", base0B: "#18974e", base0C: "#75a738", base0D: "#477ca1", base0E: "#8868b3", base0F: "#b3588e" }, e2.exports = t2.default;
      }, 9600: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "mocha", author: "chris kempson (http://chriskempson.com)", base00: "#3B3228", base01: "#534636", base02: "#645240", base03: "#7e705a", base04: "#b8afad", base05: "#d0c8c6", base06: "#e9e1dd", base07: "#f5eeeb", base08: "#cb6077", base09: "#d28b71", base0A: "#f4bc87", base0B: "#beb55b", base0C: "#7bbda4", base0D: "#8ab3b5", base0E: "#a89bb9", base0F: "#bb9584" }, e2.exports = t2.default;
      }, 1240: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "monokai", author: "wimer hazenberg (http://www.monokai.nl)", base00: "#272822", base01: "#383830", base02: "#49483e", base03: "#75715e", base04: "#a59f85", base05: "#f8f8f2", base06: "#f5f4f1", base07: "#f9f8f5", base08: "#f92672", base09: "#fd971f", base0A: "#f4bf75", base0B: "#a6e22e", base0C: "#a1efe4", base0D: "#66d9ef", base0E: "#ae81ff", base0F: "#cc6633" }, e2.exports = t2.default;
      }, 9768: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "ocean", author: "chris kempson (http://chriskempson.com)", base00: "#2b303b", base01: "#343d46", base02: "#4f5b66", base03: "#65737e", base04: "#a7adba", base05: "#c0c5ce", base06: "#dfe1e8", base07: "#eff1f5", base08: "#bf616a", base09: "#d08770", base0A: "#ebcb8b", base0B: "#a3be8c", base0C: "#96b5b4", base0D: "#8fa1b3", base0E: "#b48ead", base0F: "#ab7967" }, e2.exports = t2.default;
      }, 8293: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "paraiso", author: "jan t. sott", base00: "#2f1e2e", base01: "#41323f", base02: "#4f424c", base03: "#776e71", base04: "#8d8687", base05: "#a39e9b", base06: "#b9b6b0", base07: "#e7e9db", base08: "#ef6155", base09: "#f99b15", base0A: "#fec418", base0B: "#48b685", base0C: "#5bc4bf", base0D: "#06b6ef", base0E: "#815ba4", base0F: "#e96ba8" }, e2.exports = t2.default;
      }, 3093: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "pop", author: "chris kempson (http://chriskempson.com)", base00: "#000000", base01: "#202020", base02: "#303030", base03: "#505050", base04: "#b0b0b0", base05: "#d0d0d0", base06: "#e0e0e0", base07: "#ffffff", base08: "#eb008a", base09: "#f29333", base0A: "#f8ca12", base0B: "#37b349", base0C: "#00aabb", base0D: "#0e5a94", base0E: "#b31e8d", base0F: "#7a2d00" }, e2.exports = t2.default;
      }, 1951: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "railscasts", author: "ryan bates (http://railscasts.com)", base00: "#2b2b2b", base01: "#272935", base02: "#3a4055", base03: "#5a647e", base04: "#d4cfc9", base05: "#e6e1dc", base06: "#f4f1ed", base07: "#f9f7f3", base08: "#da4939", base09: "#cc7833", base0A: "#ffc66d", base0B: "#a5c261", base0C: "#519f50", base0D: "#6d9cbe", base0E: "#b6b3eb", base0F: "#bc9458" }, e2.exports = t2.default;
      }, 6368: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "shapeshifter", author: "tyler benziger (http://tybenz.com)", base00: "#000000", base01: "#040404", base02: "#102015", base03: "#343434", base04: "#555555", base05: "#ababab", base06: "#e0e0e0", base07: "#f9f9f9", base08: "#e92f2f", base09: "#e09448", base0A: "#dddd13", base0B: "#0ed839", base0C: "#23edda", base0D: "#3b48e3", base0E: "#f996e2", base0F: "#69542d" }, e2.exports = t2.default;
      }, 2317: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "solarized", author: "ethan schoonover (http://ethanschoonover.com/solarized)", base00: "#002b36", base01: "#073642", base02: "#586e75", base03: "#657b83", base04: "#839496", base05: "#93a1a1", base06: "#eee8d5", base07: "#fdf6e3", base08: "#dc322f", base09: "#cb4b16", base0A: "#b58900", base0B: "#859900", base0C: "#2aa198", base0D: "#268bd2", base0E: "#6c71c4", base0F: "#d33682" }, e2.exports = t2.default;
      }, 1091: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "summerfruit", author: "christopher corley (http://cscorley.github.io/)", base00: "#151515", base01: "#202020", base02: "#303030", base03: "#505050", base04: "#B0B0B0", base05: "#D0D0D0", base06: "#E0E0E0", base07: "#FFFFFF", base08: "#FF0086", base09: "#FD8900", base0A: "#ABA800", base0B: "#00C918", base0C: "#1faaaa", base0D: "#3777E6", base0E: "#AD00A1", base0F: "#cc6633" }, e2.exports = t2.default;
      }, 1308: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "threezerotwofour", author: "jan t. sott (http://github.com/idleberg)", base00: "#090300", base01: "#3a3432", base02: "#4a4543", base03: "#5c5855", base04: "#807d7c", base05: "#a5a2a2", base06: "#d6d5d4", base07: "#f7f7f7", base08: "#db2d20", base09: "#e8bbd0", base0A: "#fded02", base0B: "#01a252", base0C: "#b5e4f4", base0D: "#01a0e4", base0E: "#a16a94", base0F: "#cdab53" }, e2.exports = t2.default;
      }, 6943: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "tomorrow", author: "chris kempson (http://chriskempson.com)", base00: "#1d1f21", base01: "#282a2e", base02: "#373b41", base03: "#969896", base04: "#b4b7b4", base05: "#c5c8c6", base06: "#e0e0e0", base07: "#ffffff", base08: "#cc6666", base09: "#de935f", base0A: "#f0c674", base0B: "#b5bd68", base0C: "#8abeb7", base0D: "#81a2be", base0E: "#b294bb", base0F: "#a3685a" }, e2.exports = t2.default;
      }, 5670: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "london tube", author: "jan t. sott", base00: "#231f20", base01: "#1c3f95", base02: "#5a5758", base03: "#737171", base04: "#959ca1", base05: "#d9d8d8", base06: "#e7e7e8", base07: "#ffffff", base08: "#ee2e24", base09: "#f386a1", base0A: "#ffd204", base0B: "#00853e", base0C: "#85cebc", base0D: "#009ddc", base0E: "#98005d", base0F: "#b06110" }, e2.exports = t2.default;
      }, 2536: (e2, t2) => {
        "use strict";
        t2.__esModule = true, t2.default = { scheme: "twilight", author: "david hart (http://hart-dev.com)", base00: "#1e1e1e", base01: "#323537", base02: "#464b50", base03: "#5f5a60", base04: "#838184", base05: "#a7a7a7", base06: "#c3c3c3", base07: "#ffffff", base08: "#cf6a4c", base09: "#cda869", base0A: "#f9ee98", base0B: "#8f9d6a", base0C: "#afc4db", base0D: "#7587a6", base0E: "#9b859d", base0F: "#9b703f" }, e2.exports = t2.default;
      }, 6481: (e2, t2, a2) => {
        var r2 = a2(4176), n2 = {};
        for (var o in r2) r2.hasOwnProperty(o) && (n2[r2[o]] = o);
        var s = e2.exports = { rgb: { channels: 3, labels: "rgb" }, hsl: { channels: 3, labels: "hsl" }, hsv: { channels: 3, labels: "hsv" }, hwb: { channels: 3, labels: "hwb" }, cmyk: { channels: 4, labels: "cmyk" }, xyz: { channels: 3, labels: "xyz" }, lab: { channels: 3, labels: "lab" }, lch: { channels: 3, labels: "lch" }, hex: { channels: 1, labels: ["hex"] }, keyword: { channels: 1, labels: ["keyword"] }, ansi16: { channels: 1, labels: ["ansi16"] }, ansi256: { channels: 1, labels: ["ansi256"] }, hcg: { channels: 3, labels: ["h", "c", "g"] }, apple: { channels: 3, labels: ["r16", "g16", "b16"] }, gray: { channels: 1, labels: ["gray"] } };
        for (var i in s) if (s.hasOwnProperty(i)) {
          if (!("channels" in s[i])) throw new Error("missing channels property: " + i);
          if (!("labels" in s[i])) throw new Error("missing channel labels property: " + i);
          if (s[i].labels.length !== s[i].channels) throw new Error("channel and label counts mismatch: " + i);
          var l = s[i].channels, c = s[i].labels;
          delete s[i].channels, delete s[i].labels, Object.defineProperty(s[i], "channels", { value: l }), Object.defineProperty(s[i], "labels", { value: c });
        }
        s.rgb.hsl = function(e3) {
          var t3, a3, r3 = e3[0] / 255, n3 = e3[1] / 255, o2 = e3[2] / 255, s2 = Math.min(r3, n3, o2), i2 = Math.max(r3, n3, o2), l2 = i2 - s2;
          return i2 === s2 ? t3 = 0 : r3 === i2 ? t3 = (n3 - o2) / l2 : n3 === i2 ? t3 = 2 + (o2 - r3) / l2 : o2 === i2 && (t3 = 4 + (r3 - n3) / l2), (t3 = Math.min(60 * t3, 360)) < 0 && (t3 += 360), a3 = (s2 + i2) / 2, [t3, 100 * (i2 === s2 ? 0 : a3 <= 0.5 ? l2 / (i2 + s2) : l2 / (2 - i2 - s2)), 100 * a3];
        }, s.rgb.hsv = function(e3) {
          var t3, a3, r3, n3, o2, s2 = e3[0] / 255, i2 = e3[1] / 255, l2 = e3[2] / 255, c2 = Math.max(s2, i2, l2), u = c2 - Math.min(s2, i2, l2), d = function(e4) {
            return (c2 - e4) / 6 / u + 0.5;
          };
          return 0 === u ? n3 = o2 = 0 : (o2 = u / c2, t3 = d(s2), a3 = d(i2), r3 = d(l2), s2 === c2 ? n3 = r3 - a3 : i2 === c2 ? n3 = 1 / 3 + t3 - r3 : l2 === c2 && (n3 = 2 / 3 + a3 - t3), n3 < 0 ? n3 += 1 : n3 > 1 && (n3 -= 1)), [360 * n3, 100 * o2, 100 * c2];
        }, s.rgb.hwb = function(e3) {
          var t3 = e3[0], a3 = e3[1], r3 = e3[2];
          return [s.rgb.hsl(e3)[0], 100 * (1 / 255 * Math.min(t3, Math.min(a3, r3))), 100 * (r3 = 1 - 1 / 255 * Math.max(t3, Math.max(a3, r3)))];
        }, s.rgb.cmyk = function(e3) {
          var t3, a3 = e3[0] / 255, r3 = e3[1] / 255, n3 = e3[2] / 255;
          return [100 * ((1 - a3 - (t3 = Math.min(1 - a3, 1 - r3, 1 - n3))) / (1 - t3) || 0), 100 * ((1 - r3 - t3) / (1 - t3) || 0), 100 * ((1 - n3 - t3) / (1 - t3) || 0), 100 * t3];
        }, s.rgb.keyword = function(e3) {
          var t3 = n2[e3];
          if (t3) return t3;
          var a3, o2, s2, i2 = 1 / 0;
          for (var l2 in r2) if (r2.hasOwnProperty(l2)) {
            var c2 = r2[l2], u = (o2 = e3, s2 = c2, Math.pow(o2[0] - s2[0], 2) + Math.pow(o2[1] - s2[1], 2) + Math.pow(o2[2] - s2[2], 2));
            u < i2 && (i2 = u, a3 = l2);
          }
          return a3;
        }, s.keyword.rgb = function(e3) {
          return r2[e3];
        }, s.rgb.xyz = function(e3) {
          var t3 = e3[0] / 255, a3 = e3[1] / 255, r3 = e3[2] / 255;
          return [100 * (0.4124 * (t3 = t3 > 0.04045 ? Math.pow((t3 + 0.055) / 1.055, 2.4) : t3 / 12.92) + 0.3576 * (a3 = a3 > 0.04045 ? Math.pow((a3 + 0.055) / 1.055, 2.4) : a3 / 12.92) + 0.1805 * (r3 = r3 > 0.04045 ? Math.pow((r3 + 0.055) / 1.055, 2.4) : r3 / 12.92)), 100 * (0.2126 * t3 + 0.7152 * a3 + 0.0722 * r3), 100 * (0.0193 * t3 + 0.1192 * a3 + 0.9505 * r3)];
        }, s.rgb.lab = function(e3) {
          var t3 = s.rgb.xyz(e3), a3 = t3[0], r3 = t3[1], n3 = t3[2];
          return r3 /= 100, n3 /= 108.883, a3 = (a3 /= 95.047) > 8856e-6 ? Math.pow(a3, 1 / 3) : 7.787 * a3 + 16 / 116, [116 * (r3 = r3 > 8856e-6 ? Math.pow(r3, 1 / 3) : 7.787 * r3 + 16 / 116) - 16, 500 * (a3 - r3), 200 * (r3 - (n3 = n3 > 8856e-6 ? Math.pow(n3, 1 / 3) : 7.787 * n3 + 16 / 116))];
        }, s.hsl.rgb = function(e3) {
          var t3, a3, r3, n3, o2, s2 = e3[0] / 360, i2 = e3[1] / 100, l2 = e3[2] / 100;
          if (0 === i2) return [o2 = 255 * l2, o2, o2];
          t3 = 2 * l2 - (a3 = l2 < 0.5 ? l2 * (1 + i2) : l2 + i2 - l2 * i2), n3 = [0, 0, 0];
          for (var c2 = 0; c2 < 3; c2++) (r3 = s2 + 1 / 3 * -(c2 - 1)) < 0 && r3++, r3 > 1 && r3--, o2 = 6 * r3 < 1 ? t3 + 6 * (a3 - t3) * r3 : 2 * r3 < 1 ? a3 : 3 * r3 < 2 ? t3 + (a3 - t3) * (2 / 3 - r3) * 6 : t3, n3[c2] = 255 * o2;
          return n3;
        }, s.hsl.hsv = function(e3) {
          var t3 = e3[0], a3 = e3[1] / 100, r3 = e3[2] / 100, n3 = a3, o2 = Math.max(r3, 0.01);
          return a3 *= (r3 *= 2) <= 1 ? r3 : 2 - r3, n3 *= o2 <= 1 ? o2 : 2 - o2, [t3, 100 * (0 === r3 ? 2 * n3 / (o2 + n3) : 2 * a3 / (r3 + a3)), 100 * ((r3 + a3) / 2)];
        }, s.hsv.rgb = function(e3) {
          var t3 = e3[0] / 60, a3 = e3[1] / 100, r3 = e3[2] / 100, n3 = Math.floor(t3) % 6, o2 = t3 - Math.floor(t3), s2 = 255 * r3 * (1 - a3), i2 = 255 * r3 * (1 - a3 * o2), l2 = 255 * r3 * (1 - a3 * (1 - o2));
          switch (r3 *= 255, n3) {
            case 0:
              return [r3, l2, s2];
            case 1:
              return [i2, r3, s2];
            case 2:
              return [s2, r3, l2];
            case 3:
              return [s2, i2, r3];
            case 4:
              return [l2, s2, r3];
            case 5:
              return [r3, s2, i2];
          }
        }, s.hsv.hsl = function(e3) {
          var t3, a3, r3, n3 = e3[0], o2 = e3[1] / 100, s2 = e3[2] / 100, i2 = Math.max(s2, 0.01);
          return r3 = (2 - o2) * s2, a3 = o2 * i2, [n3, 100 * (a3 = (a3 /= (t3 = (2 - o2) * i2) <= 1 ? t3 : 2 - t3) || 0), 100 * (r3 /= 2)];
        }, s.hwb.rgb = function(e3) {
          var t3, a3, r3, n3, o2, s2, i2, l2 = e3[0] / 360, c2 = e3[1] / 100, u = e3[2] / 100, d = c2 + u;
          switch (d > 1 && (c2 /= d, u /= d), r3 = 6 * l2 - (t3 = Math.floor(6 * l2)), 1 & t3 && (r3 = 1 - r3), n3 = c2 + r3 * ((a3 = 1 - u) - c2), t3) {
            default:
            case 6:
            case 0:
              o2 = a3, s2 = n3, i2 = c2;
              break;
            case 1:
              o2 = n3, s2 = a3, i2 = c2;
              break;
            case 2:
              o2 = c2, s2 = a3, i2 = n3;
              break;
            case 3:
              o2 = c2, s2 = n3, i2 = a3;
              break;
            case 4:
              o2 = n3, s2 = c2, i2 = a3;
              break;
            case 5:
              o2 = a3, s2 = c2, i2 = n3;
          }
          return [255 * o2, 255 * s2, 255 * i2];
        }, s.cmyk.rgb = function(e3) {
          var t3 = e3[0] / 100, a3 = e3[1] / 100, r3 = e3[2] / 100, n3 = e3[3] / 100;
          return [255 * (1 - Math.min(1, t3 * (1 - n3) + n3)), 255 * (1 - Math.min(1, a3 * (1 - n3) + n3)), 255 * (1 - Math.min(1, r3 * (1 - n3) + n3))];
        }, s.xyz.rgb = function(e3) {
          var t3, a3, r3, n3 = e3[0] / 100, o2 = e3[1] / 100, s2 = e3[2] / 100;
          return a3 = -0.9689 * n3 + 1.8758 * o2 + 0.0415 * s2, r3 = 0.0557 * n3 + -0.204 * o2 + 1.057 * s2, t3 = (t3 = 3.2406 * n3 + -1.5372 * o2 + -0.4986 * s2) > 31308e-7 ? 1.055 * Math.pow(t3, 1 / 2.4) - 0.055 : 12.92 * t3, a3 = a3 > 31308e-7 ? 1.055 * Math.pow(a3, 1 / 2.4) - 0.055 : 12.92 * a3, r3 = r3 > 31308e-7 ? 1.055 * Math.pow(r3, 1 / 2.4) - 0.055 : 12.92 * r3, [255 * (t3 = Math.min(Math.max(0, t3), 1)), 255 * (a3 = Math.min(Math.max(0, a3), 1)), 255 * (r3 = Math.min(Math.max(0, r3), 1))];
        }, s.xyz.lab = function(e3) {
          var t3 = e3[0], a3 = e3[1], r3 = e3[2];
          return a3 /= 100, r3 /= 108.883, t3 = (t3 /= 95.047) > 8856e-6 ? Math.pow(t3, 1 / 3) : 7.787 * t3 + 16 / 116, [116 * (a3 = a3 > 8856e-6 ? Math.pow(a3, 1 / 3) : 7.787 * a3 + 16 / 116) - 16, 500 * (t3 - a3), 200 * (a3 - (r3 = r3 > 8856e-6 ? Math.pow(r3, 1 / 3) : 7.787 * r3 + 16 / 116))];
        }, s.lab.xyz = function(e3) {
          var t3, a3, r3, n3 = e3[0];
          t3 = e3[1] / 500 + (a3 = (n3 + 16) / 116), r3 = a3 - e3[2] / 200;
          var o2 = Math.pow(a3, 3), s2 = Math.pow(t3, 3), i2 = Math.pow(r3, 3);
          return a3 = o2 > 8856e-6 ? o2 : (a3 - 16 / 116) / 7.787, t3 = s2 > 8856e-6 ? s2 : (t3 - 16 / 116) / 7.787, r3 = i2 > 8856e-6 ? i2 : (r3 - 16 / 116) / 7.787, [t3 *= 95.047, a3 *= 100, r3 *= 108.883];
        }, s.lab.lch = function(e3) {
          var t3, a3 = e3[0], r3 = e3[1], n3 = e3[2];
          return (t3 = 360 * Math.atan2(n3, r3) / 2 / Math.PI) < 0 && (t3 += 360), [a3, Math.sqrt(r3 * r3 + n3 * n3), t3];
        }, s.lch.lab = function(e3) {
          var t3, a3 = e3[0], r3 = e3[1];
          return t3 = e3[2] / 360 * 2 * Math.PI, [a3, r3 * Math.cos(t3), r3 * Math.sin(t3)];
        }, s.rgb.ansi16 = function(e3) {
          var t3 = e3[0], a3 = e3[1], r3 = e3[2], n3 = 1 in arguments ? arguments[1] : s.rgb.hsv(e3)[2];
          if (0 === (n3 = Math.round(n3 / 50))) return 30;
          var o2 = 30 + (Math.round(r3 / 255) << 2 | Math.round(a3 / 255) << 1 | Math.round(t3 / 255));
          return 2 === n3 && (o2 += 60), o2;
        }, s.hsv.ansi16 = function(e3) {
          return s.rgb.ansi16(s.hsv.rgb(e3), e3[2]);
        }, s.rgb.ansi256 = function(e3) {
          var t3 = e3[0], a3 = e3[1], r3 = e3[2];
          return t3 === a3 && a3 === r3 ? t3 < 8 ? 16 : t3 > 248 ? 231 : Math.round((t3 - 8) / 247 * 24) + 232 : 16 + 36 * Math.round(t3 / 255 * 5) + 6 * Math.round(a3 / 255 * 5) + Math.round(r3 / 255 * 5);
        }, s.ansi16.rgb = function(e3) {
          var t3 = e3 % 10;
          if (0 === t3 || 7 === t3) return e3 > 50 && (t3 += 3.5), [t3 = t3 / 10.5 * 255, t3, t3];
          var a3 = 0.5 * (1 + ~~(e3 > 50));
          return [(1 & t3) * a3 * 255, (t3 >> 1 & 1) * a3 * 255, (t3 >> 2 & 1) * a3 * 255];
        }, s.ansi256.rgb = function(e3) {
          if (e3 >= 232) {
            var t3 = 10 * (e3 - 232) + 8;
            return [t3, t3, t3];
          }
          var a3;
          return e3 -= 16, [Math.floor(e3 / 36) / 5 * 255, Math.floor((a3 = e3 % 36) / 6) / 5 * 255, a3 % 6 / 5 * 255];
        }, s.rgb.hex = function(e3) {
          var t3 = (((255 & Math.round(e3[0])) << 16) + ((255 & Math.round(e3[1])) << 8) + (255 & Math.round(e3[2]))).toString(16).toUpperCase();
          return "000000".substring(t3.length) + t3;
        }, s.hex.rgb = function(e3) {
          var t3 = e3.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);
          if (!t3) return [0, 0, 0];
          var a3 = t3[0];
          3 === t3[0].length && (a3 = a3.split("").map(function(e4) {
            return e4 + e4;
          }).join(""));
          var r3 = parseInt(a3, 16);
          return [r3 >> 16 & 255, r3 >> 8 & 255, 255 & r3];
        }, s.rgb.hcg = function(e3) {
          var t3, a3 = e3[0] / 255, r3 = e3[1] / 255, n3 = e3[2] / 255, o2 = Math.max(Math.max(a3, r3), n3), s2 = Math.min(Math.min(a3, r3), n3), i2 = o2 - s2;
          return t3 = i2 <= 0 ? 0 : o2 === a3 ? (r3 - n3) / i2 % 6 : o2 === r3 ? 2 + (n3 - a3) / i2 : 4 + (a3 - r3) / i2 + 4, t3 /= 6, [360 * (t3 %= 1), 100 * i2, 100 * (i2 < 1 ? s2 / (1 - i2) : 0)];
        }, s.hsl.hcg = function(e3) {
          var t3 = e3[1] / 100, a3 = e3[2] / 100, r3 = 1, n3 = 0;
          return (r3 = a3 < 0.5 ? 2 * t3 * a3 : 2 * t3 * (1 - a3)) < 1 && (n3 = (a3 - 0.5 * r3) / (1 - r3)), [e3[0], 100 * r3, 100 * n3];
        }, s.hsv.hcg = function(e3) {
          var t3 = e3[1] / 100, a3 = e3[2] / 100, r3 = t3 * a3, n3 = 0;
          return r3 < 1 && (n3 = (a3 - r3) / (1 - r3)), [e3[0], 100 * r3, 100 * n3];
        }, s.hcg.rgb = function(e3) {
          var t3 = e3[0] / 360, a3 = e3[1] / 100, r3 = e3[2] / 100;
          if (0 === a3) return [255 * r3, 255 * r3, 255 * r3];
          var n3, o2 = [0, 0, 0], s2 = t3 % 1 * 6, i2 = s2 % 1, l2 = 1 - i2;
          switch (Math.floor(s2)) {
            case 0:
              o2[0] = 1, o2[1] = i2, o2[2] = 0;
              break;
            case 1:
              o2[0] = l2, o2[1] = 1, o2[2] = 0;
              break;
            case 2:
              o2[0] = 0, o2[1] = 1, o2[2] = i2;
              break;
            case 3:
              o2[0] = 0, o2[1] = l2, o2[2] = 1;
              break;
            case 4:
              o2[0] = i2, o2[1] = 0, o2[2] = 1;
              break;
            default:
              o2[0] = 1, o2[1] = 0, o2[2] = l2;
          }
          return n3 = (1 - a3) * r3, [255 * (a3 * o2[0] + n3), 255 * (a3 * o2[1] + n3), 255 * (a3 * o2[2] + n3)];
        }, s.hcg.hsv = function(e3) {
          var t3 = e3[1] / 100, a3 = t3 + e3[2] / 100 * (1 - t3), r3 = 0;
          return a3 > 0 && (r3 = t3 / a3), [e3[0], 100 * r3, 100 * a3];
        }, s.hcg.hsl = function(e3) {
          var t3 = e3[1] / 100, a3 = e3[2] / 100 * (1 - t3) + 0.5 * t3, r3 = 0;
          return a3 > 0 && a3 < 0.5 ? r3 = t3 / (2 * a3) : a3 >= 0.5 && a3 < 1 && (r3 = t3 / (2 * (1 - a3))), [e3[0], 100 * r3, 100 * a3];
        }, s.hcg.hwb = function(e3) {
          var t3 = e3[1] / 100, a3 = t3 + e3[2] / 100 * (1 - t3);
          return [e3[0], 100 * (a3 - t3), 100 * (1 - a3)];
        }, s.hwb.hcg = function(e3) {
          var t3 = e3[1] / 100, a3 = 1 - e3[2] / 100, r3 = a3 - t3, n3 = 0;
          return r3 < 1 && (n3 = (a3 - r3) / (1 - r3)), [e3[0], 100 * r3, 100 * n3];
        }, s.apple.rgb = function(e3) {
          return [e3[0] / 65535 * 255, e3[1] / 65535 * 255, e3[2] / 65535 * 255];
        }, s.rgb.apple = function(e3) {
          return [e3[0] / 255 * 65535, e3[1] / 255 * 65535, e3[2] / 255 * 65535];
        }, s.gray.rgb = function(e3) {
          return [e3[0] / 100 * 255, e3[0] / 100 * 255, e3[0] / 100 * 255];
        }, s.gray.hsl = s.gray.hsv = function(e3) {
          return [0, 0, e3[0]];
        }, s.gray.hwb = function(e3) {
          return [0, 100, e3[0]];
        }, s.gray.cmyk = function(e3) {
          return [0, 0, 0, e3[0]];
        }, s.gray.lab = function(e3) {
          return [e3[0], 0, 0];
        }, s.gray.hex = function(e3) {
          var t3 = 255 & Math.round(e3[0] / 100 * 255), a3 = ((t3 << 16) + (t3 << 8) + t3).toString(16).toUpperCase();
          return "000000".substring(a3.length) + a3;
        }, s.rgb.gray = function(e3) {
          return [(e3[0] + e3[1] + e3[2]) / 3 / 255 * 100];
        };
      }, 4732: (e2, t2, a2) => {
        var r2 = a2(6481), n2 = a2(1157), o = {};
        Object.keys(r2).forEach(function(e3) {
          o[e3] = {}, Object.defineProperty(o[e3], "channels", { value: r2[e3].channels }), Object.defineProperty(o[e3], "labels", { value: r2[e3].labels });
          var t3 = n2(e3);
          Object.keys(t3).forEach(function(a3) {
            var r3 = t3[a3];
            o[e3][a3] = function(e4) {
              var t4 = function(t5) {
                if (null == t5) return t5;
                arguments.length > 1 && (t5 = Array.prototype.slice.call(arguments));
                var a4 = e4(t5);
                if ("object" == typeof a4) for (var r4 = a4.length, n3 = 0; n3 < r4; n3++) a4[n3] = Math.round(a4[n3]);
                return a4;
              };
              return "conversion" in e4 && (t4.conversion = e4.conversion), t4;
            }(r3), o[e3][a3].raw = function(e4) {
              var t4 = function(t5) {
                return null == t5 ? t5 : (arguments.length > 1 && (t5 = Array.prototype.slice.call(arguments)), e4(t5));
              };
              return "conversion" in e4 && (t4.conversion = e4.conversion), t4;
            }(r3);
          });
        }), e2.exports = o;
      }, 1157: (e2, t2, a2) => {
        var r2 = a2(6481);
        function n2(e3) {
          var t3 = function() {
            for (var e4 = {}, t4 = Object.keys(r2), a4 = t4.length, n4 = 0; n4 < a4; n4++) e4[t4[n4]] = { distance: -1, parent: null };
            return e4;
          }(), a3 = [e3];
          for (t3[e3].distance = 0; a3.length; ) for (var n3 = a3.pop(), o2 = Object.keys(r2[n3]), s2 = o2.length, i = 0; i < s2; i++) {
            var l = o2[i], c = t3[l];
            -1 === c.distance && (c.distance = t3[n3].distance + 1, c.parent = n3, a3.unshift(l));
          }
          return t3;
        }
        function o(e3, t3) {
          return function(a3) {
            return t3(e3(a3));
          };
        }
        function s(e3, t3) {
          for (var a3 = [t3[e3].parent, e3], n3 = r2[t3[e3].parent][e3], s2 = t3[e3].parent; t3[s2].parent; ) a3.unshift(t3[s2].parent), n3 = o(r2[t3[s2].parent][s2], n3), s2 = t3[s2].parent;
          return n3.conversion = a3, n3;
        }
        e2.exports = function(e3) {
          for (var t3 = n2(e3), a3 = {}, r3 = Object.keys(t3), o2 = r3.length, i = 0; i < o2; i++) {
            var l = r3[i];
            null !== t3[l].parent && (a3[l] = s(l, t3));
          }
          return a3;
        };
      }, 4176: (e2) => {
        "use strict";
        e2.exports = { aliceblue: [240, 248, 255], antiquewhite: [250, 235, 215], aqua: [0, 255, 255], aquamarine: [127, 255, 212], azure: [240, 255, 255], beige: [245, 245, 220], bisque: [255, 228, 196], black: [0, 0, 0], blanchedalmond: [255, 235, 205], blue: [0, 0, 255], blueviolet: [138, 43, 226], brown: [165, 42, 42], burlywood: [222, 184, 135], cadetblue: [95, 158, 160], chartreuse: [127, 255, 0], chocolate: [210, 105, 30], coral: [255, 127, 80], cornflowerblue: [100, 149, 237], cornsilk: [255, 248, 220], crimson: [220, 20, 60], cyan: [0, 255, 255], darkblue: [0, 0, 139], darkcyan: [0, 139, 139], darkgoldenrod: [184, 134, 11], darkgray: [169, 169, 169], darkgreen: [0, 100, 0], darkgrey: [169, 169, 169], darkkhaki: [189, 183, 107], darkmagenta: [139, 0, 139], darkolivegreen: [85, 107, 47], darkorange: [255, 140, 0], darkorchid: [153, 50, 204], darkred: [139, 0, 0], darksalmon: [233, 150, 122], darkseagreen: [143, 188, 143], darkslateblue: [72, 61, 139], darkslategray: [47, 79, 79], darkslategrey: [47, 79, 79], darkturquoise: [0, 206, 209], darkviolet: [148, 0, 211], deeppink: [255, 20, 147], deepskyblue: [0, 191, 255], dimgray: [105, 105, 105], dimgrey: [105, 105, 105], dodgerblue: [30, 144, 255], firebrick: [178, 34, 34], floralwhite: [255, 250, 240], forestgreen: [34, 139, 34], fuchsia: [255, 0, 255], gainsboro: [220, 220, 220], ghostwhite: [248, 248, 255], gold: [255, 215, 0], goldenrod: [218, 165, 32], gray: [128, 128, 128], green: [0, 128, 0], greenyellow: [173, 255, 47], grey: [128, 128, 128], honeydew: [240, 255, 240], hotpink: [255, 105, 180], indianred: [205, 92, 92], indigo: [75, 0, 130], ivory: [255, 255, 240], khaki: [240, 230, 140], lavender: [230, 230, 250], lavenderblush: [255, 240, 245], lawngreen: [124, 252, 0], lemonchiffon: [255, 250, 205], lightblue: [173, 216, 230], lightcoral: [240, 128, 128], lightcyan: [224, 255, 255], lightgoldenrodyellow: [250, 250, 210], lightgray: [211, 211, 211], lightgreen: [144, 238, 144], lightgrey: [211, 211, 211], lightpink: [255, 182, 193], lightsalmon: [255, 160, 122], lightseagreen: [32, 178, 170], lightskyblue: [135, 206, 250], lightslategray: [119, 136, 153], lightslategrey: [119, 136, 153], lightsteelblue: [176, 196, 222], lightyellow: [255, 255, 224], lime: [0, 255, 0], limegreen: [50, 205, 50], linen: [250, 240, 230], magenta: [255, 0, 255], maroon: [128, 0, 0], mediumaquamarine: [102, 205, 170], mediumblue: [0, 0, 205], mediumorchid: [186, 85, 211], mediumpurple: [147, 112, 219], mediumseagreen: [60, 179, 113], mediumslateblue: [123, 104, 238], mediumspringgreen: [0, 250, 154], mediumturquoise: [72, 209, 204], mediumvioletred: [199, 21, 133], midnightblue: [25, 25, 112], mintcream: [245, 255, 250], mistyrose: [255, 228, 225], moccasin: [255, 228, 181], navajowhite: [255, 222, 173], navy: [0, 0, 128], oldlace: [253, 245, 230], olive: [128, 128, 0], olivedrab: [107, 142, 35], orange: [255, 165, 0], orangered: [255, 69, 0], orchid: [218, 112, 214], palegoldenrod: [238, 232, 170], palegreen: [152, 251, 152], paleturquoise: [175, 238, 238], palevioletred: [219, 112, 147], papayawhip: [255, 239, 213], peachpuff: [255, 218, 185], peru: [205, 133, 63], pink: [255, 192, 203], plum: [221, 160, 221], powderblue: [176, 224, 230], purple: [128, 0, 128], rebeccapurple: [102, 51, 153], red: [255, 0, 0], rosybrown: [188, 143, 143], royalblue: [65, 105, 225], saddlebrown: [139, 69, 19], salmon: [250, 128, 114], sandybrown: [244, 164, 96], seagreen: [46, 139, 87], seashell: [255, 245, 238], sienna: [160, 82, 45], silver: [192, 192, 192], skyblue: [135, 206, 235], slateblue: [106, 90, 205], slategray: [112, 128, 144], slategrey: [112, 128, 144], snow: [255, 250, 250], springgreen: [0, 255, 127], steelblue: [70, 130, 180], tan: [210, 180, 140], teal: [0, 128, 128], thistle: [216, 191, 216], tomato: [255, 99, 71], turquoise: [64, 224, 208], violet: [238, 130, 238], wheat: [245, 222, 179], white: [255, 255, 255], whitesmoke: [245, 245, 245], yellow: [255, 255, 0], yellowgreen: [154, 205, 50] };
      }, 4877: (e2) => {
        "use strict";
        e2.exports = { aliceblue: [240, 248, 255], antiquewhite: [250, 235, 215], aqua: [0, 255, 255], aquamarine: [127, 255, 212], azure: [240, 255, 255], beige: [245, 245, 220], bisque: [255, 228, 196], black: [0, 0, 0], blanchedalmond: [255, 235, 205], blue: [0, 0, 255], blueviolet: [138, 43, 226], brown: [165, 42, 42], burlywood: [222, 184, 135], cadetblue: [95, 158, 160], chartreuse: [127, 255, 0], chocolate: [210, 105, 30], coral: [255, 127, 80], cornflowerblue: [100, 149, 237], cornsilk: [255, 248, 220], crimson: [220, 20, 60], cyan: [0, 255, 255], darkblue: [0, 0, 139], darkcyan: [0, 139, 139], darkgoldenrod: [184, 134, 11], darkgray: [169, 169, 169], darkgreen: [0, 100, 0], darkgrey: [169, 169, 169], darkkhaki: [189, 183, 107], darkmagenta: [139, 0, 139], darkolivegreen: [85, 107, 47], darkorange: [255, 140, 0], darkorchid: [153, 50, 204], darkred: [139, 0, 0], darksalmon: [233, 150, 122], darkseagreen: [143, 188, 143], darkslateblue: [72, 61, 139], darkslategray: [47, 79, 79], darkslategrey: [47, 79, 79], darkturquoise: [0, 206, 209], darkviolet: [148, 0, 211], deeppink: [255, 20, 147], deepskyblue: [0, 191, 255], dimgray: [105, 105, 105], dimgrey: [105, 105, 105], dodgerblue: [30, 144, 255], firebrick: [178, 34, 34], floralwhite: [255, 250, 240], forestgreen: [34, 139, 34], fuchsia: [255, 0, 255], gainsboro: [220, 220, 220], ghostwhite: [248, 248, 255], gold: [255, 215, 0], goldenrod: [218, 165, 32], gray: [128, 128, 128], green: [0, 128, 0], greenyellow: [173, 255, 47], grey: [128, 128, 128], honeydew: [240, 255, 240], hotpink: [255, 105, 180], indianred: [205, 92, 92], indigo: [75, 0, 130], ivory: [255, 255, 240], khaki: [240, 230, 140], lavender: [230, 230, 250], lavenderblush: [255, 240, 245], lawngreen: [124, 252, 0], lemonchiffon: [255, 250, 205], lightblue: [173, 216, 230], lightcoral: [240, 128, 128], lightcyan: [224, 255, 255], lightgoldenrodyellow: [250, 250, 210], lightgray: [211, 211, 211], lightgreen: [144, 238, 144], lightgrey: [211, 211, 211], lightpink: [255, 182, 193], lightsalmon: [255, 160, 122], lightseagreen: [32, 178, 170], lightskyblue: [135, 206, 250], lightslategray: [119, 136, 153], lightslategrey: [119, 136, 153], lightsteelblue: [176, 196, 222], lightyellow: [255, 255, 224], lime: [0, 255, 0], limegreen: [50, 205, 50], linen: [250, 240, 230], magenta: [255, 0, 255], maroon: [128, 0, 0], mediumaquamarine: [102, 205, 170], mediumblue: [0, 0, 205], mediumorchid: [186, 85, 211], mediumpurple: [147, 112, 219], mediumseagreen: [60, 179, 113], mediumslateblue: [123, 104, 238], mediumspringgreen: [0, 250, 154], mediumturquoise: [72, 209, 204], mediumvioletred: [199, 21, 133], midnightblue: [25, 25, 112], mintcream: [245, 255, 250], mistyrose: [255, 228, 225], moccasin: [255, 228, 181], navajowhite: [255, 222, 173], navy: [0, 0, 128], oldlace: [253, 245, 230], olive: [128, 128, 0], olivedrab: [107, 142, 35], orange: [255, 165, 0], orangered: [255, 69, 0], orchid: [218, 112, 214], palegoldenrod: [238, 232, 170], palegreen: [152, 251, 152], paleturquoise: [175, 238, 238], palevioletred: [219, 112, 147], papayawhip: [255, 239, 213], peachpuff: [255, 218, 185], peru: [205, 133, 63], pink: [255, 192, 203], plum: [221, 160, 221], powderblue: [176, 224, 230], purple: [128, 0, 128], rebeccapurple: [102, 51, 153], red: [255, 0, 0], rosybrown: [188, 143, 143], royalblue: [65, 105, 225], saddlebrown: [139, 69, 19], salmon: [250, 128, 114], sandybrown: [244, 164, 96], seagreen: [46, 139, 87], seashell: [255, 245, 238], sienna: [160, 82, 45], silver: [192, 192, 192], skyblue: [135, 206, 235], slateblue: [106, 90, 205], slategray: [112, 128, 144], slategrey: [112, 128, 144], snow: [255, 250, 250], springgreen: [0, 255, 127], steelblue: [70, 130, 180], tan: [210, 180, 140], teal: [0, 128, 128], thistle: [216, 191, 216], tomato: [255, 99, 71], turquoise: [64, 224, 208], violet: [238, 130, 238], wheat: [245, 222, 179], white: [255, 255, 255], whitesmoke: [245, 245, 245], yellow: [255, 255, 0], yellowgreen: [154, 205, 50] };
      }, 6138: (e2, t2, a2) => {
        var r2 = a2(4877), n2 = a2(301), o = Object.hasOwnProperty, s = /* @__PURE__ */ Object.create(null);
        for (var i in r2) o.call(r2, i) && (s[r2[i]] = i);
        var l = e2.exports = { to: {}, get: {} };
        function c(e3, t3, a3) {
          return Math.min(Math.max(t3, e3), a3);
        }
        function u(e3) {
          var t3 = Math.round(e3).toString(16).toUpperCase();
          return t3.length < 2 ? "0" + t3 : t3;
        }
        l.get = function(e3) {
          var t3, a3;
          switch (e3.substring(0, 3).toLowerCase()) {
            case "hsl":
              t3 = l.get.hsl(e3), a3 = "hsl";
              break;
            case "hwb":
              t3 = l.get.hwb(e3), a3 = "hwb";
              break;
            default:
              t3 = l.get.rgb(e3), a3 = "rgb";
          }
          return t3 ? { model: a3, value: t3 } : null;
        }, l.get.rgb = function(e3) {
          if (!e3) return null;
          var t3, a3, n3, s2 = [0, 0, 0, 1];
          if (t3 = e3.match(/^#([a-f0-9]{6})([a-f0-9]{2})?$/i)) {
            for (n3 = t3[2], t3 = t3[1], a3 = 0; a3 < 3; a3++) {
              var i2 = 2 * a3;
              s2[a3] = parseInt(t3.slice(i2, i2 + 2), 16);
            }
            n3 && (s2[3] = parseInt(n3, 16) / 255);
          } else if (t3 = e3.match(/^#([a-f0-9]{3,4})$/i)) {
            for (n3 = (t3 = t3[1])[3], a3 = 0; a3 < 3; a3++) s2[a3] = parseInt(t3[a3] + t3[a3], 16);
            n3 && (s2[3] = parseInt(n3 + n3, 16) / 255);
          } else if (t3 = e3.match(/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)) {
            for (a3 = 0; a3 < 3; a3++) s2[a3] = parseInt(t3[a3 + 1], 0);
            t3[4] && (t3[5] ? s2[3] = 0.01 * parseFloat(t3[4]) : s2[3] = parseFloat(t3[4]));
          } else {
            if (!(t3 = e3.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/))) return (t3 = e3.match(/^(\w+)$/)) ? "transparent" === t3[1] ? [0, 0, 0, 0] : o.call(r2, t3[1]) ? ((s2 = r2[t3[1]])[3] = 1, s2) : null : null;
            for (a3 = 0; a3 < 3; a3++) s2[a3] = Math.round(2.55 * parseFloat(t3[a3 + 1]));
            t3[4] && (t3[5] ? s2[3] = 0.01 * parseFloat(t3[4]) : s2[3] = parseFloat(t3[4]));
          }
          for (a3 = 0; a3 < 3; a3++) s2[a3] = c(s2[a3], 0, 255);
          return s2[3] = c(s2[3], 0, 1), s2;
        }, l.get.hsl = function(e3) {
          if (!e3) return null;
          var t3 = e3.match(/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);
          if (t3) {
            var a3 = parseFloat(t3[4]);
            return [(parseFloat(t3[1]) % 360 + 360) % 360, c(parseFloat(t3[2]), 0, 100), c(parseFloat(t3[3]), 0, 100), c(isNaN(a3) ? 1 : a3, 0, 1)];
          }
          return null;
        }, l.get.hwb = function(e3) {
          if (!e3) return null;
          var t3 = e3.match(/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);
          if (t3) {
            var a3 = parseFloat(t3[4]);
            return [(parseFloat(t3[1]) % 360 + 360) % 360, c(parseFloat(t3[2]), 0, 100), c(parseFloat(t3[3]), 0, 100), c(isNaN(a3) ? 1 : a3, 0, 1)];
          }
          return null;
        }, l.to.hex = function() {
          var e3 = n2(arguments);
          return "#" + u(e3[0]) + u(e3[1]) + u(e3[2]) + (e3[3] < 1 ? u(Math.round(255 * e3[3])) : "");
        }, l.to.rgb = function() {
          var e3 = n2(arguments);
          return e3.length < 4 || 1 === e3[3] ? "rgb(" + Math.round(e3[0]) + ", " + Math.round(e3[1]) + ", " + Math.round(e3[2]) + ")" : "rgba(" + Math.round(e3[0]) + ", " + Math.round(e3[1]) + ", " + Math.round(e3[2]) + ", " + e3[3] + ")";
        }, l.to.rgb.percent = function() {
          var e3 = n2(arguments), t3 = Math.round(e3[0] / 255 * 100), a3 = Math.round(e3[1] / 255 * 100), r3 = Math.round(e3[2] / 255 * 100);
          return e3.length < 4 || 1 === e3[3] ? "rgb(" + t3 + "%, " + a3 + "%, " + r3 + "%)" : "rgba(" + t3 + "%, " + a3 + "%, " + r3 + "%, " + e3[3] + ")";
        }, l.to.hsl = function() {
          var e3 = n2(arguments);
          return e3.length < 4 || 1 === e3[3] ? "hsl(" + e3[0] + ", " + e3[1] + "%, " + e3[2] + "%)" : "hsla(" + e3[0] + ", " + e3[1] + "%, " + e3[2] + "%, " + e3[3] + ")";
        }, l.to.hwb = function() {
          var e3 = n2(arguments), t3 = "";
          return e3.length >= 4 && 1 !== e3[3] && (t3 = ", " + e3[3]), "hwb(" + e3[0] + ", " + e3[1] + "%, " + e3[2] + "%" + t3 + ")";
        }, l.to.keyword = function(e3) {
          return s[e3.slice(0, 3)];
        };
      }, 3639: (e2, t2, a2) => {
        "use strict";
        var r2 = a2(6138), n2 = a2(4732), o = [].slice, s = ["keyword", "gray", "hex"], i = {};
        Object.keys(n2).forEach(function(e3) {
          i[o.call(n2[e3].labels).sort().join("")] = e3;
        });
        var l = {};
        function c(e3, t3) {
          if (!(this instanceof c)) return new c(e3, t3);
          if (t3 && t3 in s && (t3 = null), t3 && !(t3 in n2)) throw new Error("Unknown model: " + t3);
          var a3, u2;
          if (null == e3) this.model = "rgb", this.color = [0, 0, 0], this.valpha = 1;
          else if (e3 instanceof c) this.model = e3.model, this.color = e3.color.slice(), this.valpha = e3.valpha;
          else if ("string" == typeof e3) {
            var d2 = r2.get(e3);
            if (null === d2) throw new Error("Unable to parse color from string: " + e3);
            this.model = d2.model, u2 = n2[this.model].channels, this.color = d2.value.slice(0, u2), this.valpha = "number" == typeof d2.value[u2] ? d2.value[u2] : 1;
          } else if (e3.length) {
            this.model = t3 || "rgb", u2 = n2[this.model].channels;
            var p = o.call(e3, 0, u2);
            this.color = b(p, u2), this.valpha = "number" == typeof e3[u2] ? e3[u2] : 1;
          } else if ("number" == typeof e3) e3 &= 16777215, this.model = "rgb", this.color = [e3 >> 16 & 255, e3 >> 8 & 255, 255 & e3], this.valpha = 1;
          else {
            this.valpha = 1;
            var f = Object.keys(e3);
            "alpha" in e3 && (f.splice(f.indexOf("alpha"), 1), this.valpha = "number" == typeof e3.alpha ? e3.alpha : 0);
            var h = f.sort().join("");
            if (!(h in i)) throw new Error("Unable to parse color from object: " + JSON.stringify(e3));
            this.model = i[h];
            var m = n2[this.model].labels, v = [];
            for (a3 = 0; a3 < m.length; a3++) v.push(e3[m[a3]]);
            this.color = b(v);
          }
          if (l[this.model]) for (u2 = n2[this.model].channels, a3 = 0; a3 < u2; a3++) {
            var g = l[this.model][a3];
            g && (this.color[a3] = g(this.color[a3]));
          }
          this.valpha = Math.max(0, Math.min(1, this.valpha)), Object.freeze && Object.freeze(this);
        }
        function u(e3, t3, a3) {
          return (e3 = Array.isArray(e3) ? e3 : [e3]).forEach(function(e4) {
            (l[e4] || (l[e4] = []))[t3] = a3;
          }), e3 = e3[0], function(r3) {
            var n3;
            return arguments.length ? (a3 && (r3 = a3(r3)), (n3 = this[e3]()).color[t3] = r3, n3) : (n3 = this[e3]().color[t3], a3 && (n3 = a3(n3)), n3);
          };
        }
        function d(e3) {
          return function(t3) {
            return Math.max(0, Math.min(e3, t3));
          };
        }
        function b(e3, t3) {
          for (var a3 = 0; a3 < t3; a3++) "number" != typeof e3[a3] && (e3[a3] = 0);
          return e3;
        }
        c.prototype = { toString: function() {
          return this.string();
        }, toJSON: function() {
          return this[this.model]();
        }, string: function(e3) {
          var t3 = this.model in r2.to ? this : this.rgb(), a3 = 1 === (t3 = t3.round("number" == typeof e3 ? e3 : 1)).valpha ? t3.color : t3.color.concat(this.valpha);
          return r2.to[t3.model](a3);
        }, percentString: function(e3) {
          var t3 = this.rgb().round("number" == typeof e3 ? e3 : 1), a3 = 1 === t3.valpha ? t3.color : t3.color.concat(this.valpha);
          return r2.to.rgb.percent(a3);
        }, array: function() {
          return 1 === this.valpha ? this.color.slice() : this.color.concat(this.valpha);
        }, object: function() {
          for (var e3 = {}, t3 = n2[this.model].channels, a3 = n2[this.model].labels, r3 = 0; r3 < t3; r3++) e3[a3[r3]] = this.color[r3];
          return 1 !== this.valpha && (e3.alpha = this.valpha), e3;
        }, unitArray: function() {
          var e3 = this.rgb().color;
          return e3[0] /= 255, e3[1] /= 255, e3[2] /= 255, 1 !== this.valpha && e3.push(this.valpha), e3;
        }, unitObject: function() {
          var e3 = this.rgb().object();
          return e3.r /= 255, e3.g /= 255, e3.b /= 255, 1 !== this.valpha && (e3.alpha = this.valpha), e3;
        }, round: function(e3) {
          return e3 = Math.max(e3 || 0, 0), new c(this.color.map(/* @__PURE__ */ function(e4) {
            return function(t3) {
              return function(e5, t4) {
                return Number(e5.toFixed(t4));
              }(t3, e4);
            };
          }(e3)).concat(this.valpha), this.model);
        }, alpha: function(e3) {
          return arguments.length ? new c(this.color.concat(Math.max(0, Math.min(1, e3))), this.model) : this.valpha;
        }, red: u("rgb", 0, d(255)), green: u("rgb", 1, d(255)), blue: u("rgb", 2, d(255)), hue: u(["hsl", "hsv", "hsl", "hwb", "hcg"], 0, function(e3) {
          return (e3 % 360 + 360) % 360;
        }), saturationl: u("hsl", 1, d(100)), lightness: u("hsl", 2, d(100)), saturationv: u("hsv", 1, d(100)), value: u("hsv", 2, d(100)), chroma: u("hcg", 1, d(100)), gray: u("hcg", 2, d(100)), white: u("hwb", 1, d(100)), wblack: u("hwb", 2, d(100)), cyan: u("cmyk", 0, d(100)), magenta: u("cmyk", 1, d(100)), yellow: u("cmyk", 2, d(100)), black: u("cmyk", 3, d(100)), x: u("xyz", 0, d(100)), y: u("xyz", 1, d(100)), z: u("xyz", 2, d(100)), l: u("lab", 0, d(100)), a: u("lab", 1), b: u("lab", 2), keyword: function(e3) {
          return arguments.length ? new c(e3) : n2[this.model].keyword(this.color);
        }, hex: function(e3) {
          return arguments.length ? new c(e3) : r2.to.hex(this.rgb().round().color);
        }, rgbNumber: function() {
          var e3 = this.rgb().color;
          return (255 & e3[0]) << 16 | (255 & e3[1]) << 8 | 255 & e3[2];
        }, luminosity: function() {
          for (var e3 = this.rgb().color, t3 = [], a3 = 0; a3 < e3.length; a3++) {
            var r3 = e3[a3] / 255;
            t3[a3] = r3 <= 0.03928 ? r3 / 12.92 : Math.pow((r3 + 0.055) / 1.055, 2.4);
          }
          return 0.2126 * t3[0] + 0.7152 * t3[1] + 0.0722 * t3[2];
        }, contrast: function(e3) {
          var t3 = this.luminosity(), a3 = e3.luminosity();
          return t3 > a3 ? (t3 + 0.05) / (a3 + 0.05) : (a3 + 0.05) / (t3 + 0.05);
        }, level: function(e3) {
          var t3 = this.contrast(e3);
          return t3 >= 7.1 ? "AAA" : t3 >= 4.5 ? "AA" : "";
        }, isDark: function() {
          var e3 = this.rgb().color;
          return (299 * e3[0] + 587 * e3[1] + 114 * e3[2]) / 1e3 < 128;
        }, isLight: function() {
          return !this.isDark();
        }, negate: function() {
          for (var e3 = this.rgb(), t3 = 0; t3 < 3; t3++) e3.color[t3] = 255 - e3.color[t3];
          return e3;
        }, lighten: function(e3) {
          var t3 = this.hsl();
          return t3.color[2] += t3.color[2] * e3, t3;
        }, darken: function(e3) {
          var t3 = this.hsl();
          return t3.color[2] -= t3.color[2] * e3, t3;
        }, saturate: function(e3) {
          var t3 = this.hsl();
          return t3.color[1] += t3.color[1] * e3, t3;
        }, desaturate: function(e3) {
          var t3 = this.hsl();
          return t3.color[1] -= t3.color[1] * e3, t3;
        }, whiten: function(e3) {
          var t3 = this.hwb();
          return t3.color[1] += t3.color[1] * e3, t3;
        }, blacken: function(e3) {
          var t3 = this.hwb();
          return t3.color[2] += t3.color[2] * e3, t3;
        }, grayscale: function() {
          var e3 = this.rgb().color, t3 = 0.3 * e3[0] + 0.59 * e3[1] + 0.11 * e3[2];
          return c.rgb(t3, t3, t3);
        }, fade: function(e3) {
          return this.alpha(this.valpha - this.valpha * e3);
        }, opaquer: function(e3) {
          return this.alpha(this.valpha + this.valpha * e3);
        }, rotate: function(e3) {
          var t3 = this.hsl(), a3 = t3.color[0];
          return a3 = (a3 = (a3 + e3) % 360) < 0 ? 360 + a3 : a3, t3.color[0] = a3, t3;
        }, mix: function(e3, t3) {
          if (!e3 || !e3.rgb) throw new Error('Argument to "mix" was not a Color instance, but rather an instance of ' + typeof e3);
          var a3 = e3.rgb(), r3 = this.rgb(), n3 = void 0 === t3 ? 0.5 : t3, o2 = 2 * n3 - 1, s2 = a3.alpha() - r3.alpha(), i2 = ((o2 * s2 == -1 ? o2 : (o2 + s2) / (1 + o2 * s2)) + 1) / 2, l2 = 1 - i2;
          return c.rgb(i2 * a3.red() + l2 * r3.red(), i2 * a3.green() + l2 * r3.green(), i2 * a3.blue() + l2 * r3.blue(), a3.alpha() * n3 + r3.alpha() * (1 - n3));
        } }, Object.keys(n2).forEach(function(e3) {
          if (-1 === s.indexOf(e3)) {
            var t3 = n2[e3].channels;
            c.prototype[e3] = function() {
              if (this.model === e3) return new c(this);
              if (arguments.length) return new c(arguments, e3);
              var a3, r3 = "number" == typeof arguments[t3] ? t3 : this.valpha;
              return new c((a3 = n2[this.model][e3].raw(this.color), Array.isArray(a3) ? a3 : [a3]).concat(r3), e3);
            }, c[e3] = function(a3) {
              return "number" == typeof a3 && (a3 = b(o.call(arguments), t3)), new c(a3, e3);
            };
          }
        }), e2.exports = c;
      }, 9784: (e2) => {
        "use strict";
        var t2, a2 = "object" == typeof Reflect ? Reflect : null, r2 = a2 && "function" == typeof a2.apply ? a2.apply : function(e3, t3, a3) {
          return Function.prototype.apply.call(e3, t3, a3);
        };
        t2 = a2 && "function" == typeof a2.ownKeys ? a2.ownKeys : Object.getOwnPropertySymbols ? function(e3) {
          return Object.getOwnPropertyNames(e3).concat(Object.getOwnPropertySymbols(e3));
        } : function(e3) {
          return Object.getOwnPropertyNames(e3);
        };
        var n2 = Number.isNaN || function(e3) {
          return e3 != e3;
        };
        function o() {
          o.init.call(this);
        }
        e2.exports = o, e2.exports.once = function(e3, t3) {
          return new Promise(function(a3, r3) {
            function n3(a4) {
              e3.removeListener(t3, o2), r3(a4);
            }
            function o2() {
              "function" == typeof e3.removeListener && e3.removeListener("error", n3), a3([].slice.call(arguments));
            }
            h(e3, t3, o2, { once: true }), "error" !== t3 && function(e4, t4, a4) {
              "function" == typeof e4.on && h(e4, "error", t4, a4);
            }(e3, n3, { once: true });
          });
        }, o.EventEmitter = o, o.prototype._events = void 0, o.prototype._eventsCount = 0, o.prototype._maxListeners = void 0;
        var s = 10;
        function i(e3) {
          if ("function" != typeof e3) throw new TypeError('The "listener" argument must be of type Function. Received type ' + typeof e3);
        }
        function l(e3) {
          return void 0 === e3._maxListeners ? o.defaultMaxListeners : e3._maxListeners;
        }
        function c(e3, t3, a3, r3) {
          var n3, o2, s2, c2;
          if (i(a3), void 0 === (o2 = e3._events) ? (o2 = e3._events = /* @__PURE__ */ Object.create(null), e3._eventsCount = 0) : (void 0 !== o2.newListener && (e3.emit("newListener", t3, a3.listener ? a3.listener : a3), o2 = e3._events), s2 = o2[t3]), void 0 === s2) s2 = o2[t3] = a3, ++e3._eventsCount;
          else if ("function" == typeof s2 ? s2 = o2[t3] = r3 ? [a3, s2] : [s2, a3] : r3 ? s2.unshift(a3) : s2.push(a3), (n3 = l(e3)) > 0 && s2.length > n3 && !s2.warned) {
            s2.warned = true;
            var u2 = new Error("Possible EventEmitter memory leak detected. " + s2.length + " " + String(t3) + " listeners added. Use emitter.setMaxListeners() to increase limit");
            u2.name = "MaxListenersExceededWarning", u2.emitter = e3, u2.type = t3, u2.count = s2.length, c2 = u2, console && console.warn && console.warn(c2);
          }
          return e3;
        }
        function u() {
          if (!this.fired) return this.target.removeListener(this.type, this.wrapFn), this.fired = true, 0 === arguments.length ? this.listener.call(this.target) : this.listener.apply(this.target, arguments);
        }
        function d(e3, t3, a3) {
          var r3 = { fired: false, wrapFn: void 0, target: e3, type: t3, listener: a3 }, n3 = u.bind(r3);
          return n3.listener = a3, r3.wrapFn = n3, n3;
        }
        function b(e3, t3, a3) {
          var r3 = e3._events;
          if (void 0 === r3) return [];
          var n3 = r3[t3];
          return void 0 === n3 ? [] : "function" == typeof n3 ? a3 ? [n3.listener || n3] : [n3] : a3 ? function(e4) {
            for (var t4 = new Array(e4.length), a4 = 0; a4 < t4.length; ++a4) t4[a4] = e4[a4].listener || e4[a4];
            return t4;
          }(n3) : f(n3, n3.length);
        }
        function p(e3) {
          var t3 = this._events;
          if (void 0 !== t3) {
            var a3 = t3[e3];
            if ("function" == typeof a3) return 1;
            if (void 0 !== a3) return a3.length;
          }
          return 0;
        }
        function f(e3, t3) {
          for (var a3 = new Array(t3), r3 = 0; r3 < t3; ++r3) a3[r3] = e3[r3];
          return a3;
        }
        function h(e3, t3, a3, r3) {
          if ("function" == typeof e3.on) r3.once ? e3.once(t3, a3) : e3.on(t3, a3);
          else {
            if ("function" != typeof e3.addEventListener) throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type ' + typeof e3);
            e3.addEventListener(t3, function n3(o2) {
              r3.once && e3.removeEventListener(t3, n3), a3(o2);
            });
          }
        }
        Object.defineProperty(o, "defaultMaxListeners", { enumerable: true, get: function() {
          return s;
        }, set: function(e3) {
          if ("number" != typeof e3 || e3 < 0 || n2(e3)) throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + e3 + ".");
          s = e3;
        } }), o.init = function() {
          void 0 !== this._events && this._events !== Object.getPrototypeOf(this)._events || (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0), this._maxListeners = this._maxListeners || void 0;
        }, o.prototype.setMaxListeners = function(e3) {
          if ("number" != typeof e3 || e3 < 0 || n2(e3)) throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + e3 + ".");
          return this._maxListeners = e3, this;
        }, o.prototype.getMaxListeners = function() {
          return l(this);
        }, o.prototype.emit = function(e3) {
          for (var t3 = [], a3 = 1; a3 < arguments.length; a3++) t3.push(arguments[a3]);
          var n3 = "error" === e3, o2 = this._events;
          if (void 0 !== o2) n3 = n3 && void 0 === o2.error;
          else if (!n3) return false;
          if (n3) {
            var s2;
            if (t3.length > 0 && (s2 = t3[0]), s2 instanceof Error) throw s2;
            var i2 = new Error("Unhandled error." + (s2 ? " (" + s2.message + ")" : ""));
            throw i2.context = s2, i2;
          }
          var l2 = o2[e3];
          if (void 0 === l2) return false;
          if ("function" == typeof l2) r2(l2, this, t3);
          else {
            var c2 = l2.length, u2 = f(l2, c2);
            for (a3 = 0; a3 < c2; ++a3) r2(u2[a3], this, t3);
          }
          return true;
        }, o.prototype.addListener = function(e3, t3) {
          return c(this, e3, t3, false);
        }, o.prototype.on = o.prototype.addListener, o.prototype.prependListener = function(e3, t3) {
          return c(this, e3, t3, true);
        }, o.prototype.once = function(e3, t3) {
          return i(t3), this.on(e3, d(this, e3, t3)), this;
        }, o.prototype.prependOnceListener = function(e3, t3) {
          return i(t3), this.prependListener(e3, d(this, e3, t3)), this;
        }, o.prototype.removeListener = function(e3, t3) {
          var a3, r3, n3, o2, s2;
          if (i(t3), void 0 === (r3 = this._events)) return this;
          if (void 0 === (a3 = r3[e3])) return this;
          if (a3 === t3 || a3.listener === t3) 0 == --this._eventsCount ? this._events = /* @__PURE__ */ Object.create(null) : (delete r3[e3], r3.removeListener && this.emit("removeListener", e3, a3.listener || t3));
          else if ("function" != typeof a3) {
            for (n3 = -1, o2 = a3.length - 1; o2 >= 0; o2--) if (a3[o2] === t3 || a3[o2].listener === t3) {
              s2 = a3[o2].listener, n3 = o2;
              break;
            }
            if (n3 < 0) return this;
            0 === n3 ? a3.shift() : function(e4, t4) {
              for (; t4 + 1 < e4.length; t4++) e4[t4] = e4[t4 + 1];
              e4.pop();
            }(a3, n3), 1 === a3.length && (r3[e3] = a3[0]), void 0 !== r3.removeListener && this.emit("removeListener", e3, s2 || t3);
          }
          return this;
        }, o.prototype.off = o.prototype.removeListener, o.prototype.removeAllListeners = function(e3) {
          var t3, a3, r3;
          if (void 0 === (a3 = this._events)) return this;
          if (void 0 === a3.removeListener) return 0 === arguments.length ? (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0) : void 0 !== a3[e3] && (0 == --this._eventsCount ? this._events = /* @__PURE__ */ Object.create(null) : delete a3[e3]), this;
          if (0 === arguments.length) {
            var n3, o2 = Object.keys(a3);
            for (r3 = 0; r3 < o2.length; ++r3) "removeListener" !== (n3 = o2[r3]) && this.removeAllListeners(n3);
            return this.removeAllListeners("removeListener"), this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0, this;
          }
          if ("function" == typeof (t3 = a3[e3])) this.removeListener(e3, t3);
          else if (void 0 !== t3) for (r3 = t3.length - 1; r3 >= 0; r3--) this.removeListener(e3, t3[r3]);
          return this;
        }, o.prototype.listeners = function(e3) {
          return b(this, e3, true);
        }, o.prototype.rawListeners = function(e3) {
          return b(this, e3, false);
        }, o.listenerCount = function(e3, t3) {
          return "function" == typeof e3.listenerCount ? e3.listenerCount(t3) : p.call(e3, t3);
        }, o.prototype.listenerCount = p, o.prototype.eventNames = function() {
          return this._eventsCount > 0 ? t2(this._events) : [];
        };
      }, 8336: (e2) => {
        e2.exports = function(e3) {
          return !(!e3 || "string" == typeof e3) && (e3 instanceof Array || Array.isArray(e3) || e3.length >= 0 && (e3.splice instanceof Function || Object.getOwnPropertyDescriptor(e3, e3.length - 1) && "String" !== e3.constructor.name));
        };
      }, 3989: (e2) => {
        var t2 = "__lodash_placeholder__", a2 = 32, r2 = 1 / 0, n2 = NaN, o = [["ary", 128], ["bind", 1], ["bindKey", 2], ["curry", 8], ["curryRight", 16], ["flip", 512], ["partial", a2], ["partialRight", 64], ["rearg", 256]], s = "[object Function]", i = "[object GeneratorFunction]", l = /^\s+|\s+$/g, c = /\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/, u = /\{\n\/\* \[wrapped with (.+)\] \*/, d = /,? & /, b = /^[-+]0x[0-9a-f]+$/i, p = /^0b[01]+$/i, f = /^\[object .+?Constructor\]$/, h = /^0o[0-7]+$/i, m = /^(?:0|[1-9]\d*)$/, v = parseInt, g = "object" == typeof global && global && global.Object === Object && global, y = "object" == typeof self && self && self.Object === Object && self, k = g || y || Function("return this")();
        function E(e3, t3, a3) {
          switch (a3.length) {
            case 0:
              return e3.call(t3);
            case 1:
              return e3.call(t3, a3[0]);
            case 2:
              return e3.call(t3, a3[0], a3[1]);
            case 3:
              return e3.call(t3, a3[0], a3[1], a3[2]);
          }
          return e3.apply(t3, a3);
        }
        function j(e3, t3) {
          return !!(e3 ? e3.length : 0) && function(e4, t4, a3) {
            if (t4 != t4) return function(e5, t5, a4, r4) {
              var n4 = e5.length, o2 = a4 + (r4 ? 1 : -1);
              for (; r4 ? o2-- : ++o2 < n4; ) if (t5(e5[o2], o2, e5)) return o2;
              return -1;
            }(e4, w, a3);
            var r3 = a3 - 1, n3 = e4.length;
            for (; ++r3 < n3; ) if (e4[r3] === t4) return r3;
            return -1;
          }(e3, t3, 0) > -1;
        }
        function w(e3) {
          return e3 != e3;
        }
        function x(e3, a3) {
          for (var r3 = -1, n3 = e3.length, o2 = 0, s2 = []; ++r3 < n3; ) {
            var i2 = e3[r3];
            i2 !== a3 && i2 !== t2 || (e3[r3] = t2, s2[o2++] = r3);
          }
          return s2;
        }
        var C, O, M, S = Function.prototype, _ = Object.prototype, A = k["__core-js_shared__"], F = (C = /[^.]+$/.exec(A && A.keys && A.keys.IE_PROTO || "")) ? "Symbol(src)_1." + C : "", P = S.toString, D = _.hasOwnProperty, I = _.toString, R = RegExp("^" + P.call(D).replace(/[\\^$.*+?()[\]{}|]/g, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"), z = Object.create, B = Math.max, N = Math.min, L = (O = H(Object, "defineProperty"), (M = H.name) && M.length > 2 ? O : void 0);
        function q(e3) {
          if (!X(e3) || function(e4) {
            return !!F && F in e4;
          }(e3)) return false;
          var t3 = function(e4) {
            var t4 = X(e4) ? I.call(e4) : "";
            return t4 == s || t4 == i;
          }(e3) || function(e4) {
            var t4 = false;
            if (null != e4 && "function" != typeof e4.toString) try {
              t4 = !!(e4 + "");
            } catch (e5) {
            }
            return t4;
          }(e3) ? R : f;
          return t3.test(function(e4) {
            if (null != e4) {
              try {
                return P.call(e4);
              } catch (e5) {
              }
              try {
                return e4 + "";
              } catch (e5) {
              }
            }
            return "";
          }(e3));
        }
        function V(e3) {
          return function() {
            var t3 = arguments;
            switch (t3.length) {
              case 0:
                return new e3();
              case 1:
                return new e3(t3[0]);
              case 2:
                return new e3(t3[0], t3[1]);
              case 3:
                return new e3(t3[0], t3[1], t3[2]);
              case 4:
                return new e3(t3[0], t3[1], t3[2], t3[3]);
              case 5:
                return new e3(t3[0], t3[1], t3[2], t3[3], t3[4]);
              case 6:
                return new e3(t3[0], t3[1], t3[2], t3[3], t3[4], t3[5]);
              case 7:
                return new e3(t3[0], t3[1], t3[2], t3[3], t3[4], t3[5], t3[6]);
            }
            var a3, r3 = X(a3 = e3.prototype) ? z(a3) : {}, n3 = e3.apply(r3, t3);
            return X(n3) ? n3 : r3;
          };
        }
        function T(e3, t3, a3, r3, n3, o2, s2, i2, l2, c2) {
          var u2 = 128 & t3, d2 = 1 & t3, b2 = 2 & t3, p2 = 24 & t3, f2 = 512 & t3, h2 = b2 ? void 0 : V(e3);
          return function m2() {
            for (var v2 = arguments.length, g2 = Array(v2), y2 = v2; y2--; ) g2[y2] = arguments[y2];
            if (p2) var E2 = U(m2), j2 = function(e4, t4) {
              for (var a4 = e4.length, r4 = 0; a4--; ) e4[a4] === t4 && r4++;
              return r4;
            }(g2, E2);
            if (r3 && (g2 = function(e4, t4, a4, r4) {
              for (var n4 = -1, o3 = e4.length, s3 = a4.length, i3 = -1, l3 = t4.length, c3 = B(o3 - s3, 0), u3 = Array(l3 + c3), d3 = !r4; ++i3 < l3; ) u3[i3] = t4[i3];
              for (; ++n4 < s3; ) (d3 || n4 < o3) && (u3[a4[n4]] = e4[n4]);
              for (; c3--; ) u3[i3++] = e4[n4++];
              return u3;
            }(g2, r3, n3, p2)), o2 && (g2 = function(e4, t4, a4, r4) {
              for (var n4 = -1, o3 = e4.length, s3 = -1, i3 = a4.length, l3 = -1, c3 = t4.length, u3 = B(o3 - i3, 0), d3 = Array(u3 + c3), b3 = !r4; ++n4 < u3; ) d3[n4] = e4[n4];
              for (var p3 = n4; ++l3 < c3; ) d3[p3 + l3] = t4[l3];
              for (; ++s3 < i3; ) (b3 || n4 < o3) && (d3[p3 + a4[s3]] = e4[n4++]);
              return d3;
            }(g2, o2, s2, p2)), v2 -= j2, p2 && v2 < c2) {
              var w2 = x(g2, E2);
              return K(e3, t3, T, m2.placeholder, a3, g2, w2, i2, l2, c2 - v2);
            }
            var C2 = d2 ? a3 : this, O2 = b2 ? C2[e3] : e3;
            return v2 = g2.length, i2 ? g2 = function(e4, t4) {
              var a4 = e4.length, r4 = N(t4.length, a4), n4 = function(e5, t5) {
                var a5 = -1, r5 = e5.length;
                for (t5 || (t5 = Array(r5)); ++a5 < r5; ) t5[a5] = e5[a5];
                return t5;
              }(e4);
              for (; r4--; ) {
                var o3 = t4[r4];
                e4[r4] = J(o3, a4) ? n4[o3] : void 0;
              }
              return e4;
            }(g2, i2) : f2 && v2 > 1 && g2.reverse(), u2 && l2 < v2 && (g2.length = l2), this && this !== k && this instanceof m2 && (O2 = h2 || V(O2)), O2.apply(C2, g2);
          };
        }
        function K(e3, t3, r3, n3, o2, s2, i2, l2, c2, u2) {
          var d2 = 8 & t3;
          t3 |= d2 ? a2 : 64, 4 & (t3 &= ~(d2 ? 64 : a2)) || (t3 &= -4);
          var b2 = r3(e3, t3, o2, d2 ? s2 : void 0, d2 ? i2 : void 0, d2 ? void 0 : s2, d2 ? void 0 : i2, l2, c2, u2);
          return b2.placeholder = n3, G(b2, e3, t3);
        }
        function W(e3, t3, r3, n3, o2, s2, i2, l2) {
          var c2 = 2 & t3;
          if (!c2 && "function" != typeof e3) throw new TypeError("Expected a function");
          var u2 = n3 ? n3.length : 0;
          if (u2 || (t3 &= -97, n3 = o2 = void 0), i2 = void 0 === i2 ? i2 : B(te(i2), 0), l2 = void 0 === l2 ? l2 : te(l2), u2 -= o2 ? o2.length : 0, 64 & t3) {
            var d2 = n3, b2 = o2;
            n3 = o2 = void 0;
          }
          var p2 = [e3, t3, r3, n3, o2, d2, b2, s2, i2, l2];
          if (e3 = p2[0], t3 = p2[1], r3 = p2[2], n3 = p2[3], o2 = p2[4], !(l2 = p2[9] = null == p2[9] ? c2 ? 0 : e3.length : B(p2[9] - u2, 0)) && 24 & t3 && (t3 &= -25), t3 && 1 != t3) f2 = 8 == t3 || 16 == t3 ? function(e4, t4, a3) {
            var r4 = V(e4);
            return function n4() {
              for (var o3 = arguments.length, s3 = Array(o3), i3 = o3, l3 = U(n4); i3--; ) s3[i3] = arguments[i3];
              var c3 = o3 < 3 && s3[0] !== l3 && s3[o3 - 1] !== l3 ? [] : x(s3, l3);
              return (o3 -= c3.length) < a3 ? K(e4, t4, T, n4.placeholder, void 0, s3, c3, void 0, void 0, a3 - o3) : E(this && this !== k && this instanceof n4 ? r4 : e4, this, s3);
            };
          }(e3, t3, l2) : t3 != a2 && 33 != t3 || o2.length ? T.apply(void 0, p2) : function(e4, t4, a3, r4) {
            var n4 = 1 & t4, o3 = V(e4);
            return function t5() {
              for (var s3 = -1, i3 = arguments.length, l3 = -1, c3 = r4.length, u3 = Array(c3 + i3), d3 = this && this !== k && this instanceof t5 ? o3 : e4; ++l3 < c3; ) u3[l3] = r4[l3];
              for (; i3--; ) u3[l3++] = arguments[++s3];
              return E(d3, n4 ? a3 : this, u3);
            };
          }(e3, t3, r3, n3);
          else var f2 = function(e4, t4, a3) {
            var r4 = 1 & t4, n4 = V(e4);
            return function t5() {
              return (this && this !== k && this instanceof t5 ? n4 : e4).apply(r4 ? a3 : this, arguments);
            };
          }(e3, t3, r3);
          return G(f2, e3, t3);
        }
        function U(e3) {
          return e3.placeholder;
        }
        function H(e3, t3) {
          var a3 = function(e4, t4) {
            return null == e4 ? void 0 : e4[t4];
          }(e3, t3);
          return q(a3) ? a3 : void 0;
        }
        function $(e3) {
          var t3 = e3.match(u);
          return t3 ? t3[1].split(d) : [];
        }
        function Y(e3, t3) {
          var a3 = t3.length, r3 = a3 - 1;
          return t3[r3] = (a3 > 1 ? "& " : "") + t3[r3], t3 = t3.join(a3 > 2 ? ", " : " "), e3.replace(c, "{\n/* [wrapped with " + t3 + "] */\n");
        }
        function J(e3, t3) {
          return !!(t3 = null == t3 ? 9007199254740991 : t3) && ("number" == typeof e3 || m.test(e3)) && e3 > -1 && e3 % 1 == 0 && e3 < t3;
        }
        var G = L ? function(e3, t3, a3) {
          var r3, n3 = t3 + "";
          return L(e3, "toString", { configurable: true, enumerable: false, value: (r3 = Y(n3, Q($(n3), a3)), function() {
            return r3;
          }) });
        } : function(e3) {
          return e3;
        };
        function Q(e3, t3) {
          return function(e4, t4) {
            for (var a3 = -1, r3 = e4 ? e4.length : 0; ++a3 < r3 && false !== t4(e4[a3], a3, e4); ) ;
          }(o, function(a3) {
            var r3 = "_." + a3[0];
            t3 & a3[1] && !j(e3, r3) && e3.push(r3);
          }), e3.sort();
        }
        function Z(e3, t3, a3) {
          var r3 = W(e3, 8, void 0, void 0, void 0, void 0, void 0, t3 = a3 ? void 0 : t3);
          return r3.placeholder = Z.placeholder, r3;
        }
        function X(e3) {
          var t3 = typeof e3;
          return !!e3 && ("object" == t3 || "function" == t3);
        }
        function ee(e3) {
          return e3 ? (e3 = function(e4) {
            if ("number" == typeof e4) return e4;
            if (function(e5) {
              return "symbol" == typeof e5 || /* @__PURE__ */ function(e6) {
                return !!e6 && "object" == typeof e6;
              }(e5) && "[object Symbol]" == I.call(e5);
            }(e4)) return n2;
            if (X(e4)) {
              var t3 = "function" == typeof e4.valueOf ? e4.valueOf() : e4;
              e4 = X(t3) ? t3 + "" : t3;
            }
            if ("string" != typeof e4) return 0 === e4 ? e4 : +e4;
            e4 = e4.replace(l, "");
            var a3 = p.test(e4);
            return a3 || h.test(e4) ? v(e4.slice(2), a3 ? 2 : 8) : b.test(e4) ? n2 : +e4;
          }(e3)) === r2 || e3 === -1 / 0 ? 17976931348623157e292 * (e3 < 0 ? -1 : 1) : e3 == e3 ? e3 : 0 : 0 === e3 ? e3 : 0;
        }
        function te(e3) {
          var t3 = ee(e3), a3 = t3 % 1;
          return t3 == t3 ? a3 ? t3 - a3 : t3 : 0;
        }
        Z.placeholder = {}, e2.exports = Z;
      }, 301: (e2, t2, a2) => {
        "use strict";
        var r2 = a2(8336), n2 = Array.prototype.concat, o = Array.prototype.slice, s = e2.exports = function(e3) {
          for (var t3 = [], a3 = 0, s2 = e3.length; a3 < s2; a3++) {
            var i = e3[a3];
            r2(i) ? t3 = n2.call(t3, o.call(i)) : t3.push(i);
          }
          return t3;
        };
        s.wrap = function(e3) {
          return function() {
            return e3(s(arguments));
          };
        };
      }, 4119: (t2) => {
        "use strict";
        t2.exports = e;
      } }, a = {};
      function r(e2) {
        var n2 = a[e2];
        if (void 0 !== n2) return n2.exports;
        var o = a[e2] = { exports: {} };
        return t[e2](o, o.exports, r), o.exports;
      }
      r.n = (e2) => {
        var t2 = e2 && e2.__esModule ? () => e2.default : () => e2;
        return r.d(t2, { a: t2 }), t2;
      }, r.d = (e2, t2) => {
        for (var a2 in t2) r.o(t2, a2) && !r.o(e2, a2) && Object.defineProperty(e2, a2, { enumerable: true, get: t2[a2] });
      }, r.o = (e2, t2) => Object.prototype.hasOwnProperty.call(e2, t2), r.r = (e2) => {
        "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e2, "__esModule", { value: true });
      };
      var n = {};
      return (() => {
        "use strict";
        function e2(t3) {
          return e2 = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e3) {
            return typeof e3;
          } : function(e3) {
            return e3 && "function" == typeof Symbol && e3.constructor === Symbol && e3 !== Symbol.prototype ? "symbol" : typeof e3;
          }, e2(t3);
        }
        function t2(t3) {
          var a3 = function(t4, a4) {
            if ("object" != e2(t4) || !t4) return t4;
            var r2 = t4[Symbol.toPrimitive];
            if (void 0 !== r2) {
              var n2 = r2.call(t4, a4 || "default");
              if ("object" != e2(n2)) return n2;
              throw new TypeError("@@toPrimitive must return a primitive value.");
            }
            return ("string" === a4 ? String : Number)(t4);
          }(t3, "string");
          return "symbol" == e2(a3) ? a3 : a3 + "";
        }
        function a2(e3, a3, r2) {
          return (a3 = t2(a3)) in e3 ? Object.defineProperty(e3, a3, { value: r2, enumerable: true, configurable: true, writable: true }) : e3[a3] = r2, e3;
        }
        function o(e3, t3) {
          var a3 = Object.keys(e3);
          if (Object.getOwnPropertySymbols) {
            var r2 = Object.getOwnPropertySymbols(e3);
            t3 && (r2 = r2.filter(function(t4) {
              return Object.getOwnPropertyDescriptor(e3, t4).enumerable;
            })), a3.push.apply(a3, r2);
          }
          return a3;
        }
        function s(e3) {
          for (var t3 = 1; t3 < arguments.length; t3++) {
            var r2 = null != arguments[t3] ? arguments[t3] : {};
            t3 % 2 ? o(Object(r2), true).forEach(function(t4) {
              a2(e3, t4, r2[t4]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e3, Object.getOwnPropertyDescriptors(r2)) : o(Object(r2)).forEach(function(t4) {
              Object.defineProperty(e3, t4, Object.getOwnPropertyDescriptor(r2, t4));
            });
          }
          return e3;
        }
        function i(e3, t3) {
          if (!(e3 instanceof t3)) throw new TypeError("Cannot call a class as a function");
        }
        function l(e3, a3) {
          for (var r2 = 0; r2 < a3.length; r2++) {
            var n2 = a3[r2];
            n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(e3, t2(n2.key), n2);
          }
        }
        function c(e3, t3, a3) {
          return t3 && l(e3.prototype, t3), a3 && l(e3, a3), Object.defineProperty(e3, "prototype", { writable: false }), e3;
        }
        function u(e3) {
          return u = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e4) {
            return e4.__proto__ || Object.getPrototypeOf(e4);
          }, u(e3);
        }
        function d() {
          try {
            var e3 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
            }));
          } catch (e4) {
          }
          return (d = function() {
            return !!e3;
          })();
        }
        function b(t3, a3) {
          if (a3 && ("object" == e2(a3) || "function" == typeof a3)) return a3;
          if (void 0 !== a3) throw new TypeError("Derived constructors may only return object or undefined");
          return function(e3) {
            if (void 0 === e3) throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
            return e3;
          }(t3);
        }
        function p(e3, t3, a3) {
          return t3 = u(t3), b(e3, d() ? Reflect.construct(t3, a3 || [], u(e3).constructor) : t3.apply(e3, a3));
        }
        function f(e3, t3) {
          return f = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e4, t4) {
            return e4.__proto__ = t4, e4;
          }, f(e3, t3);
        }
        function h(e3, t3) {
          if ("function" != typeof t3 && null !== t3) throw new TypeError("Super expression must either be null or a function");
          e3.prototype = Object.create(t3 && t3.prototype, { constructor: { value: e3, writable: true, configurable: true } }), Object.defineProperty(e3, "prototype", { writable: false }), t3 && f(e3, t3);
        }
        r.r(n), r.d(n, { default: () => gt });
        var m = r(4119), v = r.n(m);
        function g() {
          var e3 = this.constructor.getDerivedStateFromProps(this.props, this.state);
          null != e3 && this.setState(e3);
        }
        function y(e3) {
          this.setState((function(t3) {
            var a3 = this.constructor.getDerivedStateFromProps(e3, t3);
            return null != a3 ? a3 : null;
          }).bind(this));
        }
        function k(e3, t3) {
          try {
            var a3 = this.props, r2 = this.state;
            this.props = e3, this.state = t3, this.__reactInternalSnapshotFlag = true, this.__reactInternalSnapshot = this.getSnapshotBeforeUpdate(a3, r2);
          } finally {
            this.props = a3, this.state = r2;
          }
        }
        function E(e3) {
          var t3 = e3.prototype;
          if (!t3 || !t3.isReactComponent) throw new Error("Can only polyfill class components");
          if ("function" != typeof e3.getDerivedStateFromProps && "function" != typeof t3.getSnapshotBeforeUpdate) return e3;
          var a3 = null, r2 = null, n2 = null;
          if ("function" == typeof t3.componentWillMount ? a3 = "componentWillMount" : "function" == typeof t3.UNSAFE_componentWillMount && (a3 = "UNSAFE_componentWillMount"), "function" == typeof t3.componentWillReceiveProps ? r2 = "componentWillReceiveProps" : "function" == typeof t3.UNSAFE_componentWillReceiveProps && (r2 = "UNSAFE_componentWillReceiveProps"), "function" == typeof t3.componentWillUpdate ? n2 = "componentWillUpdate" : "function" == typeof t3.UNSAFE_componentWillUpdate && (n2 = "UNSAFE_componentWillUpdate"), null !== a3 || null !== r2 || null !== n2) {
            var o2 = e3.displayName || e3.name, s2 = "function" == typeof e3.getDerivedStateFromProps ? "getDerivedStateFromProps()" : "getSnapshotBeforeUpdate()";
            throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n" + o2 + " uses " + s2 + " but also contains the following legacy lifecycles:" + (null !== a3 ? "\n  " + a3 : "") + (null !== r2 ? "\n  " + r2 : "") + (null !== n2 ? "\n  " + n2 : "") + "\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks");
          }
          if ("function" == typeof e3.getDerivedStateFromProps && (t3.componentWillMount = g, t3.componentWillReceiveProps = y), "function" == typeof t3.getSnapshotBeforeUpdate) {
            if ("function" != typeof t3.componentDidUpdate) throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");
            t3.componentWillUpdate = k;
            var i2 = t3.componentDidUpdate;
            t3.componentDidUpdate = function(e4, t4, a4) {
              var r3 = this.__reactInternalSnapshotFlag ? this.__reactInternalSnapshot : a4;
              i2.call(this, e4, t4, r3);
            };
          }
          return e3;
        }
        function j(e3, t3) {
          if (null == e3) return {};
          var a3 = {};
          for (var r2 in e3) if ({}.hasOwnProperty.call(e3, r2)) {
            if (t3.includes(r2)) continue;
            a3[r2] = e3[r2];
          }
          return a3;
        }
        function w(e3, t3) {
          if (null == e3) return {};
          var a3, r2, n2 = j(e3, t3);
          if (Object.getOwnPropertySymbols) {
            var o2 = Object.getOwnPropertySymbols(e3);
            for (r2 = 0; r2 < o2.length; r2++) a3 = o2[r2], t3.includes(a3) || {}.propertyIsEnumerable.call(e3, a3) && (n2[a3] = e3[a3]);
          }
          return n2;
        }
        function x(e3, t3) {
          if (t3 && (null == e3 ? void 0 : e3.constructor) === t3) return "bigNumber";
          var a3 = function(e4) {
            return {}.toString.call(e4).match(/\s([a-zA-Z]+)/)[1].toLowerCase();
          }(e3);
          return "number" === a3 && (a3 = isNaN(e3) ? "nan" : (0 | e3) != e3 ? "float" : "integer"), a3;
        }
        function C(e3) {
          return e3.replace(/\\/g, "\\\\").replace(/\n/g, "\\n").replace(/\t/g, "\\t").replace(/\r/g, "\\r").replace(/\f/g, "\\f");
        }
        g.__suppressDeprecationWarning = true, y.__suppressDeprecationWarning = true, k.__suppressDeprecationWarning = true;
        var O = { scheme: "rjv-default", author: "mac gainor", base00: "rgba(0, 0, 0, 0)", base01: "rgb(245, 245, 245)", base02: "rgb(235, 235, 235)", base03: "#93a1a1", base04: "rgba(0, 0, 0, 0.3)", base05: "#586e75", base06: "#073642", base07: "#002b36", base08: "#d33682", base09: "#cb4b16", base0A: "#dc322f", base0B: "#859900", base0C: "#6c71c4", base0D: "#586e75", base0E: "#2aa198", base0F: "#268bd2" }, M = { scheme: "rjv-grey", author: "mac gainor", base00: "rgba(1, 1, 1, 0)", base01: "rgba(1, 1, 1, 0.1)", base02: "rgba(0, 0, 0, 0.2)", base03: "rgba(1, 1, 1, 0.3)", base04: "rgba(0, 0, 0, 0.4)", base05: "rgba(1, 1, 1, 0.5)", base06: "rgba(1, 1, 1, 0.6)", base07: "rgba(1, 1, 1, 0.7)", base08: "rgba(1, 1, 1, 0.8)", base09: "rgba(1, 1, 1, 0.8)", base0A: "rgba(1, 1, 1, 0.8)", base0B: "rgba(1, 1, 1, 0.8)", base0C: "rgba(1, 1, 1, 0.8)", base0D: "rgba(1, 1, 1, 0.8)", base0E: "rgba(1, 1, 1, 0.8)", base0F: "rgba(1, 1, 1, 0.8)" };
        const S = { white: "#fff", black: "#000", transparent: "rgba(1, 1, 1, 0)", globalFontFamily: "monospace", globalCursor: "default", indentBlockWidth: "5px", braceFontWeight: "bold", braceCursor: "pointer", ellipsisFontSize: "18px", ellipsisLineHeight: "10px", ellipsisCursor: "pointer", keyMargin: "0px 5px", keyLetterSpacing: "0.5px", keyFontStyle: "none", keyBorderRadius: "3px", keyColonWeight: "bold", keyVerticalAlign: "top", keyOpacity: "0.85", keyOpacityHover: "1", keyValPaddingTop: "3px", keyValPaddingBottom: "3px", keyValPaddingRight: "5px", keyValBorderLeft: "1px solid", keyValBorderHover: "2px solid", keyValPaddingHover: "3px 5px 3px 4px", pushedContentMarginLeft: "6px", variableValuePaddingRight: "6px", nullFontSize: "11px", nullFontWeight: "bold", nullPadding: "1px 2px", nullBorderRadius: "3px", nanFontSize: "11px", nanFontWeight: "bold", nanPadding: "1px 2px", nanBorderRadius: "3px", undefinedFontSize: "11px", undefinedFontWeight: "bold", undefinedPadding: "1px 2px", undefinedBorderRadius: "3px", dataTypeFontSize: "11px", dataTypeMarginRight: "4px", datatypeOpacity: "0.8", objectSizeBorderRadius: "3px", objectSizeFontStyle: "italic", objectSizeMargin: "0px 6px 0px 0px", clipboardCursor: "pointer", clipboardCheckMarginLeft: "-12px", metaDataPadding: "0px 0px 0px 10px", arrayGroupMetaPadding: "0px 0px 0px 4px", iconContainerWidth: "17px", tooltipPadding: "4px", editInputMinWidth: "130px", editInputBorderRadius: "2px", editInputPadding: "5px", editInputMarginRight: "4px", editInputFontFamily: "monospace", iconCursor: "pointer", iconFontSize: "15px", iconPaddingRight: "1px", dateValueMarginLeft: "2px", iconMarginRight: "3px", detectedRowPaddingTop: "3px", addKeyCoverBackground: "rgba(255, 255, 255, 0.3)", addKeyCoverPosition: "absolute", addKeyCoverPositionPx: "0px", addKeyModalWidth: "200px", addKeyModalMargin: "auto", addKeyModalPadding: "10px", addKeyModalRadius: "3px" };
        function _(e3, t3) {
          (null == t3 || t3 > e3.length) && (t3 = e3.length);
          for (var a3 = 0, r2 = Array(t3); a3 < t3; a3++) r2[a3] = e3[a3];
          return r2;
        }
        function A(e3, t3) {
          if (e3) {
            if ("string" == typeof e3) return _(e3, t3);
            var a3 = {}.toString.call(e3).slice(8, -1);
            return "Object" === a3 && e3.constructor && (a3 = e3.constructor.name), "Map" === a3 || "Set" === a3 ? Array.from(e3) : "Arguments" === a3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a3) ? _(e3, t3) : void 0;
          }
        }
        function F(e3, t3) {
          return function(e4) {
            if (Array.isArray(e4)) return e4;
          }(e3) || function(e4, t4) {
            var a3 = null == e4 ? null : "undefined" != typeof Symbol && e4[Symbol.iterator] || e4["@@iterator"];
            if (null != a3) {
              var r2, n2, o2, s2, i2 = [], l2 = true, c2 = false;
              try {
                if (o2 = (a3 = a3.call(e4)).next, 0 === t4) {
                  if (Object(a3) !== a3) return;
                  l2 = false;
                } else for (; !(l2 = (r2 = o2.call(a3)).done) && (i2.push(r2.value), i2.length !== t4); l2 = true) ;
              } catch (e5) {
                c2 = true, n2 = e5;
              } finally {
                try {
                  if (!l2 && null != a3.return && (s2 = a3.return(), Object(s2) !== s2)) return;
                } finally {
                  if (c2) throw n2;
                }
              }
              return i2;
            }
          }(e3, t3) || A(e3, t3) || function() {
            throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
          }();
        }
        var P = r(9446), D = r(3639), I = r.n(D), R = r(3989), z = r.n(R);
        function B(e3, t3) {
          var a3 = Object.keys(e3);
          if (Object.getOwnPropertySymbols) {
            var r2 = Object.getOwnPropertySymbols(e3);
            t3 && (r2 = r2.filter(function(t4) {
              return Object.getOwnPropertyDescriptor(e3, t4).enumerable;
            })), a3.push.apply(a3, r2);
          }
          return a3;
        }
        function N(e3) {
          for (var t3 = 1; t3 < arguments.length; t3++) {
            var r2 = null != arguments[t3] ? arguments[t3] : {};
            t3 % 2 ? B(Object(r2), true).forEach(function(t4) {
              a2(e3, t4, r2[t4]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e3, Object.getOwnPropertyDescriptors(r2)) : B(Object(r2)).forEach(function(t4) {
              Object.defineProperty(e3, t4, Object.getOwnPropertyDescriptor(r2, t4));
            });
          }
          return e3;
        }
        var L = P.default, q = Object.keys(L), V = function(e3) {
          var t3, a3 = function(e4) {
            var t4 = e4[0] / 255, a4 = e4[1] / 255, r3 = e4[2] / 255;
            return [0.299 * t4 + 0.587 * a4 + 0.114 * r3, -0.14713 * t4 + -0.28886 * a4 + 0.436 * r3, 0.615 * t4 + -0.51499 * a4 + -0.10001 * r3];
          }(I()(e3).array()), r2 = F(a3, 3), n2 = r2[0], o2 = r2[1], s2 = r2[2], i2 = function(e4) {
            var t4, a4, r3, n3 = e4[0], o3 = e4[1], s3 = e4[2];
            return t4 = 1 * n3 + 0 * o3 + 1.13983 * s3, a4 = 1 * n3 + -0.39465 * o3 + -0.5806 * s3, r3 = 1 * n3 + 2.02311 * o3 + 0 * s3, [255 * (t4 = Math.min(Math.max(0, t4), 1)), 255 * (a4 = Math.min(Math.max(0, a4), 1)), 255 * (r3 = Math.min(Math.max(0, r3), 1))];
          }([(t3 = n2, t3 < 0.25 ? 1 : t3 < 0.5 ? 0.9 - t3 : 1.1 - t3), o2, s2]);
          return I().rgb(i2).hex();
        }, T = function(e3) {
          return function(t3) {
            return { className: [t3.className, e3.className].filter(Boolean).join(" "), style: N(N({}, t3.style || {}), e3.style || {}) };
          };
        }, K = function(t3, a3) {
          var r2 = Object.keys(a3);
          for (var n2 in t3) -1 === r2.indexOf(n2) && r2.push(n2);
          return r2.reduce(function(r3, n3) {
            return r3[n3] = function(t4, a4) {
              if (void 0 === t4) return a4;
              if (void 0 === a4) return t4;
              var r4 = e2(t4), n4 = e2(a4);
              switch (r4) {
                case "string":
                  switch (n4) {
                    case "string":
                      return [a4, t4].filter(Boolean).join(" ");
                    case "object":
                      return T({ className: t4, style: a4 });
                    case "function":
                      return function(e3) {
                        for (var r5 = arguments.length, n5 = new Array(r5 > 1 ? r5 - 1 : 0), o2 = 1; o2 < r5; o2++) n5[o2 - 1] = arguments[o2];
                        return T({ className: t4 })(a4.apply(void 0, [e3].concat(n5)));
                      };
                  }
                  break;
                case "object":
                  switch (n4) {
                    case "string":
                      return T({ className: a4, style: t4 });
                    case "object":
                      return N(N({}, a4), t4);
                    case "function":
                      return function(e3) {
                        for (var r5 = arguments.length, n5 = new Array(r5 > 1 ? r5 - 1 : 0), o2 = 1; o2 < r5; o2++) n5[o2 - 1] = arguments[o2];
                        return T({ style: t4 })(a4.apply(void 0, [e3].concat(n5)));
                      };
                  }
                  break;
                case "function":
                  switch (n4) {
                    case "string":
                      return function(e3) {
                        for (var r5 = arguments.length, n5 = new Array(r5 > 1 ? r5 - 1 : 0), o2 = 1; o2 < r5; o2++) n5[o2 - 1] = arguments[o2];
                        return t4.apply(void 0, [T(e3)({ className: a4 })].concat(n5));
                      };
                    case "object":
                      return function(e3) {
                        for (var r5 = arguments.length, n5 = new Array(r5 > 1 ? r5 - 1 : 0), o2 = 1; o2 < r5; o2++) n5[o2 - 1] = arguments[o2];
                        return t4.apply(void 0, [T(e3)({ style: a4 })].concat(n5));
                      };
                    case "function":
                      return function(e3) {
                        for (var r5 = arguments.length, n5 = new Array(r5 > 1 ? r5 - 1 : 0), o2 = 1; o2 < r5; o2++) n5[o2 - 1] = arguments[o2];
                        return t4.apply(void 0, [a4.apply(void 0, [e3].concat(n5))].concat(n5));
                      };
                  }
              }
            }(t3[n3], a3[n3]), r3;
          }, {});
        }, W = function(t3, a3) {
          for (var r2 = arguments.length, n2 = new Array(r2 > 2 ? r2 - 2 : 0), o2 = 2; o2 < r2; o2++) n2[o2 - 2] = arguments[o2];
          if (null === a3) return t3;
          Array.isArray(a3) || (a3 = [a3]);
          var s2 = a3.map(function(e3) {
            return t3[e3];
          }).filter(Boolean).reduce(function(t4, a4) {
            return "string" == typeof a4 ? t4.className = [t4.className, a4].filter(Boolean).join(" ") : "object" === e2(a4) ? t4.style = N(N({}, t4.style), a4) : "function" == typeof a4 && (t4 = N(N({}, t4), a4.apply(void 0, [t4].concat(n2)))), t4;
          }, { className: "", style: {} });
          return s2.className || delete s2.className, 0 === Object.keys(s2.style).length && delete s2.style, s2;
        }, U = function(e3) {
          return Object.keys(e3).reduce(function(t3, a3) {
            return t3[a3] = /^base/.test(a3) ? V(e3[a3]) : "scheme" === a3 ? e3[a3] + ":inverted" : e3[a3], t3;
          }, {});
        }, H = z()(function(e3) {
          var t3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, a3 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {}, r2 = t3.defaultBase16, n2 = void 0 === r2 ? L : r2, o2 = t3.base16Themes, s2 = Y(a3, void 0 === o2 ? null : o2);
          s2 && (a3 = N(N({}, s2), a3));
          for (var i2 = q.reduce(function(e4, t4) {
            return e4[t4] = a3[t4] || n2[t4], e4;
          }, {}), l2 = Object.keys(a3).reduce(function(e4, t4) {
            return -1 === q.indexOf(t4) ? (e4[t4] = a3[t4], e4) : e4;
          }, {}), c2 = e3(i2), u2 = K(l2, c2), d2 = arguments.length, b2 = new Array(d2 > 3 ? d2 - 3 : 0), p2 = 3; p2 < d2; p2++) b2[p2 - 3] = arguments[p2];
          return z()(W, 2).apply(void 0, [u2].concat(b2));
        }, 3), $ = function(e3) {
          return !!e3.extend;
        }, Y = function(e3, t3) {
          if (e3 && $(e3) && e3.extend && (e3 = e3.extend), "string" == typeof e3) {
            var a3 = F(e3.split(":"), 2), r2 = a3[0], n2 = a3[1];
            e3 = t3 ? t3[r2] : P[r2], "inverted" === n2 && (e3 = U(e3));
          }
          return e3 && Object.prototype.hasOwnProperty.call(e3, "base00") ? e3 : void 0;
        }, J = function(e3) {
          var t3 = function(e4) {
            return { backgroundColor: e4.base00, ellipsisColor: e4.base09, braceColor: e4.base07, expandedIcon: e4.base0D, collapsedIcon: e4.base0E, keyColor: e4.base07, arrayKeyColor: e4.base0C, objectSize: e4.base04, copyToClipboard: e4.base0F, copyToClipboardCheck: e4.base0D, objectBorder: e4.base02, dataTypes: { boolean: e4.base0E, date: e4.base0D, float: e4.base0B, function: e4.base0D, integer: e4.base0F, string: e4.base09, nan: e4.base08, null: e4.base0A, undefined: e4.base05, regexp: e4.base0A, background: e4.base02, bigNumber: e4.base09 }, editVariable: { editIcon: e4.base0E, cancelIcon: e4.base09, removeIcon: e4.base09, addIcon: e4.base0E, checkIcon: e4.base0E, background: e4.base01, color: e4.base0A, border: e4.base07 }, addKeyModal: { background: e4.base05, border: e4.base04, color: e4.base0A, labelColor: e4.base01 }, validationFailure: { background: e4.base09, iconColor: e4.base01, fontColor: e4.base01 } };
          }(e3);
          return { "app-container": { fontFamily: S.globalFontFamily, cursor: S.globalCursor, backgroundColor: t3.backgroundColor, position: "relative" }, ellipsis: { display: "inline-block", color: t3.ellipsisColor, fontSize: S.ellipsisFontSize, lineHeight: S.ellipsisLineHeight, cursor: S.ellipsisCursor }, "brace-row": { display: "inline-block", cursor: "pointer" }, brace: { display: "inline-block", cursor: S.braceCursor, fontWeight: S.braceFontWeight, color: t3.braceColor }, "expanded-icon": { color: t3.expandedIcon }, "collapsed-icon": { color: t3.collapsedIcon }, colon: { display: "inline-block", margin: S.keyMargin, color: t3.keyColor, verticalAlign: "top" }, objectKeyVal: function(e4, a3) {
            return { style: s({ paddingTop: S.keyValPaddingTop, paddingRight: S.keyValPaddingRight, paddingBottom: S.keyValPaddingBottom, borderLeft: S.keyValBorderLeft + " " + t3.objectBorder, ":hover": { paddingLeft: a3.paddingLeft - 1 + "px", borderLeft: S.keyValBorderHover + " " + t3.objectBorder } }, a3) };
          }, "object-key-val-no-border": { padding: S.keyValPadding }, "pushed-content": { marginLeft: S.pushedContentMarginLeft }, variableValue: function(e4, t4) {
            return { style: s({ display: "inline-block", paddingRight: S.variableValuePaddingRight, position: "relative" }, t4) };
          }, "object-name": { display: "inline-block", color: t3.keyColor, letterSpacing: S.keyLetterSpacing, fontStyle: S.keyFontStyle, verticalAlign: S.keyVerticalAlign, opacity: S.keyOpacity, ":hover": { opacity: S.keyOpacityHover } }, "array-key": { display: "inline-block", color: t3.arrayKeyColor, letterSpacing: S.keyLetterSpacing, fontStyle: S.keyFontStyle, verticalAlign: S.keyVerticalAlign, opacity: S.keyOpacity, ":hover": { opacity: S.keyOpacityHover } }, "object-size": { color: t3.objectSize, borderRadius: S.objectSizeBorderRadius, fontStyle: S.objectSizeFontStyle, margin: S.objectSizeMargin, cursor: "default" }, "data-type-label": { fontSize: S.dataTypeFontSize, marginRight: S.dataTypeMarginRight, opacity: S.datatypeOpacity }, boolean: { display: "inline-block", color: t3.dataTypes.boolean }, date: { display: "inline-block", color: t3.dataTypes.date }, "date-value": { marginLeft: S.dateValueMarginLeft }, float: { display: "inline-block", color: t3.dataTypes.float }, function: { display: "inline-block", color: t3.dataTypes.function, cursor: "pointer", whiteSpace: "pre-line" }, "function-value": { fontStyle: "italic" }, integer: { display: "inline-block", color: t3.dataTypes.integer }, bigNumber: { display: "inline-block", color: t3.dataTypes.bigNumber }, string: { display: "inline-block", color: t3.dataTypes.string }, nan: { display: "inline-block", color: t3.dataTypes.nan, fontSize: S.nanFontSize, fontWeight: S.nanFontWeight, backgroundColor: t3.dataTypes.background, padding: S.nanPadding, borderRadius: S.nanBorderRadius }, null: { display: "inline-block", color: t3.dataTypes.null, fontSize: S.nullFontSize, fontWeight: S.nullFontWeight, backgroundColor: t3.dataTypes.background, padding: S.nullPadding, borderRadius: S.nullBorderRadius }, undefined: { display: "inline-block", color: t3.dataTypes.undefined, fontSize: S.undefinedFontSize, padding: S.undefinedPadding, borderRadius: S.undefinedBorderRadius, backgroundColor: t3.dataTypes.background }, regexp: { display: "inline-block", color: t3.dataTypes.regexp }, "copy-to-clipboard": { cursor: S.clipboardCursor }, "copy-icon": { color: t3.copyToClipboard, fontSize: S.iconFontSize, marginRight: S.iconMarginRight, verticalAlign: "top" }, "copy-icon-copied": { color: t3.copyToClipboardCheck, marginLeft: S.clipboardCheckMarginLeft }, "array-group-meta-data": { display: "inline-block", padding: S.arrayGroupMetaPadding }, "object-meta-data": { display: "inline-block", padding: S.metaDataPadding }, "icon-container": { display: "inline-block", width: S.iconContainerWidth }, tooltip: { padding: S.tooltipPadding }, removeVarIcon: { verticalAlign: "top", display: "inline-block", color: t3.editVariable.removeIcon, cursor: S.iconCursor, fontSize: S.iconFontSize, marginRight: S.iconMarginRight }, addVarIcon: { verticalAlign: "top", display: "inline-block", color: t3.editVariable.addIcon, cursor: S.iconCursor, fontSize: S.iconFontSize, marginRight: S.iconMarginRight }, editVarIcon: { verticalAlign: "top", display: "inline-block", color: t3.editVariable.editIcon, cursor: S.iconCursor, fontSize: S.iconFontSize, marginRight: S.iconMarginRight }, "edit-icon-container": { display: "inline-block", verticalAlign: "top" }, "check-icon": { display: "inline-block", cursor: S.iconCursor, color: t3.editVariable.checkIcon, fontSize: S.iconFontSize, paddingRight: S.iconPaddingRight }, "cancel-icon": { display: "inline-block", cursor: S.iconCursor, color: t3.editVariable.cancelIcon, fontSize: S.iconFontSize, paddingRight: S.iconPaddingRight }, "edit-input": { display: "inline-block", minWidth: S.editInputMinWidth, borderRadius: S.editInputBorderRadius, backgroundColor: t3.editVariable.background, color: t3.editVariable.color, padding: S.editInputPadding, marginRight: S.editInputMarginRight, fontFamily: S.editInputFontFamily }, "detected-row": { paddingTop: S.detectedRowPaddingTop }, "key-modal-request": { position: S.addKeyCoverPosition, top: S.addKeyCoverPositionPx, left: S.addKeyCoverPositionPx, right: S.addKeyCoverPositionPx, bottom: S.addKeyCoverPositionPx, backgroundColor: S.addKeyCoverBackground }, "key-modal": { width: S.addKeyModalWidth, backgroundColor: t3.addKeyModal.background, marginLeft: S.addKeyModalMargin, marginRight: S.addKeyModalMargin, padding: S.addKeyModalPadding, borderRadius: S.addKeyModalRadius, marginTop: "15px", position: "relative" }, "key-modal-label": { color: t3.addKeyModal.labelColor, marginLeft: "2px", marginBottom: "5px", fontSize: "11px" }, "key-modal-input-container": { overflow: "hidden" }, "key-modal-input": { width: "100%", padding: "3px 6px", fontFamily: "monospace", color: t3.addKeyModal.color, border: "none", boxSizing: "border-box", borderRadius: "2px" }, "key-modal-cancel": { backgroundColor: t3.editVariable.removeIcon, position: "absolute", top: "0px", right: "0px", borderRadius: "0px 3px 0px 3px", cursor: "pointer" }, "key-modal-cancel-icon": { color: t3.addKeyModal.labelColor, fontSize: S.iconFontSize, transform: "rotate(45deg)" }, "key-modal-submit": { color: t3.editVariable.addIcon, fontSize: S.iconFontSize, position: "absolute", right: "2px", top: "3px", cursor: "pointer" }, "function-ellipsis": { display: "inline-block", color: t3.ellipsisColor, fontSize: S.ellipsisFontSize, lineHeight: S.ellipsisLineHeight, cursor: S.ellipsisCursor }, "validation-failure": { float: "right", padding: "3px 6px", borderRadius: "2px", cursor: "pointer", color: t3.validationFailure.fontColor, backgroundColor: t3.validationFailure.background }, "validation-failure-label": { marginRight: "6px" }, "validation-failure-clear": { position: "relative", verticalAlign: "top", cursor: "pointer", color: t3.validationFailure.iconColor, fontSize: S.iconFontSize, transform: "rotate(45deg)" } };
        };
        function G(e3, t3, a3) {
          return e3 || console.error("theme has not been set"), function(e4) {
            var t4 = O;
            return false !== e4 && "none" !== e4 || (t4 = M), H(J, { defaultBase16: t4 })(e4);
          }(e3)(t3, a3);
        }
        var Q = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = (e4.rjvId, e4.type_name), a3 = e4.displayDataTypes, r2 = e4.theme;
            return a3 ? v().createElement("span", Object.assign({ className: "data-type-label" }, G(r2, "data-type-label")), t4) : null;
          } }]);
        }(v().PureComponent), Z = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props;
            return v().createElement("div", G(e4.theme, "boolean"), v().createElement(Q, Object.assign({ type_name: "bool" }, e4)), e4.value ? "true" : "false");
          } }]);
        }(v().PureComponent), X = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props;
            return v().createElement("div", G(e4.theme, "date"), v().createElement(Q, Object.assign({ type_name: "date" }, e4)), v().createElement("span", Object.assign({ className: "date-value" }, G(e4.theme, "date-value")), e4.value.toLocaleTimeString("en-us", { weekday: "short", year: "numeric", month: "short", day: "numeric", hour: "2-digit", minute: "2-digit" })));
          } }]);
        }(v().PureComponent), ee = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props;
            return v().createElement("div", G(e4.theme, "float"), v().createElement(Q, Object.assign({ type_name: "float" }, e4)), this.props.value);
          } }]);
        }(v().PureComponent);
        function te(e3) {
          return function(e4) {
            if (Array.isArray(e4)) return _(e4);
          }(e3) || function(e4) {
            if ("undefined" != typeof Symbol && null != e4[Symbol.iterator] || null != e4["@@iterator"]) return Array.from(e4);
          }(e3) || A(e3) || function() {
            throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
          }();
        }
        var ae = r(9784), re = function() {
          return c(function e3() {
            i(this, e3), this.handler = function() {
            };
          }, [{ key: "register", value: function(e3) {
            this.handler = e3;
          } }, { key: "dispatch", value: function(e3) {
            var t3;
            null === (t3 = this.handler) || void 0 === t3 || t3.call(this, e3);
          } }]);
        }();
        globalThis.__globalDispatcherInstance || (globalThis.__globalDispatcherInstance = new re());
        const ne = globalThis.__globalDispatcherInstance;
        var oe = new (function(e3) {
          function t3() {
            var e4;
            i(this, t3);
            for (var a3 = arguments.length, r2 = new Array(a3), n2 = 0; n2 < a3; n2++) r2[n2] = arguments[n2];
            return (e4 = p(this, t3, [].concat(r2))).objects = {}, e4.set = function(t4, a4, r3, n3) {
              void 0 === e4.objects[t4] && (e4.objects[t4] = {}), void 0 === e4.objects[t4][a4] && (e4.objects[t4][a4] = {}), e4.objects[t4][a4][r3] = n3;
            }, e4.get = function(t4, a4, r3, n3) {
              return void 0 === e4.objects[t4] || void 0 === e4.objects[t4][a4] || null == e4.objects[t4][a4][r3] ? n3 : e4.objects[t4][a4][r3];
            }, e4.handleAction = function(t4) {
              var a4 = t4.rjvId, r3 = t4.data;
              switch (t4.name) {
                case "RESET":
                  e4.emit("reset-" + a4);
                  break;
                case "VARIABLE_UPDATED":
                  t4.data.updated_src = e4.updateSrc(a4, r3), e4.set(a4, "action", "variable-update", s(s({}, r3), {}, { type: "variable-edited" })), e4.emit("variable-update-" + a4);
                  break;
                case "VARIABLE_REMOVED":
                  t4.data.updated_src = e4.updateSrc(a4, r3), e4.set(a4, "action", "variable-update", s(s({}, r3), {}, { type: "variable-removed" })), e4.emit("variable-update-" + a4);
                  break;
                case "VARIABLE_ADDED":
                  t4.data.updated_src = e4.updateSrc(a4, r3), e4.set(a4, "action", "variable-update", s(s({}, r3), {}, { type: "variable-added" })), e4.emit("variable-update-" + a4);
                  break;
                case "ADD_VARIABLE_KEY_REQUEST":
                  e4.set(a4, "action", "new-key-request", r3), e4.emit("add-key-request-" + a4);
              }
            }, e4.updateSrc = function(t4, a4) {
              var r3 = a4.name, n3 = a4.namespace, o2 = a4.new_value, s2 = (a4.existing_value, a4.variable_removed);
              n3.shift();
              var i2, l2 = e4.get(t4, "global", "src"), c2 = e4.deepCopy(l2, te(n3)), u2 = c2, d2 = function(e5, t5) {
                var a5 = "undefined" != typeof Symbol && e5[Symbol.iterator] || e5["@@iterator"];
                if (!a5) {
                  if (Array.isArray(e5) || (a5 = A(e5)) || t5 && e5 && "number" == typeof e5.length) {
                    a5 && (e5 = a5);
                    var r4 = 0, n4 = function() {
                    };
                    return { s: n4, n: function() {
                      return r4 >= e5.length ? { done: true } : { done: false, value: e5[r4++] };
                    }, e: function(e6) {
                      throw e6;
                    }, f: n4 };
                  }
                  throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
                }
                var o3, s3 = true, i3 = false;
                return { s: function() {
                  a5 = a5.call(e5);
                }, n: function() {
                  var e6 = a5.next();
                  return s3 = e6.done, e6;
                }, e: function(e6) {
                  i3 = true, o3 = e6;
                }, f: function() {
                  try {
                    s3 || null == a5.return || a5.return();
                  } finally {
                    if (i3) throw o3;
                  }
                } };
              }(n3);
              try {
                for (d2.s(); !(i2 = d2.n()).done; ) {
                  u2 = u2[i2.value];
                }
              } catch (e5) {
                d2.e(e5);
              } finally {
                d2.f();
              }
              return s2 ? "array" == x(u2) ? u2.splice(r3, 1) : delete u2[r3] : null !== r3 ? u2[r3] = o2 : c2 = o2, e4.set(t4, "global", "src", c2), c2;
            }, e4.deepCopy = function(t4, a4) {
              var r3, n3 = x(t4), o2 = a4.shift();
              return "array" == n3 ? r3 = te(t4) : "object" == n3 && (r3 = s({}, t4)), void 0 !== o2 && (r3[o2] = e4.deepCopy(t4[o2], a4)), r3;
            }, e4;
          }
          return h(t3, e3), c(t3);
        }(ae.EventEmitter))();
        ne.register(oe.handleAction.bind(oe));
        const se = oe;
        var ie = function(e3) {
          function t3(e4) {
            var a3;
            return i(this, t3), (a3 = p(this, t3, [e4])).toggleCollapsed = function() {
              a3.setState({ collapsed: !a3.state.collapsed }, function() {
                se.set(a3.props.rjvId, a3.props.namespace, "collapsed", a3.state.collapsed);
              });
            }, a3.getFunctionDisplay = function(e5) {
              var t4 = a3.props;
              return e5 ? v().createElement("span", null, a3.props.value.toString().slice(9, -1).replace(/\{[\s\S]+/, ""), v().createElement("span", { className: "function-collapsed", style: { fontWeight: "bold" } }, v().createElement("span", null, "{"), v().createElement("span", G(t4.theme, "ellipsis"), "..."), v().createElement("span", null, "}"))) : a3.props.value.toString().slice(9, -1);
            }, a3.state = { collapsed: se.get(e4.rjvId, e4.namespace, "collapsed", true) }, a3;
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = this.state.collapsed;
            return v().createElement("div", G(e4.theme, "function"), v().createElement(Q, Object.assign({ type_name: "function" }, e4)), v().createElement("span", Object.assign({}, G(e4.theme, "function-value"), { className: "rjv-function-container", onClick: this.toggleCollapsed }), this.getFunctionDisplay(t4)));
          } }]);
        }(v().PureComponent), le = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            return v().createElement("div", G(this.props.theme, "nan"), "NaN");
          } }]);
        }(v().PureComponent), ce = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            return v().createElement("div", G(this.props.theme, "null"), "NULL");
          } }]);
        }(v().PureComponent), ue = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props;
            return v().createElement("div", G(e4.theme, "integer"), v().createElement(Q, Object.assign({ type_name: "int" }, e4)), this.props.value);
          } }]);
        }(v().PureComponent), de = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props;
            return v().createElement("div", G(e4.theme, "regexp"), v().createElement(Q, Object.assign({ type_name: "regexp" }, e4)), this.props.value.toString());
          } }]);
        }(v().PureComponent), be = function(e3) {
          function t3(e4) {
            var a3;
            return i(this, t3), (a3 = p(this, t3, [e4])).toggleCollapsed = function() {
              a3.setState({ collapsed: !a3.state.collapsed }, function() {
                se.set(a3.props.rjvId, a3.props.namespace, "collapsed", a3.state.collapsed);
              });
            }, a3.state = { collapsed: se.get(e4.rjvId, e4.namespace, "collapsed", true) }, a3;
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.state.collapsed, t4 = this.props, a3 = t4.collapseStringsAfterLength, r2 = t4.theme, n2 = t4.escapeStrings, o2 = t4.value, s2 = "integer" === x(a3), i2 = { style: { cursor: "default" } };
            return n2 && (o2 = C(o2)), s2 && o2.length > a3 && (i2.style.cursor = "pointer", e4 && (o2 = v().createElement("span", null, o2.substring(0, a3), v().createElement("span", G(r2, "ellipsis"), " ...")))), v().createElement("div", G(r2, "string"), v().createElement(Q, Object.assign({ type_name: "string" }, t4)), v().createElement("span", Object.assign({ className: "string-value" }, i2, { onClick: this.toggleCollapsed }), '"', o2, '"'));
          } }]);
        }(v().PureComponent), pe = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            return v().createElement("div", G(this.props.theme, "undefined"), "undefined");
          } }]);
        }(v().PureComponent), fe = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props;
            return v().createElement("div", G(e4.theme, "bigNumber"), v().createElement(Q, Object.assign({ type_name: "bigNumber" }, e4)), this.props.value.toString());
          } }]);
        }(v().PureComponent);
        function he() {
          return he = Object.assign ? Object.assign.bind() : function(e3) {
            for (var t3 = 1; t3 < arguments.length; t3++) {
              var a3 = arguments[t3];
              for (var r2 in a3) ({}).hasOwnProperty.call(a3, r2) && (e3[r2] = a3[r2]);
            }
            return e3;
          }, he.apply(null, arguments);
        }
        const me = m.useLayoutEffect;
        var ve = function(e3, t3) {
          "function" != typeof e3 ? e3.current = t3 : e3(t3);
        };
        const ge = function(e3, t3) {
          var a3 = (0, m.useRef)();
          return (0, m.useCallback)(function(r2) {
            e3.current = r2, a3.current && ve(a3.current, null), a3.current = t3, t3 && ve(t3, r2);
          }, [t3]);
        };
        var ye = { "min-height": "0", "max-height": "none", height: "0", visibility: "hidden", overflow: "hidden", position: "absolute", "z-index": "-1000", top: "0", right: "0" }, ke = function(e3) {
          Object.keys(ye).forEach(function(t3) {
            e3.style.setProperty(t3, ye[t3], "important");
          });
        }, Ee = null;
        var je = function() {
        }, we = ["borderBottomWidth", "borderLeftWidth", "borderRightWidth", "borderTopWidth", "boxSizing", "fontFamily", "fontSize", "fontStyle", "fontWeight", "letterSpacing", "lineHeight", "paddingBottom", "paddingLeft", "paddingRight", "paddingTop", "tabSize", "textIndent", "textRendering", "textTransform", "width", "wordBreak"], xe = !!document.documentElement.currentStyle, Ce = function(e3) {
          var t3, a3, r2 = (t3 = e3, a3 = m.useRef(t3), me(function() {
            a3.current = t3;
          }), a3);
          (0, m.useLayoutEffect)(function() {
            var e4 = function(e5) {
              r2.current(e5);
            };
            return window.addEventListener("resize", e4), function() {
              window.removeEventListener("resize", e4);
            };
          }, []);
        }, Oe = function(e3, t3) {
          var a3 = e3.cacheMeasurements, r2 = e3.maxRows, n2 = e3.minRows, o2 = e3.onChange, s2 = void 0 === o2 ? je : o2, i2 = e3.onHeightChange, l2 = void 0 === i2 ? je : i2, c2 = j(e3, ["cacheMeasurements", "maxRows", "minRows", "onChange", "onHeightChange"]);
          var u2 = void 0 !== c2.value, d2 = (0, m.useRef)(null), b2 = ge(d2, t3), p2 = (0, m.useRef)(0), f2 = (0, m.useRef)(), h2 = function() {
            var e4 = d2.current, t4 = a3 && f2.current ? f2.current : function(e5) {
              var t5 = window.getComputedStyle(e5);
              if (null === t5) return null;
              var a4, r3 = (a4 = t5, we.reduce(function(e6, t6) {
                return e6[t6] = a4[t6], e6;
              }, {})), n3 = r3.boxSizing;
              return "" === n3 ? null : (xe && "border-box" === n3 && (r3.width = parseFloat(r3.width) + parseFloat(r3.borderRightWidth) + parseFloat(r3.borderLeftWidth) + parseFloat(r3.paddingRight) + parseFloat(r3.paddingLeft) + "px"), { sizingStyle: r3, paddingSize: parseFloat(r3.paddingBottom) + parseFloat(r3.paddingTop), borderSize: parseFloat(r3.borderBottomWidth) + parseFloat(r3.borderTopWidth) });
            }(e4);
            if (t4) {
              f2.current = t4;
              var o3 = function(e5, t5, a4, r3) {
                void 0 === a4 && (a4 = 1), void 0 === r3 && (r3 = 1 / 0), Ee || ((Ee = document.createElement("textarea")).setAttribute("tabindex", "-1"), Ee.setAttribute("aria-hidden", "true"), ke(Ee)), null === Ee.parentNode && document.body.appendChild(Ee);
                var n3 = e5.paddingSize, o4 = e5.borderSize, s4 = e5.sizingStyle, i4 = s4.boxSizing;
                Object.keys(s4).forEach(function(e6) {
                  var t6 = e6;
                  Ee.style[t6] = s4[t6];
                }), ke(Ee), Ee.value = t5;
                var l3 = function(e6, t6) {
                  var a5 = e6.scrollHeight;
                  return "border-box" === t6.sizingStyle.boxSizing ? a5 + t6.borderSize : a5 - t6.paddingSize;
                }(Ee, e5);
                Ee.value = "x";
                var c3 = Ee.scrollHeight - n3, u3 = c3 * a4;
                "border-box" === i4 && (u3 = u3 + n3 + o4), l3 = Math.max(u3, l3);
                var d3 = c3 * r3;
                return "border-box" === i4 && (d3 = d3 + n3 + o4), [l3 = Math.min(d3, l3), c3];
              }(t4, e4.value || e4.placeholder || "x", n2, r2), s3 = o3[0], i3 = o3[1];
              p2.current !== s3 && (p2.current = s3, e4.style.setProperty("height", s3 + "px", "important"), l2(s3, { rowHeight: i3 }));
            }
          };
          return (0, m.useLayoutEffect)(h2), Ce(h2), (0, m.createElement)("textarea", he({}, c2, { onChange: function(e4) {
            u2 || h2(), s2(e4);
          }, ref: b2 }));
        };
        const Me = (0, m.forwardRef)(Oe);
        function Se(e3, t3) {
          e3 = e3.trim();
          try {
            if ("[" === (e3 = structuredClone(e3))[0]) return _e("array", JSON.parse(e3));
            if ("{" === e3[0]) return _e("object", JSON.parse(e3));
            if (e3.match(/\-?\d+\.\d+/) && e3.match(/\-?\d+\.\d+/)[0] === e3) return t3 && parseFloat(e3).toString() !== e3 ? _e("bigNumber", e3) : _e("float", parseFloat(e3));
            if (e3.match(/\-?\d+e-\d+/) && e3.match(/\-?\d+e-\d+/)[0] === e3) return _e("float", Number(e3));
            if (e3.match(/\-?\d+/) && e3.match(/\-?\d+/)[0] === e3) return t3 && parseInt(e3).toString() !== e3 ? _e("bigNumber", e3) : _e("integer", parseInt(e3));
            if (e3.match(/\-?\d+e\+\d+/) && e3.match(/\-?\d+e\+\d+/)[0] === e3) return _e("integer", Number(e3));
          } catch (e4) {
          }
          switch (e3 = e3.toLowerCase()) {
            case "undefined":
              return _e("undefined", void 0);
            case "nan":
              return _e("nan", NaN);
            case "null":
              return _e("null", null);
            case "true":
              return _e("boolean", true);
            case "false":
              return _e("boolean", false);
            default:
              if (e3 = Date.parse(e3)) return _e("date", new Date(e3));
          }
          return _e(false, null);
        }
        function _e(e3, t3) {
          return { type: e3, value: t3 };
        }
        var Ae = ["style"], Fe = ["style"], Pe = ["style"], De = ["style"], Ie = ["style"], Re = ["style"], ze = ["style"], Be = ["style"], Ne = ["style"], Le = ["style"], qe = ["style"], Ve = ["style"], Te = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, Ae);
            return v().createElement("span", a3, v().createElement("svg", Object.assign({}, et(t4), { viewBox: "0 0 24 24", fill: "currentColor", preserveAspectRatio: "xMidYMid meet" }), v().createElement("path", { d: "M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7,13H17V11H7" })));
          } }]);
        }(v().PureComponent), Ke = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, Fe);
            return v().createElement("span", a3, v().createElement("svg", Object.assign({}, et(t4), { viewBox: "0 0 24 24", fill: "currentColor", preserveAspectRatio: "xMidYMid meet" }), v().createElement("path", { d: "M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M13,7H11V11H7V13H11V17H13V13H17V11H13V7Z" })));
          } }]);
        }(v().PureComponent), We = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, Pe), r2 = et(t4).style;
            return v().createElement("span", a3, v().createElement("svg", { fill: r2.color, width: r2.height, height: r2.width, style: r2, viewBox: "0 0 1792 1792" }, v().createElement("path", { d: "M1344 800v64q0 14-9 23t-23 9h-832q-14 0-23-9t-9-23v-64q0-14 9-23t23-9h832q14 0 23 9t9 23zm128 448v-832q0-66-47-113t-113-47h-832q-66 0-113 47t-47 113v832q0 66 47 113t113 47h832q66 0 113-47t47-113zm128-832v832q0 119-84.5 203.5t-203.5 84.5h-832q-119 0-203.5-84.5t-84.5-203.5v-832q0-119 84.5-203.5t203.5-84.5h832q119 0 203.5 84.5t84.5 203.5z" })));
          } }]);
        }(v().PureComponent), Ue = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, De), r2 = et(t4).style;
            return v().createElement("span", a3, v().createElement("svg", { fill: r2.color, width: r2.height, height: r2.width, style: r2, viewBox: "0 0 1792 1792" }, v().createElement("path", { d: "M1344 800v64q0 14-9 23t-23 9h-352v352q0 14-9 23t-23 9h-64q-14 0-23-9t-9-23v-352h-352q-14 0-23-9t-9-23v-64q0-14 9-23t23-9h352v-352q0-14 9-23t23-9h64q14 0 23 9t9 23v352h352q14 0 23 9t9 23zm128 448v-832q0-66-47-113t-113-47h-832q-66 0-113 47t-47 113v832q0 66 47 113t113 47h832q66 0 113-47t47-113zm128-832v832q0 119-84.5 203.5t-203.5 84.5h-832q-119 0-203.5-84.5t-84.5-203.5v-832q0-119 84.5-203.5t203.5-84.5h832q119 0 203.5 84.5t84.5 203.5z" })));
          } }]);
        }(v().PureComponent), He = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, Ie);
            return v().createElement("span", a3, v().createElement("svg", { style: s(s({}, et(t4).style), {}, { paddingLeft: "2px", verticalAlign: "top" }), viewBox: "0 0 15 15", fill: "currentColor" }, v().createElement("path", { d: "M0 14l6-6-6-6z" })));
          } }]);
        }(v().PureComponent), $e = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, Re);
            return v().createElement("span", a3, v().createElement("svg", { style: s(s({}, et(t4).style), {}, { paddingLeft: "2px", verticalAlign: "top" }), viewBox: "0 0 15 15", fill: "currentColor" }, v().createElement("path", { d: "M0 5l6 6 6-6z" })));
          } }]);
        }(v().PureComponent), Ye = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, ze);
            return v().createElement("span", a3, v().createElement("svg", Object.assign({}, et(t4), { viewBox: "0 0 40 40", fill: "currentColor", preserveAspectRatio: "xMidYMid meet" }), v().createElement("g", null, v().createElement("path", { d: "m30 35h-25v-22.5h25v7.5h2.5v-12.5c0-1.4-1.1-2.5-2.5-2.5h-7.5c0-2.8-2.2-5-5-5s-5 2.2-5 5h-7.5c-1.4 0-2.5 1.1-2.5 2.5v27.5c0 1.4 1.1 2.5 2.5 2.5h25c1.4 0 2.5-1.1 2.5-2.5v-5h-2.5v5z m-20-27.5h2.5s2.5-1.1 2.5-2.5 1.1-2.5 2.5-2.5 2.5 1.1 2.5 2.5 1.3 2.5 2.5 2.5h2.5s2.5 1.1 2.5 2.5h-20c0-1.5 1.1-2.5 2.5-2.5z m-2.5 20h5v-2.5h-5v2.5z m17.5-5v-5l-10 7.5 10 7.5v-5h12.5v-5h-12.5z m-17.5 10h7.5v-2.5h-7.5v2.5z m12.5-17.5h-12.5v2.5h12.5v-2.5z m-7.5 5h-5v2.5h5v-2.5z" }))));
          } }]);
        }(v().PureComponent), Je = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, Be);
            return v().createElement("span", a3, v().createElement("svg", Object.assign({}, et(t4), { viewBox: "0 0 40 40", fill: "currentColor", preserveAspectRatio: "xMidYMid meet" }), v().createElement("g", null, v().createElement("path", { d: "m28.6 25q0-0.5-0.4-1l-4-4 4-4q0.4-0.5 0.4-1 0-0.6-0.4-1.1l-2-2q-0.4-0.4-1-0.4-0.6 0-1 0.4l-4.1 4.1-4-4.1q-0.4-0.4-1-0.4-0.6 0-1 0.4l-2 2q-0.5 0.5-0.5 1.1 0 0.5 0.5 1l4 4-4 4q-0.5 0.5-0.5 1 0 0.7 0.5 1.1l2 2q0.4 0.4 1 0.4 0.6 0 1-0.4l4-4.1 4.1 4.1q0.4 0.4 1 0.4 0.6 0 1-0.4l2-2q0.4-0.4 0.4-1z m8.7-5q0 4.7-2.3 8.6t-6.3 6.2-8.6 2.3-8.6-2.3-6.2-6.2-2.3-8.6 2.3-8.6 6.2-6.2 8.6-2.3 8.6 2.3 6.3 6.2 2.3 8.6z" }))));
          } }]);
        }(v().PureComponent), Ge = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, Ne);
            return v().createElement("span", a3, v().createElement("svg", Object.assign({}, et(t4), { viewBox: "0 0 40 40", fill: "currentColor", preserveAspectRatio: "xMidYMid meet" }), v().createElement("g", null, v().createElement("path", { d: "m30.1 21.4v-2.8q0-0.6-0.4-1t-1-0.5h-5.7v-5.7q0-0.6-0.4-1t-1-0.4h-2.9q-0.6 0-1 0.4t-0.4 1v5.7h-5.7q-0.6 0-1 0.5t-0.5 1v2.8q0 0.6 0.5 1t1 0.5h5.7v5.7q0 0.5 0.4 1t1 0.4h2.9q0.6 0 1-0.4t0.4-1v-5.7h5.7q0.6 0 1-0.5t0.4-1z m7.2-1.4q0 4.7-2.3 8.6t-6.3 6.2-8.6 2.3-8.6-2.3-6.2-6.2-2.3-8.6 2.3-8.6 6.2-6.2 8.6-2.3 8.6 2.3 6.3 6.2 2.3 8.6z" }))));
          } }]);
        }(v().PureComponent), Qe = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, Le);
            return v().createElement("span", a3, v().createElement("svg", Object.assign({}, et(t4), { viewBox: "0 0 40 40", fill: "currentColor", preserveAspectRatio: "xMidYMid meet" }), v().createElement("g", null, v().createElement("path", { d: "m31.6 21.6h-10v10h-3.2v-10h-10v-3.2h10v-10h3.2v10h10v3.2z" }))));
          } }]);
        }(v().PureComponent), Ze = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, qe);
            return v().createElement("span", a3, v().createElement("svg", Object.assign({}, et(t4), { viewBox: "0 0 40 40", fill: "currentColor", preserveAspectRatio: "xMidYMid meet" }), v().createElement("g", null, v().createElement("path", { d: "m19.8 26.4l2.6-2.6-3.4-3.4-2.6 2.6v1.3h2.2v2.1h1.2z m9.8-16q-0.3-0.4-0.7 0l-7.8 7.8q-0.4 0.4 0 0.7t0.7 0l7.8-7.8q0.4-0.4 0-0.7z m1.8 13.2v4.3q0 2.6-1.9 4.5t-4.5 1.9h-18.6q-2.6 0-4.5-1.9t-1.9-4.5v-18.6q0-2.7 1.9-4.6t4.5-1.8h18.6q1.4 0 2.6 0.5 0.3 0.2 0.4 0.5 0.1 0.4-0.2 0.7l-1.1 1.1q-0.3 0.3-0.7 0.1-0.5-0.1-1-0.1h-18.6q-1.4 0-2.5 1.1t-1 2.5v18.6q0 1.4 1 2.5t2.5 1h18.6q1.5 0 2.5-1t1.1-2.5v-2.9q0-0.2 0.2-0.4l1.4-1.5q0.3-0.3 0.8-0.1t0.4 0.6z m-2.1-16.5l6.4 6.5-15 15h-6.4v-6.5z m9.9 3l-2.1 2-6.4-6.4 2.1-2q0.6-0.7 1.5-0.7t1.5 0.7l3.4 3.4q0.6 0.6 0.6 1.5t-0.6 1.5z" }))));
          } }]);
        }(v().PureComponent), Xe = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.style, a3 = w(e4, Ve);
            return v().createElement("span", a3, v().createElement("svg", Object.assign({}, et(t4), { viewBox: "0 0 40 40", fill: "currentColor", preserveAspectRatio: "xMidYMid meet" }), v().createElement("g", null, v().createElement("path", { d: "m31.7 16.4q0-0.6-0.4-1l-2.1-2.1q-0.4-0.4-1-0.4t-1 0.4l-9.1 9.1-5-5q-0.5-0.4-1-0.4t-1 0.4l-2.1 2q-0.4 0.4-0.4 1 0 0.6 0.4 1l8.1 8.1q0.4 0.4 1 0.4 0.6 0 1-0.4l12.2-12.1q0.4-0.4 0.4-1z m5.6 3.6q0 4.7-2.3 8.6t-6.3 6.2-8.6 2.3-8.6-2.3-6.2-6.2-2.3-8.6 2.3-8.6 6.2-6.2 8.6-2.3 8.6 2.3 6.3 6.2 2.3 8.6z" }))));
          } }]);
        }(v().PureComponent);
        function et(e3) {
          return e3 || (e3 = {}), { style: s(s({ verticalAlign: "middle" }, e3), {}, { color: e3.color ? e3.color : "#000000", height: "1em", width: "1em" }) };
        }
        var tt = function(e3) {
          function t3(e4) {
            var a3;
            return i(this, t3), (a3 = p(this, t3, [e4])).copiedTimer = null, a3.copyToClipboardFallback = function(e5) {
              var t4 = document.createElement("textarea");
              t4.value = e5, document.body.appendChild(t4), t4.select(), document.execCommand("copy"), document.body.removeChild(t4);
            }, a3.handleCopy = function() {
              var e5 = a3.props, t4 = e5.clickCallback, r2 = e5.src, n2 = e5.namespace, o2 = JSON.stringify(a3.clipboardValue(r2), null, "  ");
              navigator.clipboard ? navigator.clipboard.writeText(o2).catch(function() {
                a3.copyToClipboardFallback(o2);
              }) : a3.copyToClipboardFallback(o2), a3.copiedTimer = setTimeout(function() {
                a3.setState({ copied: false });
              }, 5500), a3.setState({ copied: true }, function() {
                "function" == typeof t4 && t4({ src: r2, namespace: n2, name: n2[n2.length - 1] });
              });
            }, a3.getClippyIcon = function() {
              var e5 = a3.props.theme;
              return a3.state.copied ? v().createElement("span", null, v().createElement(Ye, Object.assign({ className: "copy-icon" }, G(e5, "copy-icon"))), v().createElement("span", G(e5, "copy-icon-copied"), "✔")) : v().createElement(Ye, Object.assign({ className: "copy-icon" }, G(e5, "copy-icon")));
            }, a3.clipboardValue = function(e5) {
              switch (x(e5)) {
                case "function":
                case "regexp":
                  return e5.toString();
                default:
                  return e5;
              }
            }, a3.state = { copied: false }, a3;
          }
          return h(t3, e3), c(t3, [{ key: "componentWillUnmount", value: function() {
            this.copiedTimer && (clearTimeout(this.copiedTimer), this.copiedTimer = null);
          } }, { key: "render", value: function() {
            var e4 = this.props, t4 = (e4.src, e4.theme), a3 = e4.hidden, r2 = e4.rowHovered, n2 = G(t4, "copy-to-clipboard").style, o2 = "inline";
            return a3 && (o2 = "none"), v().createElement("span", { className: "copy-to-clipboard-container", title: "Copy to clipboard", style: { verticalAlign: "top", display: r2 ? "inline-block" : "none" } }, v().createElement("span", { style: s(s({}, n2), {}, { display: o2 }), onClick: this.handleCopy }, this.getClippyIcon()));
          } }]);
        }(v().PureComponent);
        const at = function(e3) {
          function t3(e4) {
            var a3;
            return i(this, t3), (a3 = p(this, t3, [e4])).getEditIcon = function() {
              var e5 = a3.props, t4 = e5.variable, r2 = e5.theme;
              return v().createElement("div", { className: "click-to-edit", style: { verticalAlign: "top", display: a3.state.hovered ? "inline-block" : "none" } }, v().createElement(Ze, Object.assign({ className: "click-to-edit-icon" }, G(r2, "editVarIcon"), { onClick: function() {
                a3.prepopInput(t4);
              } })));
            }, a3.prepopInput = function(e5) {
              if (false !== a3.props.onEdit) {
                var t4 = function(e6, t5) {
                  var a4;
                  switch (x(e6, t5)) {
                    case "undefined":
                      a4 = "undefined";
                      break;
                    case "nan":
                      a4 = "NaN";
                      break;
                    case "string":
                      a4 = e6;
                      break;
                    case "bigNumber":
                    case "date":
                    case "function":
                    case "regexp":
                      a4 = e6.toString();
                      break;
                    default:
                      try {
                        a4 = JSON.stringify(e6, null, "  ");
                      } catch (e7) {
                        a4 = "";
                      }
                  }
                  return a4;
                }(e5.value, a3.props.bigNumber), r2 = Se(t4, a3.props.bigNumber);
                a3.setState({ editMode: true, editValue: t4, parsedInput: { type: r2.type, value: r2.value } });
              }
            }, a3.getRemoveIcon = function() {
              var e5 = a3.props, t4 = e5.variable, r2 = e5.namespace, n2 = e5.theme, o2 = e5.rjvId;
              return v().createElement("div", { className: "click-to-remove", style: { verticalAlign: "top", display: a3.state.hovered ? "inline-block" : "none" } }, v().createElement(Je, Object.assign({ className: "click-to-remove-icon" }, G(n2, "removeVarIcon"), { onClick: function() {
                ne.dispatch({ name: "VARIABLE_REMOVED", rjvId: o2, data: { name: t4.name, namespace: r2, existing_value: t4.value, variable_removed: true } });
              } })));
            }, a3.getValue = function(e5, t4) {
              var r2 = !t4 && e5.type, n2 = a3.props;
              switch (r2) {
                case false:
                  return a3.getEditInput();
                case "string":
                  return v().createElement(be, Object.assign({ value: e5.value }, n2));
                case "integer":
                  return v().createElement(ue, Object.assign({ value: e5.value }, n2));
                case "float":
                  return v().createElement(ee, Object.assign({ value: e5.value }, n2));
                case "boolean":
                  return v().createElement(Z, Object.assign({ value: e5.value }, n2));
                case "function":
                  return v().createElement(ie, Object.assign({ value: e5.value }, n2));
                case "null":
                  return v().createElement(ce, n2);
                case "nan":
                  return v().createElement(le, n2);
                case "undefined":
                  return v().createElement(pe, n2);
                case "date":
                  return v().createElement(X, Object.assign({ value: e5.value }, n2));
                case "regexp":
                  return v().createElement(de, Object.assign({ value: e5.value }, n2));
                case "bigNumber":
                  return v().createElement(fe, Object.assign({ value: e5.value }, n2));
                default:
                  return v().createElement("div", { className: "object-value" }, JSON.stringify(e5.value));
              }
            }, a3.getEditInput = function() {
              var e5 = a3.props, t4 = e5.keyModifier, r2 = e5.selectOnFocus, n2 = e5.theme, o2 = a3.state.editValue;
              return v().createElement("div", null, v().createElement(Me, Object.assign({ type: "text", ref: function(e6) {
                e6 && e6[r2 ? "select" : "focus"]();
              }, value: o2, className: "variable-editor", onChange: function(e6) {
                var t5 = e6.target.value, r3 = Se(t5, a3.props.bigNumber);
                a3.setState({ editValue: t5, parsedInput: { type: r3.type, value: r3.value } });
              }, onKeyDown: function(e6) {
                switch (e6.key) {
                  case "Escape":
                    a3.setState({ editMode: false, editValue: "" });
                    break;
                  case "Enter":
                    t4(e6, "submit") && a3.submitEdit(true);
                }
                e6.stopPropagation();
              }, placeholder: "update this value", minRows: 2 }, G(n2, "edit-input"))), v().createElement("div", G(n2, "edit-icon-container"), v().createElement(Je, Object.assign({ className: "edit-cancel" }, G(n2, "cancel-icon"), { onClick: function(e6) {
                e6 && e6.stopPropagation(), a3.setState({ editMode: false, editValue: "" });
              } })), v().createElement(Xe, Object.assign({ className: "edit-check string-value" }, G(n2, "check-icon"), { onClick: function(e6) {
                e6 && e6.stopPropagation(), a3.submitEdit();
              } })), v().createElement("div", null, a3.showDetected())));
            }, a3.submitEdit = function(e5) {
              var t4 = a3.props, r2 = t4.variable, n2 = t4.namespace, o2 = t4.rjvId, s2 = t4.bigNumber, i2 = a3.state, l2 = i2.editValue, c2 = i2.parsedInput, u2 = l2;
              e5 && c2.type && (u2 = c2.value, s2 && "bigNumber" === c2.type && (u2 = new s2(u2))), a3.setState({ editMode: false }), ne.dispatch({ name: "VARIABLE_UPDATED", rjvId: o2, data: { name: r2.name, namespace: n2, existing_value: r2.value, new_value: u2, variable_removed: false } });
            }, a3.showDetected = function() {
              var e5 = a3.props, t4 = e5.theme, r2 = (e5.variable, e5.namespace, e5.rjvId, a3.state.parsedInput), n2 = (r2.type, r2.value, a3.getDetectedInput());
              if (n2) return v().createElement("div", null, v().createElement("div", G(t4, "detected-row"), n2, v().createElement(Xe, { className: "edit-check detected", style: s({ verticalAlign: "top", paddingLeft: "3px" }, G(t4, "check-icon").style), onClick: function(e6) {
                e6 && e6.stopPropagation(), a3.submitEdit(true);
              } })));
            }, a3.getDetectedInput = function() {
              var e5 = a3.state.parsedInput, t4 = e5.type, r2 = e5.value, n2 = a3.props, o2 = n2.theme;
              if (false !== t4) switch (t4.toLowerCase()) {
                case "object":
                  return v().createElement("span", null, v().createElement("span", { style: s(s({}, G(o2, "brace").style), {}, { cursor: "default" }) }, "{"), v().createElement("span", { style: s(s({}, G(o2, "ellipsis").style), {}, { cursor: "default" }) }, "..."), v().createElement("span", { style: s(s({}, G(o2, "brace").style), {}, { cursor: "default" }) }, "}"));
                case "array":
                  return v().createElement("span", null, v().createElement("span", { style: s(s({}, G(o2, "brace").style), {}, { cursor: "default" }) }, "["), v().createElement("span", { style: s(s({}, G(o2, "ellipsis").style), {}, { cursor: "default" }) }, "..."), v().createElement("span", { style: s(s({}, G(o2, "brace").style), {}, { cursor: "default" }) }, "]"));
                case "string":
                  return v().createElement(be, Object.assign({ value: r2 }, n2));
                case "integer":
                  return v().createElement(ue, Object.assign({ value: r2 }, n2));
                case "float":
                  return v().createElement(ee, Object.assign({ value: r2 }, n2));
                case "boolean":
                  return v().createElement(Z, Object.assign({ value: r2 }, n2));
                case "function":
                  return v().createElement(ie, Object.assign({ value: r2 }, n2));
                case "null":
                  return v().createElement(ce, n2);
                case "nan":
                  return v().createElement(le, n2);
                case "undefined":
                  return v().createElement(pe, n2);
                case "date":
                  return v().createElement(X, Object.assign({ value: new Date(r2) }, n2));
                case "bignumber":
                  return v().createElement(fe, Object.assign({ value: r2 }, n2));
              }
            }, a3.state = { editMode: false, editValue: "", hovered: false, renameKey: false, parsedInput: { type: false, value: null } }, a3;
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this, t4 = this.props, a3 = t4.variable, r2 = t4.singleIndent, n2 = t4.type, o2 = t4.theme, i2 = t4.namespace, l2 = t4.indentWidth, c2 = t4.enableClipboard, u2 = t4.onEdit, d2 = t4.onDelete, b2 = t4.onSelect, p2 = t4.displayArrayKey, f2 = t4.quotesOnKeys, h2 = t4.keyModifier, m2 = this.state.editMode;
            return v().createElement("div", Object.assign({}, G(o2, "objectKeyVal", { paddingLeft: l2 * r2 }), { onMouseEnter: function() {
              return e4.setState(s(s({}, e4.state), {}, { hovered: true }));
            }, onMouseLeave: function() {
              return e4.setState(s(s({}, e4.state), {}, { hovered: false }));
            }, className: "variable-row", key: a3.name }), "array" == n2 ? p2 ? v().createElement("span", Object.assign({}, G(o2, "array-key"), { key: a3.name + "_" + i2 }), a3.name, v().createElement("div", G(o2, "colon"), ":")) : null : v().createElement("span", null, v().createElement("span", Object.assign({}, G(o2, "object-name"), { className: "object-key", key: a3.name + "_" + i2 }), !!f2 && v().createElement("span", { style: { verticalAlign: "top" } }, '"'), v().createElement("span", { style: { display: "inline-block" } }, C(a3.name)), !!f2 && v().createElement("span", { style: { verticalAlign: "top" } }, '"')), v().createElement("span", G(o2, "colon"), ":")), v().createElement("div", Object.assign({ className: "variable-value", onClick: false === b2 && false === u2 ? null : function(t5) {
              var r3 = te(i2);
              h2(t5, "edit") && false !== u2 ? e4.prepopInput(a3) : false !== b2 && (r3.shift(), b2(s(s({}, a3), {}, { namespace: r3 })));
            } }, G(o2, "variableValue", { cursor: false === b2 ? "default" : "pointer" })), this.getValue(a3, m2)), c2 ? v().createElement(tt, { rowHovered: this.state.hovered, hidden: m2, src: a3.value, clickCallback: c2, theme: o2, namespace: [].concat(te(i2), [a3.name]) }) : null, false !== u2 && 0 == m2 ? this.getEditIcon() : null, false !== d2 && 0 == m2 ? this.getRemoveIcon() : null);
          } }]);
        }(v().PureComponent);
        var rt = function(e3) {
          function t3() {
            var e4;
            i(this, t3);
            for (var a3 = arguments.length, r2 = new Array(a3), n2 = 0; n2 < a3; n2++) r2[n2] = arguments[n2];
            return (e4 = p(this, t3, [].concat(r2))).getObjectSize = function() {
              var t4 = e4.props, a4 = t4.size, r3 = t4.theme;
              if (t4.displayObjectSize) return v().createElement("span", Object.assign({ className: "object-size" }, G(r3, "object-size")), a4, " item", 1 === a4 ? "" : "s");
            }, e4.getAddAttribute = function(t4) {
              var a4 = e4.props, r3 = a4.theme, n3 = a4.namespace, o2 = a4.name, i2 = a4.src, l2 = a4.rjvId, c2 = a4.depth;
              return v().createElement("span", { className: "click-to-add", style: { verticalAlign: "top", display: t4 ? "inline-block" : "none" } }, v().createElement(Ge, Object.assign({ className: "click-to-add-icon" }, G(r3, "addVarIcon"), { onClick: function() {
                var e5 = { name: c2 > 0 ? o2 : null, namespace: n3.splice(0, n3.length - 1), existing_value: i2, variable_removed: false, key_name: null };
                "object" === x(i2) ? ne.dispatch({ name: "ADD_VARIABLE_KEY_REQUEST", rjvId: l2, data: e5 }) : ne.dispatch({ name: "VARIABLE_ADDED", rjvId: l2, data: s(s({}, e5), {}, { new_value: [].concat(te(i2), [null]) }) });
              } })));
            }, e4.getRemoveObject = function(t4) {
              var a4 = e4.props, r3 = a4.theme, n3 = (a4.hover, a4.namespace), o2 = a4.name, s2 = a4.src, i2 = a4.rjvId;
              if (1 !== n3.length) return v().createElement("span", { className: "click-to-remove", style: { display: t4 ? "inline-block" : "none" } }, v().createElement(Je, Object.assign({ className: "click-to-remove-icon" }, G(r3, "removeVarIcon"), { onClick: function() {
                ne.dispatch({ name: "VARIABLE_REMOVED", rjvId: i2, data: { name: o2, namespace: n3.splice(0, n3.length - 1), existing_value: s2, variable_removed: true } });
              } })));
            }, e4.render = function() {
              var t4 = e4.props, a4 = t4.theme, r3 = t4.onDelete, n3 = t4.onAdd, o2 = t4.enableClipboard, s2 = t4.src, i2 = t4.namespace, l2 = t4.rowHovered;
              return v().createElement("div", Object.assign({}, G(a4, "object-meta-data"), { className: "object-meta-data", onClick: function(e5) {
                e5.stopPropagation();
              } }), e4.getObjectSize(), o2 ? v().createElement(tt, { rowHovered: l2, clickCallback: o2, src: s2, theme: a4, namespace: i2 }) : null, false !== n3 ? e4.getAddAttribute(l2) : null, false !== r3 ? e4.getRemoveObject(l2) : null);
            }, e4;
          }
          return h(t3, e3), c(t3);
        }(v().PureComponent);
        function nt(e3) {
          var t3 = e3.parent_type, a3 = e3.namespace, r2 = e3.quotesOnKeys, n2 = e3.theme, o2 = e3.jsvRoot, s2 = e3.name, i2 = e3.displayArrayKey, l2 = e3.name ? e3.name : "";
          return !o2 || false !== s2 && null !== s2 ? "array" == t3 ? i2 ? v().createElement("span", Object.assign({}, G(n2, "array-key"), { key: a3 }), v().createElement("span", { className: "array-key" }, l2), v().createElement("span", G(n2, "colon"), ":")) : v().createElement("span", null) : v().createElement("span", Object.assign({}, G(n2, "object-name"), { key: a3 }), v().createElement("span", { className: "object-key" }, r2 && v().createElement("span", { style: { verticalAlign: "top" } }, '"'), v().createElement("span", null, l2), r2 && v().createElement("span", { style: { verticalAlign: "top" } }, '"')), v().createElement("span", G(n2, "colon"), ":")) : v().createElement("span", null);
        }
        function ot(e3) {
          var t3 = e3.theme;
          switch (e3.iconStyle) {
            case "triangle":
              return v().createElement($e, Object.assign({}, G(t3, "expanded-icon"), { className: "expanded-icon" }));
            case "square":
              return v().createElement(We, Object.assign({}, G(t3, "expanded-icon"), { className: "expanded-icon" }));
            default:
              return v().createElement(Te, Object.assign({}, G(t3, "expanded-icon"), { className: "expanded-icon" }));
          }
        }
        function st(e3) {
          var t3 = e3.theme;
          switch (e3.iconStyle) {
            case "triangle":
              return v().createElement(He, Object.assign({}, G(t3, "collapsed-icon"), { className: "collapsed-icon" }));
            case "square":
              return v().createElement(Ue, Object.assign({}, G(t3, "collapsed-icon"), { className: "collapsed-icon" }));
            default:
              return v().createElement(Ke, Object.assign({}, G(t3, "collapsed-icon"), { className: "collapsed-icon" }));
          }
        }
        var it = ["src", "groupArraysAfterLength", "depth", "name", "theme", "jsvRoot", "namespace", "parent_type"], lt = function(e3) {
          function t3(e4) {
            var a3;
            return i(this, t3), (a3 = p(this, t3, [e4])).toggleCollapsed = function(e5) {
              var t4 = [];
              for (var r2 in a3.state.expanded) t4.push(a3.state.expanded[r2]);
              t4[e5] = !t4[e5], a3.setState({ expanded: t4 });
            }, a3.state = { expanded: [] }, a3;
          }
          return h(t3, e3), c(t3, [{ key: "getExpandedIcon", value: function(e4) {
            var t4 = this.props, a3 = t4.theme, r2 = t4.iconStyle;
            return this.state.expanded[e4] ? v().createElement(ot, { theme: a3, iconStyle: r2 }) : v().createElement(st, { theme: a3, iconStyle: r2 });
          } }, { key: "render", value: function() {
            var e4 = this, t4 = this.props, a3 = t4.src, r2 = t4.groupArraysAfterLength, n2 = (t4.depth, t4.name), o2 = t4.theme, s2 = t4.jsvRoot, i2 = t4.namespace, l2 = (t4.parent_type, w(t4, it)), c2 = 0, u2 = 5 * this.props.indentWidth;
            s2 || (c2 = 5 * this.props.indentWidth);
            var d2 = r2, b2 = Math.ceil(a3.length / d2);
            return v().createElement("div", Object.assign({ className: "object-key-val" }, G(o2, s2 ? "jsv-root" : "objectKeyVal", { paddingLeft: c2 })), v().createElement(nt, this.props), v().createElement("span", null, v().createElement(rt, Object.assign({ size: a3.length }, this.props))), te(Array(b2)).map(function(t5, r3) {
              return v().createElement("div", Object.assign({ key: r3, className: "object-key-val array-group" }, G(o2, "objectKeyVal", { marginLeft: 6, paddingLeft: u2 })), v().createElement("span", G(o2, "brace-row"), v().createElement("div", Object.assign({ className: "icon-container" }, G(o2, "icon-container"), { onClick: function(t6) {
                e4.toggleCollapsed(r3);
              } }), e4.getExpandedIcon(r3)), e4.state.expanded[r3] ? v().createElement(bt, Object.assign({ key: n2 + r3, depth: 0, name: false, collapsed: false, groupArraysAfterLength: d2, index_offset: r3 * d2, src: a3.slice(r3 * d2, r3 * d2 + d2), namespace: i2, type: "array", parent_type: "array_group", theme: o2 }, l2)) : v().createElement("span", Object.assign({}, G(o2, "brace"), { onClick: function(t6) {
                e4.toggleCollapsed(r3);
              }, className: "array-group-brace" }), "[", v().createElement("div", Object.assign({}, G(o2, "array-group-meta-data"), { className: "array-group-meta-data" }), v().createElement("span", Object.assign({ className: "object-size" }, G(o2, "object-size")), r3 * d2, " - ", r3 * d2 + d2 > a3.length ? a3.length : r3 * d2 + d2)), "]")));
            }));
          } }]);
        }(v().PureComponent), ct = ["depth", "src", "namespace", "name", "type", "parent_type", "theme", "jsvRoot", "iconStyle"], ut = function(e3) {
          function t3(e4) {
            var a3;
            i(this, t3), (a3 = p(this, t3, [e4])).toggleCollapsed = function() {
              a3.setState({ expanded: !a3.state.expanded }, function() {
                se.set(a3.props.rjvId, a3.props.namespace, "expanded", a3.state.expanded);
              });
            }, a3.getObjectContent = function(e5, t4, r3) {
              return v().createElement("div", { className: "pushed-content object-container" }, v().createElement("div", Object.assign({ className: "object-content" }, G(a3.props.theme, "pushed-content")), a3.renderObjectContents(t4, r3)));
            }, a3.getEllipsis = function() {
              return 0 === a3.state.size ? null : v().createElement("div", Object.assign({}, G(a3.props.theme, "ellipsis"), { className: "node-ellipsis", onClick: a3.toggleCollapsed }), "...");
            }, a3.getObjectMetaData = function(e5) {
              var t4 = a3.props, r3 = (t4.rjvId, t4.theme, a3.state), n2 = r3.size, o2 = r3.hovered;
              return v().createElement(rt, Object.assign({ rowHovered: o2, size: n2 }, a3.props));
            }, a3.renderObjectContents = function(e5, t4) {
              var r3, n2 = a3.props, o2 = n2.depth, s2 = n2.parent_type, i2 = n2.index_offset, l2 = n2.groupArraysAfterLength, c2 = n2.namespace, u2 = a3.state.object_type, d2 = [], b2 = Object.keys(e5 || {});
              return a3.props.sortKeys && "array" !== u2 && (b2 = b2.sort()), b2.forEach(function(n3) {
                if (r3 = new dt(n3, e5[n3], t4.bigNumber), "array_group" === s2 && i2 && (r3.name = parseInt(r3.name) + i2), Object.prototype.hasOwnProperty.call(e5, n3)) if ("object" === r3.type) d2.push(v().createElement(bt, Object.assign({ key: r3.name, depth: o2 + 1, name: r3.name, src: r3.value, namespace: c2.concat(r3.name), parent_type: u2 }, t4)));
                else if ("array" === r3.type) {
                  var b3 = bt;
                  l2 && r3.value.length > l2 && (b3 = lt), d2.push(v().createElement(b3, Object.assign({ key: r3.name, depth: o2 + 1, name: r3.name, src: r3.value, namespace: c2.concat(r3.name), type: "array", parent_type: u2 }, t4)));
                } else d2.push(v().createElement(at, Object.assign({ key: r3.name + "_" + c2, variable: r3, singleIndent: 5, namespace: c2, type: a3.props.type }, t4)));
                else ;
              }), d2;
            };
            var r2 = t3.getState(e4);
            return a3.state = s(s({}, r2), {}, { prevProps: {} }), a3;
          }
          return h(t3, e3), c(t3, [{ key: "getBraceStart", value: function(e4, t4) {
            var a3 = this, r2 = this.props, n2 = r2.src, o2 = r2.theme, s2 = r2.iconStyle;
            if ("array_group" === r2.parent_type) return v().createElement("span", null, v().createElement("span", G(o2, "brace"), "array" === e4 ? "[" : "{"), t4 ? this.getObjectMetaData(n2) : null);
            var i2 = t4 ? ot : st;
            return v().createElement("span", null, v().createElement("span", Object.assign({ onClick: function(e5) {
              a3.toggleCollapsed();
            } }, G(o2, "brace-row")), v().createElement("div", Object.assign({ className: "icon-container" }, G(o2, "icon-container")), v().createElement(i2, { theme: o2, iconStyle: s2 })), v().createElement(nt, this.props), v().createElement("span", G(o2, "brace"), "array" === e4 ? "[" : "{")), t4 ? this.getObjectMetaData(n2) : null);
          } }, { key: "render", value: function() {
            var e4 = this, t4 = this.props, a3 = t4.depth, r2 = t4.src, n2 = (t4.namespace, t4.name, t4.type, t4.parent_type), o2 = t4.theme, i2 = t4.jsvRoot, l2 = t4.iconStyle, c2 = w(t4, ct), u2 = this.state, d2 = u2.object_type, b2 = u2.expanded, p2 = {};
            return i2 || "array_group" === n2 ? "array_group" === n2 && (p2.borderLeft = 0, p2.display = "inline") : p2.paddingLeft = 5 * this.props.indentWidth, v().createElement("div", Object.assign({ className: "object-key-val", onMouseEnter: function() {
              return e4.setState(s(s({}, e4.state), {}, { hovered: true }));
            }, onMouseLeave: function() {
              return e4.setState(s(s({}, e4.state), {}, { hovered: false }));
            } }, G(o2, i2 ? "jsv-root" : "objectKeyVal", p2)), this.getBraceStart(d2, b2), b2 ? this.getObjectContent(a3, r2, s({ theme: o2, iconStyle: l2 }, c2)) : this.getEllipsis(), v().createElement("span", { className: "brace-row" }, v().createElement("span", { style: s(s({}, G(o2, "brace").style), {}, { paddingLeft: b2 ? "3px" : "0px" }) }, "array" === d2 ? "]" : "}"), b2 ? null : this.getObjectMetaData(r2)));
          } }], [{ key: "getDerivedStateFromProps", value: function(e4, a3) {
            var r2 = a3.prevProps;
            return e4.src !== r2.src || e4.collapsed !== r2.collapsed || e4.name !== r2.name || e4.namespace !== r2.namespace || e4.rjvId !== r2.rjvId ? s(s({}, t3.getState(e4)), {}, { prevProps: e4 }) : null;
          } }]);
        }(v().PureComponent);
        ut.getState = function(e3) {
          var t3 = Object.keys(e3.src).length, a3 = (false === e3.collapsed || true !== e3.collapsed && e3.collapsed > e3.depth) && (!e3.shouldCollapse || false === e3.shouldCollapse({ name: e3.name, src: e3.src, type: x(e3.src), namespace: e3.namespace })) && 0 !== t3;
          return { expanded: se.get(e3.rjvId, e3.namespace, "expanded", a3), object_type: "array" === e3.type ? "array" : "object", parent_type: "array" === e3.type ? "array" : "object", size: t3, hovered: false };
        };
        var dt = c(function e3(t3, a3, r2) {
          i(this, e3), this.name = t3, this.value = a3, this.type = x(a3, r2);
        });
        E(ut);
        const bt = ut;
        var pt = function(e3) {
          function t3() {
            var e4;
            i(this, t3);
            for (var a3 = arguments.length, r2 = new Array(a3), n2 = 0; n2 < a3; n2++) r2[n2] = arguments[n2];
            return (e4 = p(this, t3, [].concat(r2))).render = function() {
              var t4, a4, r3, n3, o2 = e4.props, s2 = [o2.name], i2 = bt;
              "object" != typeof o2.name || Array.isArray(o2.name) || (s2 = [(null === (t4 = o2.name) || void 0 === t4 ? void 0 : t4.displayName) || (null === (a4 = o2.name) || void 0 === a4 ? void 0 : a4.name) || (null === (r3 = o2.name) || void 0 === r3 || null === (n3 = r3.type) || void 0 === n3 ? void 0 : n3.name) || "Anonymous"]);
              return Array.isArray(o2.src) && o2.groupArraysAfterLength && o2.src.length > o2.groupArraysAfterLength && (i2 = lt), v().createElement("div", { className: "pretty-json-container object-container" }, v().createElement("div", { className: "object-content" }, v().createElement(i2, Object.assign({ namespace: s2, depth: 0, jsvRoot: true }, o2))));
            }, e4;
          }
          return h(t3, e3), c(t3);
        }(v().PureComponent), ft = function(e3) {
          function t3(e4) {
            var a3;
            return i(this, t3), (a3 = p(this, t3, [e4])).closeModal = function() {
              ne.dispatch({ rjvId: a3.props.rjvId, name: "RESET" });
            }, a3.submit = function() {
              a3.props.submit(a3.state.input);
            }, a3.state = { input: e4.input ? e4.input : "" }, a3;
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this, t4 = this.props, a3 = t4.theme, r2 = t4.rjvId, n2 = t4.isValid, o2 = this.state.input, s2 = n2(o2);
            return v().createElement("div", Object.assign({ className: "key-modal-request" }, G(a3, "key-modal-request"), { onClick: this.closeModal }), v().createElement("div", Object.assign({}, G(a3, "key-modal"), { onClick: function(e5) {
              e5.stopPropagation();
            } }), v().createElement("div", G(a3, "key-modal-label"), "Key Name:"), v().createElement("div", { style: { position: "relative" } }, v().createElement("input", Object.assign({}, G(a3, "key-modal-input"), { className: "key-modal-input", ref: function(e5) {
              return e5 && e5.focus();
            }, spellCheck: false, value: o2, placeholder: "...", onChange: function(t5) {
              e4.setState({ input: t5.target.value });
            }, onKeyPress: function(t5) {
              s2 && "Enter" === t5.key ? e4.submit() : "Escape" === t5.key && e4.closeModal();
            } })), s2 ? v().createElement(Xe, Object.assign({}, G(a3, "key-modal-submit"), { className: "key-modal-submit", onClick: function(t5) {
              return e4.submit();
            } })) : null), v().createElement("span", G(a3, "key-modal-cancel"), v().createElement(Qe, Object.assign({}, G(a3, "key-modal-cancel-icon"), { className: "key-modal-cancel", onClick: function() {
              ne.dispatch({ rjvId: r2, name: "RESET" });
            } })))));
          } }]);
        }(v().PureComponent), ht = function(e3) {
          function t3() {
            var e4;
            i(this, t3);
            for (var a3 = arguments.length, r2 = new Array(a3), n2 = 0; n2 < a3; n2++) r2[n2] = arguments[n2];
            return (e4 = p(this, t3, [].concat(r2))).isValid = function(t4) {
              var a4 = e4.props.rjvId, r3 = se.get(a4, "action", "new-key-request");
              return "" != t4 && -1 === Object.keys(r3.existing_value).indexOf(t4);
            }, e4.submit = function(t4) {
              var a4 = e4.props.rjvId, r3 = se.get(a4, "action", "new-key-request");
              r3.new_value = s({}, r3.existing_value), r3.new_value[t4] = e4.props.defaultValue, ne.dispatch({ name: "VARIABLE_ADDED", rjvId: a4, data: r3 });
            }, e4;
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.active, a3 = e4.theme, r2 = e4.rjvId;
            return t4 ? v().createElement(ft, { rjvId: r2, theme: a3, isValid: this.isValid, submit: this.submit }) : null;
          } }]);
        }(v().PureComponent), mt = function(e3) {
          function t3() {
            return i(this, t3), p(this, t3, arguments);
          }
          return h(t3, e3), c(t3, [{ key: "render", value: function() {
            var e4 = this.props, t4 = e4.message, a3 = e4.active, r2 = e4.theme, n2 = e4.rjvId;
            return a3 ? v().createElement("div", Object.assign({ className: "validation-failure" }, G(r2, "validation-failure"), { onClick: function() {
              ne.dispatch({ rjvId: n2, name: "RESET" });
            } }), v().createElement("span", G(r2, "validation-failure-label"), t4), v().createElement(Qe, G(r2, "validation-failure-clear"))) : null;
          } }]);
        }(v().PureComponent), vt = function(e3) {
          function t3(e4) {
            var a3;
            return i(this, t3), (a3 = p(this, t3, [e4])).rjvId = Date.now().toString() + Math.random().toString(36).slice(2), a3.getListeners = function() {
              return { reset: a3.resetState, "variable-update": a3.updateSrc, "add-key-request": a3.addKeyRequest };
            }, a3.updateSrc = function() {
              var e5, t4 = se.get(a3.rjvId, "action", "variable-update"), r2 = t4.name, n2 = t4.namespace, o2 = t4.new_value, s2 = t4.existing_value, i2 = t4.updated_src, l2 = t4.type, c2 = a3.props, u2 = c2.onEdit, d2 = c2.onDelete, b2 = c2.onAdd, p2 = { existing_src: a3.state.src, new_value: o2, updated_src: i2, name: r2, namespace: n2, existing_value: s2 };
              switch (l2) {
                case "variable-added":
                  e5 = b2(p2);
                  break;
                case "variable-edited":
                  e5 = u2(p2);
                  break;
                case "variable-removed":
                  e5 = d2(p2);
              }
              false !== e5 ? (se.set(a3.rjvId, "global", "src", i2), a3.setState({ src: i2 })) : a3.setState({ validationFailure: true });
            }, a3.addKeyRequest = function() {
              a3.setState({ addKeyRequest: true });
            }, a3.resetState = function() {
              a3.setState({ validationFailure: false, addKeyRequest: false });
            }, a3.state = { addKeyRequest: false, editKeyRequest: false, validationFailure: false, src: t3.defaultProps.src, name: t3.defaultProps.name, theme: t3.defaultProps.theme, validationMessage: t3.defaultProps.validationMessage, prevSrc: t3.defaultProps.src, prevName: t3.defaultProps.name, prevTheme: t3.defaultProps.theme }, a3;
          }
          return h(t3, e3), c(t3, [{ key: "componentDidMount", value: function() {
            se.set(this.rjvId, "global", "src", this.state.src);
            var e4 = this.getListeners();
            for (var t4 in e4) se.on(t4 + "-" + this.rjvId, e4[t4]);
            this.setState({ addKeyRequest: false, editKeyRequest: false });
          } }, { key: "componentDidUpdate", value: function(e4, t4) {
            false !== t4.addKeyRequest && this.setState({ addKeyRequest: false }), false !== t4.editKeyRequest && this.setState({ editKeyRequest: false }), e4.src !== this.state.src && se.set(this.rjvId, "global", "src", this.state.src);
          } }, { key: "componentWillUnmount", value: function() {
            var e4 = this.getListeners();
            for (var t4 in e4) se.removeListener(t4 + "-" + this.rjvId, e4[t4]);
          } }, { key: "render", value: function() {
            var e4 = this.state, t4 = e4.validationFailure, a3 = e4.validationMessage, r2 = e4.addKeyRequest, n2 = e4.theme, o2 = e4.src, i2 = e4.name, l2 = this.props, c2 = l2.style, u2 = l2.defaultValue;
            return v().createElement("div", { className: "react-json-view", style: s(s({}, G(n2, "app-container").style), c2) }, v().createElement(mt, { message: a3, active: t4, theme: n2, rjvId: this.rjvId }), v().createElement(pt, Object.assign({}, this.props, { src: o2, name: i2, theme: n2, type: x(o2), rjvId: this.rjvId })), v().createElement(ht, { active: r2, theme: n2, rjvId: this.rjvId, defaultValue: u2 }));
          } }], [{ key: "getDerivedStateFromProps", value: function(e4, a3) {
            if (e4.src !== a3.prevSrc || e4.name !== a3.prevName || e4.theme !== a3.prevTheme) {
              var r2 = { src: e4.src, name: e4.name, theme: e4.theme, validationMessage: e4.validationMessage, prevSrc: e4.src, prevName: e4.name, prevTheme: e4.theme };
              return t3.validateState(r2);
            }
            return null;
          } }]);
        }(v().PureComponent);
        vt.defaultProps = { src: {}, name: "root", theme: "rjv-default", collapsed: false, collapseStringsAfterLength: false, shouldCollapse: false, sortKeys: false, quotesOnKeys: true, groupArraysAfterLength: 100, indentWidth: 4, enableClipboard: true, escapeStrings: true, displayObjectSize: true, displayDataTypes: true, onEdit: false, onDelete: false, onAdd: false, onSelect: false, iconStyle: "triangle", style: {}, validationMessage: "Validation Error", defaultValue: null, displayArrayKey: true, selectOnFocus: false, keyModifier: function(e3) {
          return e3.metaKey || e3.ctrlKey;
        }, bigNumber: null }, vt.validateState = function(e3) {
          var t3 = {};
          return "object" !== x(e3.theme) || function(e4) {
            var t4 = ["base00", "base01", "base02", "base03", "base04", "base05", "base06", "base07", "base08", "base09", "base0A", "base0B", "base0C", "base0D", "base0E", "base0F"];
            if ("object" === x(e4)) {
              for (var a3 = 0; a3 < t4.length; a3++) if (!(t4[a3] in e4)) return false;
              return true;
            }
            return false;
          }(e3.theme) || (console.error("react-json-view error:", "theme prop must be a theme name or valid base-16 theme object.", 'defaulting to "rjv-default" theme'), t3.theme = "rjv-default"), "object" !== x(e3.src) && "array" !== x(e3.src) && (console.error("react-json-view error:", "src property must be a valid json object"), t3.name = "ERROR", t3.src = { message: "src property must be a valid json object" }), s(s({}, e3), t3);
        }, E(vt);
        const gt = vt;
      })(), n;
    })());
  }
});
export default require_main();
//# sourceMappingURL=@microlink_react-json-view.js.map
