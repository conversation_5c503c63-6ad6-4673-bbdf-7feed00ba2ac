#!/bin/bash

echo "🚀 继续清理服务器环境 - 第二阶段..."
echo "=================================================="

# 记录开始时间
START_TIME=$(date)
echo "开始时间: $START_TIME"
echo ""

# 5. 清理数据库
echo "🗄️ 第五步：清理数据库"
echo "----------------------------------------"

# 停止数据库服务
echo "停止数据库服务..."
systemctl stop mysql 2>/dev/null || echo "MySQL未运行"
systemctl stop postgresql 2>/dev/null || echo "PostgreSQL未运行"
systemctl stop redis-server 2>/dev/null || echo "Redis未运行"
systemctl stop mongodb 2>/dev/null || echo "MongoDB未运行"

# 清理数据库数据目录 (谨慎操作)
echo "清理数据库数据目录..."
DB_DIRS=("/var/lib/mysql" "/var/lib/postgresql" "/var/lib/redis" "/var/lib/mongodb")
for dir in "${DB_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "备份并清理数据库目录: $dir"
        mv "$dir" "${dir}.backup.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || echo "数据库目录移动失败，尝试删除..."
        rm -rf "$dir" 2>/dev/null || echo "数据库目录删除失败"
    fi
done

echo "✅ 数据库清理完成"
echo ""

# 6. 清理项目文件
echo "📁 第六步：清理项目文件"
echo "----------------------------------------"

# 清理主项目目录
PROJECT_DIR="/www/wwwroot/ai.guiyunai.fun"
if [ -d "$PROJECT_DIR" ]; then
    echo "备份项目目录..."
    BACKUP_DIR="/root/backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份重要配置文件
    if [ -f "$PROJECT_DIR/.env" ]; then
        cp "$PROJECT_DIR/.env" "$BACKUP_DIR/" 2>/dev/null || echo "环境文件备份失败"
    fi
    
    if [ -f "$PROJECT_DIR/docker-compose.yml" ]; then
        cp "$PROJECT_DIR/docker-compose.yml" "$BACKUP_DIR/" 2>/dev/null || echo "Docker配置备份失败"
    fi
    
    echo "删除项目目录内容..."
    rm -rf "$PROJECT_DIR"/* 2>/dev/null || echo "项目目录清理完成"
    
    echo "项目文件已清理，重要配置已备份到: $BACKUP_DIR"
else
    echo "项目目录不存在，跳过清理"
fi

# 清理其他可能的项目目录
OTHER_DIRS=("/var/www/html" "/opt/ai-projects" "/home/<USER>/ai-*")
for dir in $OTHER_DIRS; do
    if [ -d "$dir" ] && [ "$dir" != "/var/www/html" ]; then
        echo "清理目录: $dir"
        rm -rf "$dir" 2>/dev/null || echo "目录清理失败: $dir"
    fi
done

echo "✅ 项目文件清理完成"
echo ""

# 7. 清理系统服务
echo "⚙️ 第七步：清理系统服务"
echo "----------------------------------------"

# 查找并停止自定义服务
echo "查找自定义systemd服务..."
CUSTOM_SERVICES=$(systemctl list-units --type=service --state=active | grep -E "(ai-|autogpt|agent)" | awk '{print $1}' || echo "")

if [ -n "$CUSTOM_SERVICES" ]; then
    echo "停止自定义服务..."
    for service in $CUSTOM_SERVICES; do
        echo "停止服务: $service"
        systemctl stop "$service" 2>/dev/null || echo "服务停止失败: $service"
        systemctl disable "$service" 2>/dev/null || echo "服务禁用失败: $service"
    done
else
    echo "未找到自定义AI相关服务"
fi

# 清理自定义服务文件
SERVICE_FILES="/etc/systemd/system/ai-*.service /etc/systemd/system/autogpt*.service /etc/systemd/system/agent*.service"
for file in $SERVICE_FILES; do
    if [ -f "$file" ]; then
        echo "删除服务文件: $file"
        rm -f "$file"
    fi
done

# 重新加载systemd
systemctl daemon-reload 2>/dev/null || echo "systemd重新加载完成"

echo "✅ 系统服务清理完成"
echo ""

# 8. 清理临时文件和缓存
echo "🧹 第八步：清理临时文件和缓存"
echo "----------------------------------------"

# 清理npm缓存
echo "清理npm缓存..."
npm cache clean --force 2>/dev/null || echo "npm缓存清理完成"

# 清理pnpm缓存
echo "清理pnpm缓存..."
pnpm store prune 2>/dev/null || echo "pnpm缓存清理完成"

# 清理系统临时文件
echo "清理系统临时文件..."
rm -rf /tmp/ai-* /tmp/autogpt* /tmp/agent* 2>/dev/null || echo "临时文件清理完成"

# 清理日志文件
echo "清理相关日志文件..."
rm -rf /var/log/ai-* /var/log/autogpt* /var/log/agent* 2>/dev/null || echo "日志文件清理完成"

echo "✅ 临时文件和缓存清理完成"
echo ""

# 9. 重启必要服务
echo "🔄 第九步：重启必要服务"
echo "----------------------------------------"

# 重启Nginx
echo "重启Nginx服务..."
systemctl start nginx 2>/dev/null && echo "Nginx已启动" || echo "Nginx启动失败"

# 检查服务状态
echo "检查服务状态..."
systemctl is-active nginx 2>/dev/null && echo "✅ Nginx运行正常" || echo "❌ Nginx未运行"

echo "✅ 服务重启完成"
echo ""

# 10. 最终清理和验证
echo "🔍 第十步：最终验证"
echo "----------------------------------------"

echo "检查剩余进程..."
REMAINING_PROCESSES=$(ps aux | grep -E "(node|pnpm|next)" | grep -v grep | wc -l)
echo "剩余Node.js相关进程数: $REMAINING_PROCESSES"

echo "检查Docker状态..."
DOCKER_CONTAINERS=$(docker ps -a | wc -l)
echo "剩余Docker容器数: $((DOCKER_CONTAINERS - 1))"

DOCKER_IMAGES=$(docker images | wc -l)
echo "剩余Docker镜像数: $((DOCKER_IMAGES - 1))"

echo "检查磁盘空间..."
df -h /

echo "✅ 验证完成"
echo ""

echo "🎉 服务器环境彻底清理完成！"
echo "=================================================="
echo "开始时间: $START_TIME"
echo "结束时间: $(date)"
echo ""
echo "清理总结："
echo "✅ Docker环境已完全清理"
echo "✅ 所有相关进程已停止"
echo "✅ SSL证书已清理"
echo "✅ Web服务器配置已重置为默认"
echo "✅ 数据库已清理（数据已备份）"
echo "✅ 项目文件已清理（配置已备份）"
echo "✅ 系统服务已清理"
echo "✅ 临时文件和缓存已清理"
echo "✅ 必要服务已重启"
echo ""
echo "🚀 环境已完全准备就绪，可以部署新的AI Agent项目！"
echo ""
echo "📋 下一步建议："
echo "1. 选择要部署的AI Agent项目（OpenHands、MiniCPM-o、Pipecat等）"
echo "2. 配置域名DNS解析"
echo "3. 安装项目依赖"
echo "4. 配置SSL证书"
echo "5. 启动新项目"
