<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="modulepreload" href="/assets/manifest-ca9dcf08.js"/><link rel="modulepreload" href="/assets/entry.client-NnZnI4fK.js"/><link rel="modulepreload" href="/assets/chunk-C37GKA54-CBbYr_fP.js"/><link rel="modulepreload" href="/assets/index-yKbcr7Pf.js"/><link rel="modulepreload" href="/assets/react-redux-B5osdedR.js"/><link rel="modulepreload" href="/assets/module-5laXsVNO.js"/><link rel="modulepreload" href="/assets/index-DbrbOxWj.js"/><link rel="modulepreload" href="/assets/open-hands-axios-CtirLpss.js"/><link rel="modulepreload" href="/assets/store-Bya9Reqe.js"/><link rel="modulepreload" href="/assets/open-hands-Ce72Fmtl.js"/><link rel="modulepreload" href="/assets/custom-toast-handlers-CR9P-jKI.js"/><link rel="modulepreload" href="/assets/query-client-config-CJn-5u6A.js"/><link rel="modulepreload" href="/assets/i18next-CO45VQzB.js"/><link rel="modulepreload" href="/assets/preload-helper-BXl3LOEh.js"/><link rel="modulepreload" href="/assets/i18nInstance-DBIXdvxg.js"/><link rel="modulepreload" href="/assets/browser-slice-DabBaamq.js"/><link rel="modulepreload" href="/assets/agent-state-CFaY3go2.js"/><link rel="modulepreload" href="/assets/index-cxP66Ws3.js"/><link rel="modulepreload" href="/assets/declaration-xyc84-tJ.js"/><link rel="modulepreload" href="/assets/retrieve-axios-error-message-CYr77e_f.js"/><link rel="modulepreload" href="/assets/mutation-B9dSlWD-.js"/><link rel="modulepreload" href="/assets/root-BvovGoIM.js"/><title>OpenHands</title><meta name="description" content="Let&#x27;s Start Building!"/><link rel="stylesheet" href="/assets/root-CPcSsqtz.css"/></head><body><script>
              console.log(
                "💿 Hey developer 👋. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running `clientLoader` " +
                "functions. Check out https://reactrouter.com/start/framework/route-module#hydratefallback " +
                "for more information."
              );
            </script><div id="modal-portal-exit"></div><script>window.__reactRouterContext = {"basename":"/","future":{"unstable_middleware":false,"unstable_optimizeDeps":false,"unstable_splitRouteModules":false,"unstable_subResourceIntegrity":false,"unstable_viteEnvironmentApi":false},"routeDiscovery":{"mode":"initial"},"ssr":false,"isSpaMode":true};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());</script><script type="module" async="">import "/assets/manifest-ca9dcf08.js";
import * as route0 from "/assets/root-BvovGoIM.js";
  
  window.__reactRouterRouteModules = {"root":route0};

import("/assets/entry.client-NnZnI4fK.js");</script><div id="_rht_toaster" style="position:fixed;z-index:9999;top:16px;left:16px;right:16px;bottom:16px;pointer-events:none"></div><!--$--><script>window.__reactRouterContext.streamController.enqueue("[{\"_1\":2,\"_3\":-5,\"_4\":-5},\"loaderData\",{},\"actionData\",\"errors\"]\n");</script><!--$--><script>window.__reactRouterContext.streamController.close();</script><!--/$--><!--/$--></body></html>