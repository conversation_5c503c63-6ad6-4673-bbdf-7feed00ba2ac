import{j as e,w as $,R as c}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as K}from"./use-save-settings-Bb4p0cGE.js";import{u as F,D as X}from"./use-settings-CSlhfPqo.js";import{A as u}from"./index-DbrbOxWj.js";import{B as Y}from"./brand-button-3Z8FN4qR.js";import{S as h,a as d}from"./switch-skeleton-BbiAGd10.js";import{S as q}from"./settings-input-i4i_IemM.js";import{I as o}from"./declaration-xyc84-tJ.js";import{S as z}from"./settings-dropdown-input-Did5iUTK.js";import{u as _}from"./useTranslation-BG59QWH_.js";import{h as J}from"./handle-capture-consent-L0_Qc6gK.js";import{a as Q,d as W}from"./custom-toast-handlers-CR9P-jKI.js";import{r as Z}from"./retrieve-axios-error-message-CYr77e_f.js";import{I as tt}from"./input-skeleton-FOAHYnV9.js";import{u as et}from"./use-config-jdwF3W4-.js";import{p as C}from"./settings-utils-C--CGkWg.js";import"./open-hands-axios-CtirLpss.js";import"./module-5laXsVNO.js";import"./open-hands-Ce72Fmtl.js";import"./useQuery-Cu2nkJ8V.js";import"./i18next-CO45VQzB.js";import"./preload-helper-BXl3LOEh.js";import"./i18nInstance-DBIXdvxg.js";import"./mutation-B9dSlWD-.js";import"./utils-KsbccAr1.js";import"./optional-tag-e1gRgM9y.js";import"./chunk-S6H5EOGR-Bwn62IP6.js";import"./index-yKbcr7Pf.js";import"./index-cxP66Ws3.js";import"./map-provider-g8SgAMsv.js";function st({defaultKey:a,onChange:g,name:i}){const{t:s}=_();return e.jsx(z,{testId:i,name:i,onInputChange:g,label:s(o.SETTINGS$LANGUAGE),items:u.map(l=>({key:l.value,label:l.label})),defaultSelectedKey:a,isClearable:!1,wrapperClassName:"w-full max-w-[680px]"})}function at(){return e.jsxs("div",{"data-testid":"app-settings-skeleton",className:"px-11 py-9 flex flex-col gap-6",children:[e.jsx(tt,{}),e.jsx(h,{}),e.jsx(h,{})]})}function nt(){const{t:a}=_(),{mutate:g,isPending:i}=K(),{data:s,isLoading:l}=F(),{data:x}=et(),[O,T]=c.useState(!1),[b,m]=c.useState(!1),[w,f]=c.useState(!1),[R,A]=c.useState(!1),[v,N]=c.useState(!1),G=t=>{const n=t.get("language-input")?.toString(),r=u.find(({label:p})=>p===n)?.value||X.LANGUAGE,I=t.get("enable-analytics-switch")?.toString()==="on",k=t.get("enable-sound-notifications-switch")?.toString()==="on",V=t.get("enable-proactive-conversations-switch")?.toString()==="on",H=t.get("max-budget-per-task-input")?.toString(),D=C(H||"");g({LANGUAGE:r,user_consents_to_analytics:I,ENABLE_SOUND_NOTIFICATIONS:k,ENABLE_PROACTIVE_CONVERSATION_STARTERS:V,MAX_BUDGET_PER_TASK:D},{onSuccess:()=>{J(I),W(a(o.SETTINGS$SAVED))},onError:p=>{const M=Z(p);Q(M||a(o.ERROR$GENERIC))},onSettled:()=>{T(!1),m(!1),f(!1),A(!1),N(!1)}})},L=t=>{const n=u.find(({label:r})=>r===t)?.label,S=u.find(({value:r})=>r===s?.LANGUAGE)?.label;T(n!==S)},y=t=>{const n=!!s?.USER_CONSENTS_TO_ANALYTICS;m(t!==n)},P=t=>{const n=!!s?.ENABLE_SOUND_NOTIFICATIONS;f(t!==n)},B=t=>{const n=!!s?.ENABLE_PROACTIVE_CONVERSATION_STARTERS;A(t!==n)},U=t=>{const n=C(t),S=s?.MAX_BUDGET_PER_TASK;N(n!==S)},j=!O&&!b&&!w&&!R&&!v,E=!s||l||i;return e.jsxs("form",{"data-testid":"app-settings-screen",action:G,className:"flex flex-col h-full justify-between",children:[E&&e.jsx(at,{}),!E&&e.jsxs("div",{className:"p-9 flex flex-col gap-6",children:[e.jsx(st,{name:"language-input",defaultKey:s.LANGUAGE,onChange:L}),e.jsx(d,{testId:"enable-analytics-switch",name:"enable-analytics-switch",defaultIsToggled:!!s.USER_CONSENTS_TO_ANALYTICS,onToggle:y,children:a(o.ANALYTICS$SEND_ANONYMOUS_DATA)}),e.jsx(d,{testId:"enable-sound-notifications-switch",name:"enable-sound-notifications-switch",defaultIsToggled:!!s.ENABLE_SOUND_NOTIFICATIONS,onToggle:P,children:a(o.SETTINGS$SOUND_NOTIFICATIONS)}),x?.APP_MODE==="saas"&&e.jsx(d,{testId:"enable-proactive-conversations-switch",name:"enable-proactive-conversations-switch",defaultIsToggled:!!s.ENABLE_PROACTIVE_CONVERSATION_STARTERS,onToggle:B,children:a(o.SETTINGS$PROACTIVE_CONVERSATION_STARTERS)}),e.jsx(q,{testId:"max-budget-per-task-input",name:"max-budget-per-task-input",type:"number",label:a(o.SETTINGS$MAX_BUDGET_PER_CONVERSATION),defaultValue:s.MAX_BUDGET_PER_TASK?.toString()||"",onChange:U,placeholder:a(o.SETTINGS$MAXIMUM_BUDGET_USD),min:1,step:1,className:"w-full max-w-[680px]"})]}),e.jsx("div",{className:"flex gap-6 p-6 justify-end border-t border-t-tertiary",children:e.jsxs(Y,{testId:"submit-button",variant:"primary",type:"submit",isDisabled:i||j,children:[!i&&a("SETTINGS$SAVE_CHANGES"),i&&a("SETTINGS$SAVING")]})})]})}const Bt=$(nt);export{Bt as default};
