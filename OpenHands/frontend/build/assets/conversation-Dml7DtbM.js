const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/changes-tab-BaITyvjM.js","assets/chunk-C37GKA54-CBbYr_fP.js","assets/react-redux-B5osdedR.js","assets/index-C8v-0ELK.js","assets/iconBase-2PDVWRGH.js","assets/utils-KsbccAr1.js","assets/useQuery-Cu2nkJ8V.js","assets/open-hands-axios-CtirLpss.js","assets/open-hands-Ce72Fmtl.js","assets/use-conversation-id-0JHAicdF.js","assets/retrieve-axios-error-message-CYr77e_f.js","assets/use-runtime-is-ready-CsTtc0dU.js","assets/agent-state-CFaY3go2.js","assets/use-active-conversation-B8Aw3kE2.js","assets/declaration-xyc84-tJ.js","assets/useTranslation-BG59QWH_.js","assets/i18nInstance-DBIXdvxg.js","assets/browser-tab-DQj3XIBz.js","assets/browser-slice-DabBaamq.js","assets/jupyter-tab-BN0K47b7.js","assets/scroll-to-bottom-button-DXWtkqaP.js","assets/paragraph-D4ROHliG.js","assets/served-tab-jFMthLLa.js","assets/index-DvLMSsrd.js","assets/index-Do49u1Ze.js","assets/use-active-host-CByqDCZz.js","assets/terminal-tab-Ea_WHo_u.js","assets/preload-helper-BXl3LOEh.js","assets/vscode-tab-UrEftLrh.js","assets/vscode-url-helper-5Tt6LlE2.js"])))=>i.map(i=>d[i]);
import{r as o,R as N,j as t,i as I5,u as A5,N as j5,d as R5,w as O5,b as M5}from"./chunk-C37GKA54-CBbYr_fP.js";import{u as pe,a as L5}from"./react-redux-B5osdedR.js";import{u as te}from"./use-conversation-id-0JHAicdF.js";import{I as u}from"./declaration-xyc84-tJ.js";import{g as ut,e as j1,r as Ct,a as ft,b as D5,o as pt,u as mt,c as ht,f as lt,h as $5,B as P5,d as k5,C as F5,E as U5}from"./event-handler-CNbFJRld.js";import{A as y,R as B5}from"./agent-state-CFaY3go2.js";import{u as Ke,s as H5,A as G5,E as R1,d as me,e as We,f as O1,g as Zt,c as M1,h as Yt,j as V5,k as z5,a as Qe,b as qt,l as K5,m as W5,n as Z5,W as Y5}from"./ws-client-provider-Dmsj8lkD.js";import{t as gt}from"./chunk-BOOVDPB6-Dt9AQnxs.js";import{u as P}from"./useTranslation-BG59QWH_.js";import{u as L1,a as q5}from"./use-settings-CSlhfPqo.js";import{u as ke,a as J5}from"./use-active-conversation-B8Aw3kE2.js";import{G as ie}from"./iconBase-2PDVWRGH.js";import{d as X5,b as Q5,c as es,R as Jt,e as ts}from"./conversation-card-BnTfWkKs.js";import{A as $,q as Xt,r as Qt}from"./store-Bya9Reqe.js";import{p as we}from"./module-5laXsVNO.js";import{_ as ss,a as rs,M as xt,p as as}from"./paragraph-D4ROHliG.js";import{c as V,t as et,a as Ve}from"./utils-KsbccAr1.js";import{S as ns,u as bt,a as os}from"./scroll-to-bottom-button-DXWtkqaP.js";import{c as ls,d as is,e as cs,f as ds}from"./index-Do49u1Ze.js";import{a as fe,c as us}from"./custom-toast-handlers-CR9P-jKI.js";import{M as D1}from"./modal-backdrop-ve4Sk5I2.js";import{M as vt,J as e1}from"./constants-DCYeMzG2.js";import{V as Te}from"./index-cxP66Ws3.js";import{u as Ie,B as ze}from"./brand-button-3Z8FN4qR.js";import{O as ce}from"./open-hands-Ce72Fmtl.js";import{r as Cs}from"./index-yKbcr7Pf.js";import"./index-DbrbOxWj.js";import{i as W}from"./i18next-CO45VQzB.js";import{T as it}from"./Trans-CI4prNur.js";import{F as fs,G as t1,q as $1,c as ps,ak as ms,ax as P1,ay as hs,az as gs,aA as xs,aB as bs,aC as vs,aD as Es,aE as ys,d as Ns,f as s1,g as Ss,aF as _s,u as Ts,aG as ws,aH as Is,m as As,I as r1,o as le,af as js,S as Rs,U as a1,e as Os,aI as Ms,y as B,v as Ls,P as Ds,x as U,ai as n1,aJ as $s,aK as Ps,A as ks,aL as Fs,s as Et,aM as Us,aN as Bs,aO as Hs,aP as Gs,E as Vs,aQ as zs,X as He,l as Le}from"./chunk-S6H5EOGR-Bwn62IP6.js";import{u as Ks,o as ee}from"./open-hands-axios-CtirLpss.js";import{u as yt}from"./use-config-jdwF3W4-.js";import{u as he}from"./useQuery-Cu2nkJ8V.js";import{u as k1}from"./use-optimistic-user-message-tdysaQ5t.js";import{i as o1}from"./index-DvLMSsrd.js";import{S as Ws}from"./settings-dropdown-input-Did5iUTK.js";import{u as F1}from"./use-user-providers-CVWOd-tS.js";import{L as Nt}from"./loading-spinner-D6qcLhqr.js";import{u as Zs}from"./use-runtime-is-ready-CsTtc0dU.js";import{d as Ys}from"./index-C8v-0ELK.js";import{u as qs}from"./use-active-host-CByqDCZz.js";import{_ as je}from"./preload-helper-BXl3LOEh.js";import{t as Js}from"./vscode-url-helper-5Tt6LlE2.js";import"./use-create-conversation-IQqGjJUl.js";import"./browser-slice-DabBaamq.js";import"./query-client-config-CJn-5u6A.js";import"./retrieve-axios-error-message-CYr77e_f.js";import"./mutation-B9dSlWD-.js";import"./i18nInstance-DBIXdvxg.js";import"./optional-tag-e1gRgM9y.js";var l1=fs({slots:{base:["group inline-flex flex-col relative"],label:["block","absolute","z-10","origin-top-left","flex-shrink-0","rtl:origin-top-right","subpixel-antialiased","text-small","text-foreground-500","pointer-events-none","group-data-[has-label-outside=true]:pointer-events-auto"],mainWrapper:"w-full flex flex-col",trigger:"relative px-3 gap-3 w-full inline-flex flex-row items-center shadow-xs outline-solid outline-transparent tap-highlight-transparent",innerWrapper:"inline-flex h-fit w-[calc(100%_-theme(spacing.6))] min-h-4 items-center gap-1.5 box-border",selectorIcon:"absolute end-3 w-4 h-4",spinner:"absolute end-3",value:["text-foreground-500","font-normal","w-full","text-start"],listboxWrapper:"scroll-py-6 w-full",listbox:"",popoverContent:"w-full p-1 overflow-hidden",clearButton:["w-4","h-4","z-10","mb-4","relative","start-auto","appearance-none","outline-none","select-none","opacity-70","hover:!opacity-100","cursor-pointer","active:!opacity-70","rounded-full",...t1],helperWrapper:"p-1 flex relative flex-col gap-1.5 group-data-[has-helper=true]:flex",description:"text-tiny text-foreground-400",errorMessage:"text-tiny text-danger",endWrapper:"flex end-18",endContent:"mb-4"},variants:{variant:{flat:{trigger:["bg-default-100","data-[hover=true]:bg-default-200","group-data-[focus=true]:bg-default-200"],clearButton:"mb-4"},faded:{trigger:["bg-default-100","border-medium","border-default-200","data-[hover=true]:border-default-400 data-[focus=true]:border-default-400 data-[open=true]:border-default-400"],value:"group-data-[has-value=true]:text-default-foreground",clearButton:"mb-4"},bordered:{trigger:["border-medium","border-default-200","data-[hover=true]:border-default-400","data-[open=true]:border-default-foreground","data-[focus=true]:border-default-foreground"],value:"group-data-[has-value=true]:text-default-foreground",clearButton:"mb-4"},underlined:{trigger:["!px-1","!pb-0","!gap-0","relative","box-border","border-b-medium","shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]","border-default-200","!rounded-none","hover:border-default-300","after:content-['']","after:w-0","after:origin-center","after:bg-default-foreground","after:absolute","after:left-1/2","after:-translate-x-1/2","after:-bottom-[2px]","after:h-[2px]","data-[open=true]:after:w-full","data-[focus=true]:after:w-full"],value:"group-data-[has-value=true]:text-default-foreground",clearButton:"mb-4 me-2"}},color:{default:{},primary:{selectorIcon:"text-primary"},secondary:{selectorIcon:"text-secondary"},success:{selectorIcon:"text-success"},warning:{selectorIcon:"text-warning"},danger:{selectorIcon:"text-danger"}},size:{sm:{label:"text-tiny",trigger:"h-8 min-h-8 px-2 rounded-small",value:"text-small",clearButton:"text-medium"},md:{trigger:"h-10 min-h-10 rounded-medium",value:"text-small",clearButton:"text-large"},lg:{trigger:"h-12 min-h-12 rounded-large",value:"text-medium",clearButton:"mb-5 text-large"}},radius:{none:{trigger:"rounded-none"},sm:{trigger:"rounded-small"},md:{trigger:"rounded-medium"},lg:{trigger:"rounded-large"},full:{trigger:"rounded-full"}},labelPlacement:{outside:{base:"flex flex-col",clearButton:"mb-0"},"outside-left":{base:"flex-row items-center flex-nowrap data-[has-helper=true]:items-start",label:"relative pe-2 text-foreground",clearButton:"mb-0"},inside:{label:"text-tiny cursor-pointer",trigger:"flex-col items-start justify-center gap-0"}},fullWidth:{true:{base:"w-full"},false:{base:"min-w-40"}},isClearable:{true:{clearButton:"peer-data-[filled=true]:opacity-70 peer-data-[filled=true]:block",endContent:"ms-3"}},isDisabled:{true:{base:"opacity-disabled pointer-events-none",trigger:"pointer-events-none"}},isInvalid:{true:{label:"!text-danger",value:"!text-danger",selectorIcon:"text-danger"}},isRequired:{true:{label:"after:content-['*'] after:text-danger after:ms-0.5"}},isMultiline:{true:{label:"relative",trigger:"!h-auto"},false:{value:"truncate"}},disableAnimation:{true:{trigger:"after:transition-none",base:"transition-none",label:"transition-none",selectorIcon:"transition-none"},false:{base:"transition-background motion-reduce:transition-none !duration-150",label:["will-change-auto","origin-top-left","rtl:origin-top-right","!duration-200","!ease-out","transition-[transform,color,left,opacity,translate,scale]","motion-reduce:transition-none"],selectorIcon:"transition-transform duration-150 ease motion-reduce:transition-none",clearButton:["transition-opacity","motion-reduce:transition-none"]}},disableSelectorIconRotation:{true:{},false:{selectorIcon:"data-[open=true]:rotate-180"}}},defaultVariants:{variant:"flat",color:"default",size:"md",fullWidth:!0,isDisabled:!1,isMultiline:!1,disableSelectorIconRotation:!1},compoundVariants:[{variant:"flat",color:"default",class:{value:"group-data-[has-value=true]:text-default-foreground",trigger:["bg-default-100","data-[hover=true]:bg-default-200"]}},{variant:"flat",color:"primary",class:{trigger:["bg-primary-100","text-primary","data-[hover=true]:bg-primary-50","group-data-[focus=true]:bg-primary-50"],value:"text-primary",label:"text-primary"}},{variant:"flat",color:"secondary",class:{trigger:["bg-secondary-100","text-secondary","data-[hover=true]:bg-secondary-50","group-data-[focus=true]:bg-secondary-50"],value:"text-secondary",label:"text-secondary"}},{variant:"flat",color:"success",class:{trigger:["bg-success-100","text-success-600","dark:text-success","data-[hover=true]:bg-success-50","group-data-[focus=true]:bg-success-50"],value:"text-success-600 dark:text-success",label:"text-success-600 dark:text-success"}},{variant:"flat",color:"warning",class:{trigger:["bg-warning-100","text-warning-600","dark:text-warning","data-[hover=true]:bg-warning-50","group-data-[focus=true]:bg-warning-50"],value:"text-warning-600 dark:text-warning",label:"text-warning-600 dark:text-warning"}},{variant:"flat",color:"danger",class:{trigger:["bg-danger-100","text-danger","dark:text-danger-500","data-[hover=true]:bg-danger-50","group-data-[focus=true]:bg-danger-50"],value:"text-danger dark:text-danger-500",label:"text-danger dark:text-danger-500"}},{variant:"faded",color:"primary",class:{trigger:"data-[hover=true]:border-primary data-[focus=true]:border-primary data-[open=true]:border-primary",label:"text-primary"}},{variant:"faded",color:"secondary",class:{trigger:"data-[hover=true]:border-secondary data-[focus=true]:border-secondary data-[open=true]:border-secondary",label:"text-secondary"}},{variant:"faded",color:"success",class:{trigger:"data-[hover=true]:border-success data-[focus=true]:border-success data-[open=true]:border-success",label:"text-success"}},{variant:"faded",color:"warning",class:{trigger:"data-[hover=true]:border-warning data-[focus=true]:border-warning data-[open=true]:border-warning",label:"text-warning"}},{variant:"faded",color:"danger",class:{trigger:"data-[hover=true]:border-danger data-[focus=true]:border-danger data-[open=true]:border-danger",label:"text-danger"}},{variant:"underlined",color:"default",class:{value:"group-data-[has-value=true]:text-foreground"}},{variant:"underlined",color:"primary",class:{trigger:"after:bg-primary",label:"text-primary"}},{variant:"underlined",color:"secondary",class:{trigger:"after:bg-secondary",label:"text-secondary"}},{variant:"underlined",color:"success",class:{trigger:"after:bg-success",label:"text-success"}},{variant:"underlined",color:"warning",class:{trigger:"after:bg-warning",label:"text-warning"}},{variant:"underlined",color:"danger",class:{trigger:"after:bg-danger",label:"text-danger"}},{variant:"bordered",color:"primary",class:{trigger:["data-[open=true]:border-primary","data-[focus=true]:border-primary"],label:"text-primary"}},{variant:"bordered",color:"secondary",class:{trigger:["data-[open=true]:border-secondary","data-[focus=true]:border-secondary"],label:"text-secondary"}},{variant:"bordered",color:"success",class:{trigger:["data-[open=true]:border-success","data-[focus=true]:border-success"],label:"text-success"}},{variant:"bordered",color:"warning",class:{trigger:["data-[open=true]:border-warning","data-[focus=true]:border-warning"],label:"text-warning"}},{variant:"bordered",color:"danger",class:{trigger:["data-[open=true]:border-danger","data-[focus=true]:border-danger"],label:"text-danger"}},{labelPlacement:"inside",color:"default",class:{label:"group-data-[filled=true]:text-default-600"}},{labelPlacement:"outside",color:"default",class:{label:"group-data-[filled=true]:text-foreground"}},{radius:"full",size:["sm"],class:{trigger:"px-3"}},{radius:"full",size:"md",class:{trigger:"px-4"}},{radius:"full",size:"lg",class:{trigger:"px-5"}},{disableAnimation:!1,variant:["faded","bordered"],class:{trigger:"transition-colors motion-reduce:transition-none"}},{disableAnimation:!1,variant:"underlined",class:{trigger:"after:transition-width motion-reduce:after:transition-none"}},{variant:["flat","faded"],class:{trigger:[...t1]}},{isInvalid:!0,variant:"flat",class:{trigger:["bg-danger-50","data-[hover=true]:bg-danger-100","group-data-[focus=true]:bg-danger-50"]}},{isInvalid:!0,variant:"bordered",class:{trigger:"!border-danger group-data-[focus=true]:border-danger"}},{isInvalid:!0,variant:"underlined",class:{trigger:"after:bg-danger"}},{labelPlacement:"inside",size:"sm",class:{trigger:"h-12 min-h-12 py-1.5 px-3"}},{labelPlacement:"inside",size:"md",class:{trigger:"h-14 min-h-14 py-2"}},{labelPlacement:"inside",size:"lg",class:{label:"text-medium",trigger:"h-16 min-h-16 py-2.5 gap-0"}},{labelPlacement:"outside",isMultiline:!1,class:{base:"group relative justify-end",label:["pb-0","z-20","top-1/2","-translate-y-1/2","group-data-[filled=true]:start-0"]}},{labelPlacement:["inside"],class:{label:"group-data-[filled=true]:scale-85"}},{labelPlacement:"inside",size:["sm","md"],class:{label:"text-small"}},{labelPlacement:"inside",isMultiline:!1,size:"sm",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-tiny)/2_-_8px)]"],innerWrapper:"group-data-[has-label=true]:pt-4"}},{labelPlacement:"inside",isMultiline:!1,size:"md",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_6px)]"],innerWrapper:"group-data-[has-label=true]:pt-4"}},{labelPlacement:"inside",isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_8px)]"],innerWrapper:"group-data-[has-label=true]:pt-5"}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"sm",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-tiny)/2_-_8px_-_var(--heroui-border-width-medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"md",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_6px_-_var(--heroui-border-width-medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_8px_-_var(--heroui-border-width-medium))]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"sm",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-tiny)/2_-_5px)]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"md",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_3.5px)]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled=true]:-translate-y-[calc(50%_+_var(--heroui-font-size-small)/2_-_4px)]"]}},{labelPlacement:"outside",size:"sm",isMultiline:!1,class:{label:["start-2","text-tiny","group-data-[filled=true]:-translate-y-[calc(100%_+var(--heroui-font-size-tiny)/2_+_16px)]","group-data-[has-helper=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_26px)]"],base:"data-[has-label=true]:mt-[calc(var(--heroui-font-size-small)_+_8px)]"}},{labelPlacement:"outside",isMultiline:!1,size:"md",class:{label:["start-3","text-small","group-data-[filled=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_20px)]","group-data-[has-helper=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_30px)]"],base:"data-[has-label=true]:mt-[calc(var(--heroui-font-size-small)_+_10px)]"}},{labelPlacement:"outside",isMultiline:!1,size:"lg",class:{label:["start-3","text-medium","group-data-[filled=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_24px)]","group-data-[has-helper=true]:-translate-y-[calc(100%_+_var(--heroui-font-size-small)/2_+_34px)]"],base:"data-[has-label=true]:mt-[calc(var(--heroui-font-size-small)_+_12px)]"}},{labelPlacement:"outside-left",size:"sm",class:{label:"group-data-[has-helper=true]:pt-2"}},{labelPlacement:"outside-left",size:"md",class:{label:"group-data-[has-helper=true]:pt-3"}},{labelPlacement:"outside-left",size:"lg",class:{label:"group-data-[has-helper=true]:pt-4"}},{labelPlacement:"outside",isMultiline:!0,class:{label:"pb-1.5"}},{labelPlacement:["inside","outside"],class:{label:["pe-2","max-w-full","text-ellipsis","overflow-hidden"]}},{labelPlacement:["outside","outside-left"],isClearable:!0,class:{endContent:["mt-4"],clearButton:["group-data-[has-end-content=true]:mt-4"]}},{isClearable:!1,labelPlacement:["outside","outside-left"],class:{endContent:["mt-4"]}},{isClearable:!0,variant:["underlined"],class:{clearButton:["relative group-data-[has-end-content=true]:left-2"],endContent:["me-2"]}},{isClearable:!1,variant:["underlined"],class:{endContent:["me-2"]}},{isClearable:!0,size:"sm",class:{endContent:"ms-2"}}]});function i1(e,s=[]){const r=o.useRef(e);return $1(()=>{r.current=e}),o.useCallback((...a)=>{var n;return(n=r.current)==null?void 0:n.call(r,...a)},s)}var Xs=o.useLayoutEffect,Qs=function(s){var r=N.useRef(s);return Xs(function(){r.current=s}),r},c1=function(s,r){if(typeof s=="function"){s(r);return}s.current=r},e4=function(s,r){var a=N.useRef();return N.useCallback(function(n){s.current=n,a.current&&c1(a.current,null),a.current=r,r&&c1(r,n)},[r])},d1={"min-height":"0","max-height":"none",height:"0",visibility:"hidden",overflow:"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0",display:"block"},t4=function(s){Object.keys(d1).forEach(function(r){s.style.setProperty(r,d1[r],"important")})},u1=t4,H=null,C1=function(s,r){var a=s.scrollHeight;return r.sizingStyle.boxSizing==="border-box"?a+r.borderSize:a-r.paddingSize};function s4(e,s,r,a){r===void 0&&(r=1),a===void 0&&(a=1/0),H||(H=document.createElement("textarea"),H.setAttribute("tabindex","-1"),H.setAttribute("aria-hidden","true"),u1(H)),H.parentNode===null&&document.body.appendChild(H);var n=e.paddingSize,l=e.borderSize,i=e.sizingStyle,C=i.boxSizing;Object.keys(i).forEach(function(m){var v=m;H.style[v]=i[v]}),u1(H),H.value=s;var p=C1(H,e);H.value=s,p=C1(H,e),H.value="x";var c=H.scrollHeight-n,g=c*r;C==="border-box"&&(g=g+n+l),p=Math.max(g,p);var d=c*a;return C==="border-box"&&(d=d+n+l),p=Math.min(d,p),[p,c]}var f1=function(){},r4=function(s,r){return s.reduce(function(a,n){return a[n]=r[n],a},{})},a4=["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth","boxSizing","fontFamily","fontSize","fontStyle","fontWeight","letterSpacing","lineHeight","paddingBottom","paddingLeft","paddingRight","paddingTop","tabSize","textIndent","textRendering","textTransform","width","wordBreak","wordSpacing","scrollbarGutter"],n4=!!document.documentElement.currentStyle,o4=function(s){var r=window.getComputedStyle(s);if(r===null)return null;var a=r4(a4,r),n=a.boxSizing;if(n==="")return null;n4&&n==="border-box"&&(a.width=parseFloat(a.width)+parseFloat(a.borderRightWidth)+parseFloat(a.borderLeftWidth)+parseFloat(a.paddingRight)+parseFloat(a.paddingLeft)+"px");var l=parseFloat(a.paddingBottom)+parseFloat(a.paddingTop),i=parseFloat(a.borderBottomWidth)+parseFloat(a.borderTopWidth);return{sizingStyle:a,paddingSize:l,borderSize:i}},l4=o4;function St(e,s,r){var a=Qs(r);o.useLayoutEffect(function(){var n=function(i){return a.current(i)};if(e)return e.addEventListener(s,n),function(){return e.removeEventListener(s,n)}},[])}var i4=function(s,r){St(document.body,"reset",function(a){s.current.form===a.target&&r(a)})},c4=function(s){St(window,"resize",s)},d4=function(s){St(document.fonts,"loadingdone",s)},u4=["cacheMeasurements","maxRows","minRows","onChange","onHeightChange"],C4=function(s,r){var a=s.cacheMeasurements,n=s.maxRows,l=s.minRows,i=s.onChange,C=i===void 0?f1:i,p=s.onHeightChange,c=p===void 0?f1:p,g=ss(s,u4),d=g.value!==void 0,m=o.useRef(null),v=e4(m,r),T=o.useRef(0),f=o.useRef(),x=function(){var I=m.current,S=a&&f.current?f.current:l4(I);if(S){f.current=S;var h=s4(S,I.value||I.placeholder||"x",l,n),j=h[0],k=h[1];T.current!==j&&(T.current=j,I.style.setProperty("height",j+"px","important"),c(j,{rowHeight:k}))}},w=function(I){d||x(),C(I)};return o.useLayoutEffect(x),i4(m,function(){if(!d){var D=m.current.value;requestAnimationFrame(function(){var I=m.current;I&&D!==I.value&&x()})}}),c4(x),d4(x),o.createElement("textarea",rs({},g,{onChange:w,ref:v}))},f4=o.forwardRef(C4);function p4(e){let s=ps(e),[r,a]=o.useState(null),[n,l]=o.useState([]),i=()=>{l([]),s.close()};return{focusStrategy:r,...s,open(c=null){a(c),s.open()},toggle(c=null){a(c),s.toggle()},close(){i()},expandedKeysStack:n,openSubmenu:(c,g)=>{l(d=>g>d.length?d:[...d.slice(0,g),c])},closeSubmenu:(c,g)=>{l(d=>d[g]===c?d.slice(0,g):d)}}}function m4(e={}){const{id:s,defaultOpen:r,isOpen:a,onClose:n,onOpen:l,onChange:i=()=>{}}=e,C=i1(l),p=i1(n),[c,g]=ms(a,r||!1,i),d=o.useId(),m=s||d,v=a!==void 0,T=o.useCallback(()=>{v||g(!1),p?.()},[v,p]),f=o.useCallback(()=>{v||g(!0),C?.()},[v,C]),x=o.useCallback(()=>{(c?T:f)()},[c,f,T]);return{isOpen:!!c,onOpen:f,onClose:T,onOpenChange:x,isControlled:v,getButtonProps:(w={})=>({...w,"aria-expanded":c,"aria-controls":m,onClick:P1(w.onClick,x)}),getDisclosureProps:(w={})=>({...w,hidden:!c,id:m})}}function h4(e){const{collection:s,disabledKeys:r,selectionManager:a,selectionManager:{setSelectedKeys:n,selectedKeys:l,selectionMode:i}}=hs(e),C=o.useMemo(()=>!e.isLoading&&l.size!==0?Array.from(l).filter(Boolean).filter(c=>!s.getItem(c)):[],[l,s]),p=l.size!==0?Array.from(l).map(c=>s.getItem(c)).filter(Boolean):null;return C.length&&console.warn(`Select: Keys "${C.join(", ")}" passed to "selectedKeys" are not present in the collection.`),{collection:s,disabledKeys:r,selectionManager:a,selectionMode:i,selectedKeys:l,setSelectedKeys:n.bind(a),selectedItems:p}}function g4({validate:e,validationBehavior:s,...r}){const[a,n]=o.useState(!1),[l,i]=o.useState(null),C=p4(r),p=h4({...r,onSelectionChange:d=>{r.onSelectionChange!=null&&(d==="all"?r.onSelectionChange(new Set(p.collection.getKeys())):r.onSelectionChange(d)),r.selectionMode==="single"&&C.close()}}),c=gs({...r,validationBehavior:s,validate:d=>{if(!e)return;const m=Array.from(d);return e(r.selectionMode==="single"?m[0]:m)},value:p.selectedKeys}),g=p.collection.size===0&&r.hideEmptyContent;return{...c,...p,...C,focusStrategy:l,close(){C.close()},open(d=null){g||(i(d),C.open())},toggle(d=null){g||(i(d),C.toggle())},isFocused:a,setFocused:n}}function x4(e,s,r){const{disallowEmptySelection:a,isDisabled:n}=e,l=xs({usage:"search",sensitivity:"base"}),i=o.useMemo(()=>new bs(s.collection,s.disabledKeys,null,l),[s.collection,s.disabledKeys,l]),{menuTriggerProps:C,menuProps:p}=vs({isDisabled:n,type:"listbox"},s,r),c=h=>{if(s.selectionMode==="single")switch(h.key){case"ArrowLeft":{h.preventDefault();const j=s.selectedKeys.size>0?i.getKeyAbove(s.selectedKeys.values().next().value):i.getFirstKey();j&&s.setSelectedKeys([j]);break}case"ArrowRight":{h.preventDefault();const j=s.selectedKeys.size>0?i.getKeyBelow(s.selectedKeys.values().next().value):i.getFirstKey();j&&s.setSelectedKeys([j]);break}}},{typeSelectProps:g}=Es({keyboardDelegate:i,selectionManager:s.selectionManager,onTypeSelect(h){s.setSelectedKeys([h])}}),{isInvalid:d,validationErrors:m,validationDetails:v}=s.displayValidation,{labelProps:T,fieldProps:f,descriptionProps:x,errorMessageProps:w}=ys({...e,labelElementType:"span",isInvalid:d,errorMessage:e.errorMessage||m});g.onKeyDown=g.onKeyDownCapture,delete g.onKeyDownCapture,C.onPressStart=h=>{h.pointerType!=="touch"&&h.pointerType!=="keyboard"&&!n&&s.toggle(h.pointerType==="virtual"?"first":null)};const D=Ns(e,{labelable:!0}),I=s1(g,C,f),S=Ss();return{labelProps:{...T,onClick:()=>{var h;e.isDisabled||((h=r.current)==null||h.focus(),_s("keyboard"))}},triggerProps:s1(D,{...I,onKeyDown:P1(I.onKeyDown,c,e.onKeyDown),onKeyUp:e.onKeyUp,"aria-labelledby":[S,I["aria-labelledby"],I["aria-label"]&&!I["aria-labelledby"]?I.id:null].join(","),onFocus(h){s.isFocused||(e.onFocus&&e.onFocus(h),s.setFocused(!0))},onBlur(h){s.isOpen||(e.onBlur&&e.onBlur(h),s.setFocused(!1))}}),valueProps:{id:S},menuProps:{...p,disallowEmptySelection:a,autoFocus:s.focusStrategy||!0,shouldSelectOnPressUp:!0,shouldFocusOnHover:!0,onBlur:h=>{h.currentTarget.contains(h.relatedTarget)||(e.onBlur&&e.onBlur(h),s.setFocused(!1))},onFocus:p?.onFocus,"aria-labelledby":[f["aria-labelledby"],I["aria-label"]&&!f["aria-labelledby"]?I.id:null].filter(Boolean).join(" ")},descriptionProps:x,errorMessageProps:w,isInvalid:d,validationErrors:m,validationDetails:v}}var U1=new WeakMap;function b4(e){var s,r,a,n,l,i;const C=Ts(),{validationBehavior:p}=ws(Is)||{},[c,g]=As(e,l1.variantKeys),d=(r=(s=e.disableAnimation)!=null?s:C?.disableAnimation)!=null?r:!1,{ref:m,as:v,label:T,name:f,isLoading:x,selectorIcon:w,isOpen:D,defaultOpen:I,onOpenChange:S,startContent:h,endContent:j,description:k,renderValue:R,onSelectionChange:_,placeholder:A,isVirtualized:z,itemHeight:ge=36,maxListboxHeight:Z=256,children:de,disallowEmptySelection:se=!1,selectionMode:Q="single",spinnerRef:re,scrollRef:Re,popoverProps:xe={},scrollShadowProps:be={},listboxProps:L={},spinnerProps:ae={},validationState:ve,onChange:q,onClose:J,className:Oe,classNames:E,validationBehavior:Ee=(a=p??C?.validationBehavior)!=null?a:"native",hideEmptyContent:Me=!1,onClear:ne,...G}=c,X=r1(Re),F={popoverProps:le({placement:"bottom",triggerScaleOnOpen:!1,offset:5,disableAnimation:d},xe),scrollShadowProps:le({ref:X,isEnabled:(n=e.showScrollIndicators)!=null?n:!0,hideScrollBar:!0,offset:15},be),listboxProps:le({disableAnimation:d},L)},Fe=v||"button",ye=typeof Fe=="string",oe=r1(m),K=o.useRef(null),_t=o.useRef(null),Ze=o.useRef(null);let M=g4({...c,isOpen:D,selectionMode:Q,disallowEmptySelection:se,validationBehavior:Ee,children:de,isRequired:e.isRequired,isDisabled:e.isDisabled,isInvalid:e.isInvalid,defaultOpen:I,hideEmptyContent:Me,onOpenChange:b=>{S?.(b),b||J?.()},onSelectionChange:b=>{_?.(b),q&&typeof q=="function"&&q({target:{...oe.current&&{...oe.current,name:oe.current.name},value:Array.from(b).join(",")}}),M.commitValidation()}});M={...M,...e.isDisabled&&{disabledKeys:new Set([...M.collection.getKeys()])}},$1(()=>{var b;(b=oe.current)!=null&&b.value&&M.setSelectedKeys(new Set([...M.selectedKeys,oe.current.value]))},[oe.current]);const{labelProps:Tt,triggerProps:t5,valueProps:wt,menuProps:s5,descriptionProps:r5,errorMessageProps:It,isInvalid:a5,validationErrors:Ye,validationDetails:n5}=x4({...c,disallowEmptySelection:se,isDisabled:e.isDisabled},M,K),o5=o.useCallback(()=>{var b;M.setSelectedKeys(new Set([])),ne?.(),(b=K.current)==null||b.focus()},[ne,M]),{pressProps:At}=js({isDisabled:!!e?.isDisabled,onPress:o5}),Ne=e.isInvalid||ve==="invalid"||a5,{isPressed:jt,buttonProps:Rt}=Rs(t5,K),{focusProps:Ot,isFocused:Mt,isFocusVisible:Lt}=a1(),{focusProps:Dt,isFocusVisible:$t}=a1(),{isHovered:Pt,hoverProps:kt}=Os({isDisabled:e.isDisabled}),ue=Ms({labelPlacement:e.labelPlacement,label:T}),qe=!!A,Ft=ue==="outside-left"||ue==="outside",l5=ue==="inside",Ut=ue==="outside-left",Bt=e.isClearable,Ht=M.isOpen||qe||!!((l=M.selectedItems)!=null&&l.length)||!!h||!!j||!!e.isMultiline,Gt=!!((i=M.selectedItems)!=null&&i.length),Je=!!T,Vt=Je&&(Ut||Ft&&qe),zt=B(E?.base,Oe),O=o.useMemo(()=>l1({...g,isInvalid:Ne,isClearable:Bt,labelPlacement:ue,disableAnimation:d}),[Ls(g),Ne,ue,d]);Ds({isDisabled:!M.isOpen});const Kt=typeof c.errorMessage=="function"?c.errorMessage({isInvalid:Ne,validationErrors:Ye,validationDetails:n5}):c.errorMessage||Ye?.join(" "),Xe=!!k||!!Kt,i5=!!j;o.useEffect(()=>{if(M.isOpen&&Ze.current&&K.current){let b=K.current.getBoundingClientRect(),Ce=Ze.current;Ce.style.width=b.width+"px"}},[M.isOpen]);const c5=o.useCallback((b={})=>({"data-slot":"base","data-filled":U(Ht),"data-has-value":U(Gt),"data-has-label":U(Je),"data-has-helper":U(Xe),"data-has-end-content":U(i5),"data-invalid":U(Ne),"data-has-label-outside":U(Vt),className:O.base({class:B(zt,b.className)}),...b}),[O,Xe,Gt,Je,Vt,Ht,zt]),d5=o.useCallback((b={})=>({ref:K,"data-slot":"trigger","data-open":U(M.isOpen),"data-disabled":U(e?.isDisabled),"data-focus":U(Mt),"data-pressed":U(jt),"data-focus-visible":U(Lt),"data-hover":U(Pt),className:O.trigger({class:E?.trigger}),...le(Rt,Ot,kt,n1(G,{enabled:ye}),n1(b))}),[O,K,M.isOpen,E?.trigger,e?.isDisabled,Mt,jt,Lt,Pt,Rt,Ot,kt,G,ye]),u5=o.useCallback((b={})=>({state:M,triggerRef:K,selectRef:oe,selectionMode:Q,label:e?.label,name:e?.name,isRequired:e?.isRequired,autoComplete:e?.autoComplete,isDisabled:e?.isDisabled,form:e?.form,onChange:q,...b}),[M,Q,e?.label,e?.autoComplete,e?.name,e?.isDisabled,K]),C5=o.useCallback((b={})=>({"data-slot":"label",className:O.label({class:B(E?.label,b.className)}),...Tt,...b}),[O,E?.label,Tt]),f5=o.useCallback((b={})=>({"data-slot":"value",className:O.value({class:B(E?.value,b.className)}),...wt,...b}),[O,E?.value,wt]),p5=o.useCallback((b={})=>({"data-slot":"listboxWrapper",className:O.listboxWrapper({class:B(E?.listboxWrapper,b?.className)}),style:{maxHeight:Z??256,...b.style},...le(F.scrollShadowProps,b)}),[O.listboxWrapper,E?.listboxWrapper,F.scrollShadowProps,Z]),m5=(b={})=>{const Ce=z??M.collection.size>50;return{state:M,ref:_t,isVirtualized:Ce,virtualization:Ce?{maxListboxHeight:Z,itemHeight:ge}:void 0,"data-slot":"listbox",className:O.listbox({class:B(E?.listbox,b?.className)}),scrollShadowProps:F.scrollShadowProps,...le(F.listboxProps,b,s5)}},h5=o.useCallback((b={})=>{var Ce,Wt;const w5=le(F.popoverProps,b);return{state:M,triggerRef:K,ref:Ze,"data-slot":"popover",scrollRef:_t,triggerType:"listbox",classNames:{content:O.popoverContent({class:B(E?.popoverContent,b.className)})},...w5,offset:M.selectedItems&&M.selectedItems.length>0?M.selectedItems.length*1e-8+(((Ce=F.popoverProps)==null?void 0:Ce.offset)||0):(Wt=F.popoverProps)==null?void 0:Wt.offset}},[O,E?.popoverContent,F.popoverProps,K,M,M.selectedItems]),g5=o.useCallback(()=>({"data-slot":"selectorIcon","aria-hidden":U(!0),"data-open":U(M.isOpen),className:O.selectorIcon({class:E?.selectorIcon})}),[O,E?.selectorIcon,M.isOpen]),x5=o.useCallback((b={})=>({...b,"data-slot":"innerWrapper",className:O.innerWrapper({class:B(E?.innerWrapper,b?.className)})}),[O,E?.innerWrapper]),b5=o.useCallback((b={})=>({...b,"data-slot":"helperWrapper",className:O.helperWrapper({class:B(E?.helperWrapper,b?.className)})}),[O,E?.helperWrapper]),v5=o.useCallback((b={})=>({...b,...r5,"data-slot":"description",className:O.description({class:B(E?.description,b?.className)})}),[O,E?.description]),E5=o.useCallback((b={})=>({...b,"data-slot":"mainWrapper",className:O.mainWrapper({class:B(E?.mainWrapper,b?.className)})}),[O,E?.mainWrapper]),y5=o.useCallback((b={})=>({...b,"data-slot":"end-wrapper",className:O.endWrapper({class:B(E?.endWrapper,b?.className)})}),[O,E?.endWrapper]),N5=o.useCallback((b={})=>({...b,"data-slot":"end-content",className:O.endContent({class:B(E?.endContent,b?.className)})}),[O,E?.endContent]),S5=o.useCallback((b={})=>({...b,...It,"data-slot":"error-message",className:O.errorMessage({class:B(E?.errorMessage,b?.className)})}),[O,It,E?.errorMessage]),_5=o.useCallback((b={})=>({"aria-hidden":U(!0),"data-slot":"spinner",color:"current",size:"sm",...ae,...b,ref:re,className:O.spinner({class:B(E?.spinner,b?.className)})}),[O,re,ae,E?.spinner]),T5=o.useCallback((b={})=>({...b,type:"button",tabIndex:-1,"aria-label":"clear selection","data-slot":"clear-button","data-focus-visible":U($t),className:O.clearButton({class:B(E?.clearButton,b?.className)}),...le(At,Dt)}),[O,$t,At,Dt,E?.clearButton]);return U1.set(M,{isDisabled:e?.isDisabled,isRequired:e?.isRequired,name:e?.name,isInvalid:Ne,validationBehavior:Ee}),{Component:Fe,domRef:oe,state:M,label:T,name:f,triggerRef:K,isLoading:x,placeholder:A,startContent:h,endContent:j,description:k,selectorIcon:w,hasHelper:Xe,labelPlacement:ue,hasPlaceholder:qe,renderValue:R,selectionMode:Q,disableAnimation:d,isOutsideLeft:Ut,shouldLabelBeOutside:Ft,shouldLabelBeInside:l5,isInvalid:Ne,errorMessage:Kt,isClearable:Bt,getClearButtonProps:T5,getBaseProps:c5,getTriggerProps:d5,getLabelProps:C5,getValueProps:f5,getListboxProps:m5,getPopoverProps:h5,getSpinnerProps:_5,getMainWrapperProps:E5,getListboxWrapperProps:p5,getHiddenSelectProps:u5,getInnerWrapperProps:x5,getHelperWrapperProps:b5,getDescriptionProps:v5,getErrorMessageProps:S5,getSelectorIconProps:g5,getEndWrapperProps:y5,getEndContentProps:N5}}var v4=typeof document<"u"?N.useLayoutEffect:()=>{};function E4(e){const s=o.useRef(null);return v4(()=>{s.current=e},[e]),o.useCallback((...r)=>{const a=s.current;return a?.(...r)},[])}function y4(e,s,r){let a=o.useRef(s),n=E4(()=>{r&&r(a.current)});o.useEffect(()=>{var l;let i=(l=e?.current)==null?void 0:l.form;return i?.addEventListener("reset",n),()=>{i?.removeEventListener("reset",n)}},[e,n])}function N4(e,s,r){var a;let n=U1.get(s)||{},{autoComplete:l,name:i=n.name,isDisabled:C=n.isDisabled,selectionMode:p,onChange:c,form:g}=e,{validationBehavior:d,isRequired:m,isInvalid:v}=n,{visuallyHiddenProps:T}=$s();return y4(e.selectRef,s.selectedKeys,s.setSelectedKeys),Ps({validationBehavior:d,focus:()=>{var f;return(f=r.current)==null?void 0:f.focus()}},s,e.selectRef),{containerProps:{...T,"aria-hidden":!0,"data-a11y-ignore":"aria-hidden-focus"},inputProps:{style:{display:"none"}},selectProps:{form:g,autoComplete:l,disabled:C,"aria-invalid":v||void 0,"aria-required":m&&d==="aria"||void 0,required:m&&d==="native",name:i,tabIndex:-1,value:p==="multiple"?[...s.selectedKeys].map(f=>String(f)):(a=[...s.selectedKeys][0])!=null?a:"",multiple:p==="multiple",onChange:f=>{s.setSelectedKeys(f.target.value),c?.(f)}}}}function S4(e){var s;let{state:r,triggerRef:a,selectRef:n,label:l,name:i,isDisabled:C,form:p}=e,{containerProps:c,selectProps:g}=N4({...e,selectRef:n},r,a);return r.collection.size<=300?t.jsx("div",{...c,"data-testid":"hidden-select-container",children:t.jsxs("label",{children:[l,t.jsxs("select",{...g,ref:n,children:[t.jsx("option",{}),[...r.collection.getKeys()].map(d=>{let m=r.collection.getItem(d);if(m?.type==="item")return t.jsx("option",{value:m.key,children:m.textValue},m.key)})]})]})}):i?t.jsx("input",{autoComplete:g.autoComplete,disabled:C,form:p,name:i,type:"hidden",value:(s=[...r.selectedKeys].join(","))!=null?s:""}):null}var _4=ks(function(s,r){var a;const{Component:n,state:l,label:i,hasHelper:C,isLoading:p,triggerRef:c,selectorIcon:g=t.jsx(zs,{}),description:d,errorMessage:m,isInvalid:v,startContent:T,endContent:f,placeholder:x,renderValue:w,shouldLabelBeOutside:D,disableAnimation:I,getBaseProps:S,getLabelProps:h,getTriggerProps:j,getValueProps:k,getListboxProps:R,getPopoverProps:_,getSpinnerProps:A,getMainWrapperProps:z,getInnerWrapperProps:ge,getHiddenSelectProps:Z,getHelperWrapperProps:de,getListboxWrapperProps:se,getDescriptionProps:Q,getErrorMessageProps:re,getSelectorIconProps:Re,isClearable:xe,getClearButtonProps:be,getEndWrapperProps:L,getEndContentProps:ae}=b4({...s,ref:r}),ve=i?t.jsx("label",{...h(),children:i}):null,q=o.cloneElement(g,Re()),J=o.useMemo(()=>{var G;return xe&&((G=l.selectedItems)!=null&&G.length)?t.jsx("span",{...be(),children:t.jsx(Fs,{})}):null},[xe,be,(a=l.selectedItems)==null?void 0:a.length]),Oe=o.useMemo(()=>J?t.jsxs("div",{...L(),children:[J,f&&t.jsx("span",{...ae(),children:f})]}):f&&t.jsx("span",{...ae(),children:f}),[J,f,L,ae]),E=o.useMemo(()=>{const G=v&&m;return!C||!(G||d)?null:t.jsx("div",{...de(),children:G?t.jsx("div",{...re(),children:m}):t.jsx("div",{...Q(),children:d})})},[C,v,m,d,de,re,Q]),Ee=o.useMemo(()=>{var G;if(!((G=l.selectedItems)!=null&&G.length))return x;if(w&&typeof w=="function"){const X=[...l.selectedItems].map(F=>({key:F.key,data:F.value,type:F.type,props:F.props,textValue:F.textValue,rendered:F.rendered,"aria-label":F["aria-label"]}));return w(X)}return l.selectedItems.map(X=>X.textValue).join(", ")},[l.selectedItems,w,x]),Me=o.useMemo(()=>p?t.jsx(Et,{...A()}):q,[p,q,A]),ne=o.useMemo(()=>l.isOpen?t.jsx(Us,{..._(),children:t.jsx(Bs,{...se(),children:t.jsx(Hs,{...R()})})}):null,[l.isOpen,_,l,c,se,R]);return t.jsxs("div",{...S(),children:[t.jsx(S4,{...Z()}),D?ve:null,t.jsxs("div",{...z(),children:[t.jsxs(n,{...j(),children:[D?null:ve,t.jsxs("div",{...ge(),children:[T,t.jsx("span",{...k(),children:Ee}),f&&l.selectedItems&&t.jsx(Gs,{elementType:"span",children:","}),Oe]}),Me]}),E]}),I?ne:t.jsx(Vs,{children:ne})]})}),T4=_4;function w4(){return t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25v13.5m-7.5-13.5v13.5"})})}function I4(){return t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"})})}const A4={[y.PAUSED]:[y.INIT,y.PAUSED,y.STOPPED,y.FINISHED,y.REJECTED,y.AWAITING_USER_INPUT,y.AWAITING_USER_CONFIRMATION],[y.RUNNING]:[y.INIT,y.RUNNING,y.STOPPED,y.FINISHED,y.REJECTED,y.AWAITING_USER_INPUT,y.AWAITING_USER_CONFIRMATION],[y.STOPPED]:[y.INIT,y.STOPPED],[y.USER_CONFIRMED]:[y.RUNNING],[y.USER_REJECTED]:[y.RUNNING],[y.AWAITING_USER_CONFIRMATION]:[]};function j4({isDisabled:e=!1,content:s,action:r,handleAction:a,children:n}){return t.jsx(gt,{content:s,closeDelay:100,children:t.jsx("button",{onClick:()=>a(r),disabled:e,className:"relative overflow-visible cursor-default hover:cursor-pointer group disabled:cursor-not-allowed transition-colors duration-300 ease-in-out border border-transparent hover:border-red-400/40 rounded-full p-1",type:"button",children:t.jsx("span",{className:"relative group-hover:filter group-hover:drop-shadow-[0_0_5px_rgba(255,64,0,0.4)]",children:n})})})}function R4(){const{t:e}=P(),{send:s}=Ke(),{curAgentState:r}=pe(n=>n.agent),a=n=>{A4[n].includes(r)||s(ut(n))};return t.jsx("div",{className:"flex justify-between items-center gap-20",children:t.jsx(j4,{isDisabled:r!==y.RUNNING&&r!==y.PAUSED,content:r===y.PAUSED?e(u.AGENT$RESUME_TASK):e(u.AGENT$PAUSE_TASK),action:r===y.PAUSED?y.RUNNING:y.PAUSED,handleAction:a,children:r===y.PAUSED?t.jsx(I4,{}):t.jsx(w4,{})})})}const O4="/assets/notification-Cyeyy5ud.mp3",M4=()=>{const{data:e}=L1(),s=o.useRef(void 0);return typeof window<"u"&&!s.current&&(s.current=new Audio(O4),s.current.volume=.5),{notify:o.useCallback(async(a,n)=>{if(!(typeof window>"u")&&(n?.playSound===!0&&e?.ENABLE_SOUND_NOTIFICATIONS&&s.current&&!a.includes("BUTTON$")&&(s.current.currentTime=0,s.current.play().catch(()=>{})),Notification.permission==="default"&&await Notification.requestPermission(),Notification.permission==="granted")){const{playSound:l,...i}=n||{};return new Notification(a,i)}},[e?.ENABLE_SOUND_NOTIFICATIONS])}};let Se="",De;const p1=typeof window<"u"&&typeof document<"u",tt="notification",st={startNotification(e){if(!p1)return;Se||(Se=document.title),De&&this.stopNotification(),De=window.setInterval(()=>{document.title=document.title===Se?e:Se},1e3);const s=document.querySelector('link[rel="icon"]');s&&(s.href=s.href.includes(`?${tt}`)?s.href:`${s.href}?${tt}`)},stopNotification(){if(!p1)return;De&&(window.clearInterval(De),De=void 0),Se&&(document.title=Se);const e=document.querySelector('link[rel="icon"]');e&&(e.href=e.href.replace(`?${tt}`,""))}},L4={[y.INIT]:u.CHAT_INTERFACE$AGENT_INIT_MESSAGE,[y.RUNNING]:u.CHAT_INTERFACE$AGENT_RUNNING_MESSAGE,[y.AWAITING_USER_INPUT]:u.CHAT_INTERFACE$AGENT_AWAITING_USER_INPUT_MESSAGE,[y.PAUSED]:u.CHAT_INTERFACE$AGENT_PAUSED_MESSAGE,[y.LOADING]:u.CHAT_INTERFACE$INITIALIZING_AGENT_LOADING_MESSAGE,[y.STOPPED]:u.CHAT_INTERFACE$AGENT_STOPPED_MESSAGE,[y.FINISHED]:u.CHAT_INTERFACE$AGENT_FINISHED_MESSAGE,[y.REJECTED]:u.CHAT_INTERFACE$AGENT_REJECTED_MESSAGE,[y.ERROR]:u.CHAT_INTERFACE$AGENT_ERROR_MESSAGE,[y.AWAITING_USER_CONFIRMATION]:u.CHAT_INTERFACE$AGENT_AWAITING_USER_CONFIRMATION_MESSAGE,[y.USER_CONFIRMED]:u.CHAT_INTERFACE$AGENT_ACTION_USER_CONFIRMED_MESSAGE,[y.USER_REJECTED]:u.CHAT_INTERFACE$AGENT_ACTION_USER_REJECTED_MESSAGE,[y.RATE_LIMITED]:u.CHAT_INTERFACE$AGENT_RATE_LIMITED_MESSAGE};function D4(e,s,r,a){return e==="DISCONNECTED"||s==="STOPPED"||r==="STATUS$STOPPED"||a===y.STOPPED?"bg-red-500":s==="STARTING"||!["STATUS$READY",null].includes(r)||a!=null&&[y.LOADING,y.PAUSED,y.REJECTED,y.RATE_LIMITED].includes(a)?"bg-yellow-500":a===y.AWAITING_USER_CONFIRMATION?"bg-orange-500":a===y.AWAITING_USER_INPUT?"bg-blue-500":"bg-green-500"}function $4(e,s,r,a,n){if(r==="STOPPED"||a==="STATUS$STOPPED")return u.CHAT_INTERFACE$STOPPED;if(a&&!["STATUS$READY","STATUS$RUNTIME_STARTED"].includes(a)){const l=u[a];return l||a}return s==="DISCONNECTED"?u.CHAT_INTERFACE$DISCONNECTED:s==="CONNECTING"?u.CHAT_INTERFACE$CONNECTING:n===y.LOADING&&e?.id&&e.id!=="STATUS$READY"?e.id:n?L4[n]:a&&a!=="STATUS$READY"&&!n?a:"STATUS$ERROR"}const P4=[y.AWAITING_USER_INPUT,y.FINISHED,y.AWAITING_USER_CONFIRMATION];function k4(){const{t:e,i18n:s}=P(),{curAgentState:r}=pe(c=>c.agent),{curStatusMessage:a}=pe(c=>c.status),{webSocketStatus:n}=Ke(),{data:l}=ke(),i=D4(n,l?.status||null,l?.runtime_status||null,r),C=$4(a,n,l?.status||null,l?.runtime_status||null,r),{notify:p}=M4();return N.useEffect(()=>{if(a?.type!=="error")return;let c=a.message||"";if(a?.id){const g=a.id.trim();g==="STATUS$READY"&&(c="awaiting_user_input"),s.exists(g)&&(c=e(a.id.trim())||c)}H5({message:c,source:"agent-status",metadata:{...a}})},[a.id]),N.useEffect(()=>{if(P4.includes(r)){const c=e(C);p(c,{body:e(`Agent state changed to ${r}`),playSound:!0}),typeof document<"u"&&!document.hasFocus()&&st.startNotification(c)}},[r,C]),N.useEffect(()=>{if(typeof window>"u")return;const c=()=>{st.stopNotification()};return window.addEventListener("focus",c),()=>{window.removeEventListener("focus",c),st.stopNotification()}},[]),t.jsx("div",{className:"flex flex-col items-center",children:t.jsxs("div",{className:"flex items-center bg-base-secondary px-2 py-1 text-gray-400 rounded-[100px] text-sm gap-[6px]",children:[t.jsx("div",{className:`w-2 h-2 rounded-full animate-pulse ${i}`}),t.jsx("span",{className:"text-sm text-stone-400",children:e(C)})]})})}function F4(e){return ie({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm0 319.91a20 20 0 1 1 20-20 20 20 0 0 1-20 20zm21.72-201.15-5.74 122a16 16 0 0 1-32 0l-5.74-121.94v-.05a21.74 21.74 0 1 1 43.44 0z"},child:[]}]})(e)}function U4(e){return ie({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M368 192h-16v-80a96 96 0 1 0-192 0v80h-16a64.07 64.07 0 0 0-64 64v176a64.07 64.07 0 0 0 64 64h224a64.07 64.07 0 0 0 64-64V256a64.07 64.07 0 0 0-64-64zm-48 0H192v-80a64 64 0 1 1 128 0z"},child:[]}]})(e)}function B4({onClick:e}){return t.jsx("div",{className:"cursor-pointer hover:opacity-80 transition-all",style:{marginRight:"8px"},onClick:e,children:t.jsx(U4,{size:20})})}function H4({setSecurityOpen:e,showSecurityLock:s}){const{data:r}=ke(),[a,n]=N.useState(!1);return t.jsxs("div",{className:"flex flex-col gap-2 md:items-center md:justify-between md:flex-row",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(R4,{}),t.jsx(k4,{}),s&&t.jsx(B4,{onClick:()=>e(!0)})]}),t.jsx(X5,{variant:"compact",showOptions:!0,title:r?.title??"",lastUpdatedAt:r?.created_at??"",selectedRepository:{selected_repository:r?.selected_repository??null,selected_branch:r?.selected_branch??null,git_provider:r?.git_provider??null},conversationStatus:r?.status,conversationId:r?.conversation_id,contextMenuOpen:a,onContextMenuToggle:n})]})}const G4=e=>{const s=N.useRef(!1);N.useEffect(()=>{s.current||(s.current=!0,e())},[s.current])},V4=e=>new Promise((s,r)=>{const a=new FileReader;a.onloadend=()=>{s(a.result)},a.onerror=r,a.readAsDataURL(e)}),z4=e=>o.createElement("svg",{width:15,height:16,viewBox:"0 0 15 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{d:"M13.3125 6.80003C13.1369 6.58918 12.9171 6.41945 12.6687 6.30282C12.4204 6.18619 12.1494 6.1255 11.875 6.12503H9.025L9.375 5.23128C9.52058 4.83995 9.56907 4.41916 9.51629 4.00498C9.46351 3.5908 9.31106 3.19561 9.07199 2.8533C8.83293 2.51099 8.51439 2.23178 8.14371 2.03962C7.77303 1.84746 7.36127 1.74809 6.94375 1.75003C6.82352 1.75028 6.70592 1.7852 6.60504 1.8506C6.50417 1.91601 6.42429 2.00912 6.375 2.11878L4.59375 6.12503H3.125C2.62772 6.12503 2.15081 6.32257 1.79917 6.6742C1.44754 7.02583 1.25 7.50275 1.25 8.00003V12.375C1.25 12.8723 1.44754 13.3492 1.79917 13.7009C2.15081 14.0525 2.62772 14.25 3.125 14.25H11.0812C11.5199 14.2499 11.9446 14.096 12.2815 13.815C12.6183 13.5341 12.846 13.144 12.925 12.7125L13.7188 8.33753C13.7678 8.06714 13.7569 7.78927 13.6867 7.52358C13.6165 7.25788 13.4887 7.01087 13.3125 6.80003ZM4.375 13H3.125C2.95924 13 2.80027 12.9342 2.68306 12.817C2.56585 12.6998 2.5 12.5408 2.5 12.375V8.00003C2.5 7.83427 2.56585 7.6753 2.68306 7.55809C2.80027 7.44088 2.95924 7.37503 3.125 7.37503H4.375V13ZM12.5 8.11253L11.7062 12.4875C11.6796 12.6331 11.6022 12.7646 11.4877 12.8584C11.3733 12.9523 11.2292 13.0024 11.0812 13H5.625V6.88128L7.325 3.05628C7.49999 3.10729 7.6625 3.19403 7.80229 3.31102C7.94207 3.428 8.05608 3.57269 8.13712 3.73596C8.21817 3.89923 8.26449 4.07752 8.27316 4.25959C8.28183 4.44166 8.25266 4.62355 8.1875 4.79378L7.85625 5.68753C7.78567 5.87644 7.76184 6.07962 7.7868 6.27973C7.81176 6.47985 7.88476 6.67095 7.99958 6.83674C8.11441 7.00253 8.26763 7.13807 8.44619 7.2318C8.62475 7.32554 8.82333 7.37468 9.025 7.37503H11.875C11.9668 7.37488 12.0575 7.39496 12.1407 7.43385C12.2239 7.47274 12.2975 7.52948 12.3563 7.60003C12.4165 7.66961 12.4606 7.75162 12.4854 7.84021C12.5103 7.9288 12.5152 8.02179 12.5 8.11253Z",fill:"white"})),K4=e=>o.createElement("svg",{width:15,height:16,viewBox:"0 0 15 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{d:"M11.8749 1.75H3.91861C3.47998 1.75015 3.05528 1.90407 2.71841 2.18499C2.38154 2.4659 2.15382 2.85603 2.07486 3.2875L1.28111 7.6625C1.23166 7.93277 1.2422 8.21062 1.31201 8.47636C1.38182 8.74211 1.50917 8.98927 1.68507 9.20035C1.86097 9.41142 2.08111 9.58126 2.32991 9.69785C2.57872 9.81443 2.8501 9.87491 3.12486 9.875H5.97486L5.62486 10.7688C5.47928 11.1601 5.4308 11.5809 5.48357 11.995C5.53635 12.4092 5.68881 12.8044 5.92787 13.1467C6.16694 13.489 6.48547 13.7683 6.85615 13.9604C7.22683 14.1526 7.63859 14.2519 8.05611 14.25C8.17634 14.2497 8.29394 14.2148 8.39482 14.1494C8.4957 14.084 8.57557 13.9909 8.62486 13.8813L10.4061 9.875H11.8749C12.3721 9.875 12.8491 9.67746 13.2007 9.32583C13.5523 8.97419 13.7499 8.49728 13.7499 8V3.625C13.7499 3.12772 13.5523 2.65081 13.2007 2.29917C12.8491 1.94754 12.3721 1.75 11.8749 1.75ZM9.37486 9.11875L7.67486 12.9438C7.50092 12.8911 7.3396 12.8034 7.20083 12.6861C7.06206 12.5688 6.94878 12.4242 6.86798 12.2615C6.78717 12.0987 6.74055 11.9211 6.73099 11.7396C6.72143 11.5581 6.74912 11.3766 6.81236 11.2062L7.14361 10.3125C7.2142 10.1236 7.23803 9.92041 7.21307 9.72029C7.18811 9.52018 7.1151 9.32907 7.00028 9.16329C6.88546 8.9975 6.73223 8.86196 6.55367 8.76823C6.37511 8.67449 6.17653 8.62535 5.97486 8.625H3.12486C3.03304 8.62515 2.94232 8.60507 2.85914 8.56618C2.77597 8.52729 2.70238 8.47055 2.64361 8.4C2.58341 8.33042 2.5393 8.24841 2.51445 8.15982C2.4896 8.07123 2.48462 7.97824 2.49986 7.8875L3.29361 3.5125C3.32024 3.3669 3.39767 3.23548 3.51212 3.14162C3.62657 3.04777 3.77062 2.99759 3.91861 3H9.37486V9.11875ZM12.4999 8C12.4999 8.16576 12.434 8.32473 12.3168 8.44194C12.1996 8.55915 12.0406 8.625 11.8749 8.625H10.6249V3H11.8749C12.0406 3 12.1996 3.06585 12.3168 3.18306C12.434 3.30027 12.4999 3.45924 12.4999 3.625V8Z",fill:"white"})),W4=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round",className:"lucide lucide-download",...e},o.createElement("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),o.createElement("polyline",{points:"7 10 12 15 17 10"}),o.createElement("line",{x1:12,x2:12,y1:15,y2:3}));function rt({testId:e,onClick:s,icon:r,tooltip:a}){const n=t.jsx("button",{type:"button","data-testid":e,onClick:s,className:"button-base p-1 hover:bg-neutral-500 cursor-pointer",children:r});return a?t.jsx(gt,{content:a,closeDelay:100,children:n}):n}function Z4({onPositiveFeedback:e,onNegativeFeedback:s,onExportTrajectory:r,isSaasMode:a=!1}){const{t:n}=P();return t.jsxs("div",{"data-testid":"feedback-actions",className:"flex gap-1",children:[!a&&t.jsxs(t.Fragment,{children:[t.jsx(rt,{testId:"positive-feedback",onClick:e,icon:t.jsx(z4,{width:15,height:15}),tooltip:n(u.BUTTON$MARK_HELPFUL)}),t.jsx(rt,{testId:"negative-feedback",onClick:s,icon:t.jsx(K4,{width:15,height:15}),tooltip:n(u.BUTTON$MARK_NOT_HELPFUL)})]}),t.jsx(rt,{testId:"export-trajectory",onClick:r,icon:t.jsx(W4,{width:15,height:15}),tooltip:n(u.BUTTON$EXPORT_CONVERSATION)})]})}function Y4(e,s,r,a){return{action:G5.MESSAGE,args:{content:e,image_urls:s,file_urls:r,timestamp:a}}}function q4({isDisabled:e,onClick:s}){const{t:r}=P();return t.jsx("button",{"aria-label":r(u.BUTTON$SEND),disabled:e,onClick:s,type:"submit",className:"border border-white rounded-lg w-6 h-6 hover:bg-neutral-500 focus:bg-neutral-500 flex items-center justify-center cursor-pointer",children:t.jsx(ns,{})})}function J4({isDisabled:e,onClick:s}){const{t:r}=P();return t.jsx("button",{"data-testid":"stop-button","aria-label":r(u.BUTTON$STOP),disabled:e,onClick:s,type:"button",className:"border border-white rounded-lg w-6 h-6 hover:bg-neutral-500 focus:bg-neutral-500 flex items-center justify-center cursor-pointer",children:t.jsx("div",{className:"w-[10px] h-[10px] bg-white"})})}function X4({name:e,button:s="submit",disabled:r,showButton:a=!0,value:n,maxRows:l=8,onSubmit:i,onStop:C,onChange:p,onFocus:c,onBlur:g,onFilesPaste:d,className:m,buttonClassName:v}){const{t:T}=P(),f=N.useRef(null),[x,w]=N.useState(!1),D=_=>{if(d&&_.clipboardData.files.length>0){const A=Array.from(_.clipboardData.files);_.preventDefault(),d(A)}},I=_=>{_.preventDefault(),_.dataTransfer.types.includes("Files")&&w(!0)},S=_=>{_.preventDefault(),w(!1)},h=_=>{if(_.preventDefault(),w(!1),d&&_.dataTransfer.files.length>0){const A=Array.from(_.dataTransfer.files);A.length>0&&d(A)}},j=()=>{const _=n||f.current?.value||"";_.trim()&&(i(_),p?.(""),f.current&&(f.current.value=""))},k=_=>{_.key==="Enter"&&!_.shiftKey&&!r&&!_.nativeEvent.isComposing&&(_.preventDefault(),j())},R=_=>{p?.(_.target.value)};return t.jsxs("div",{"data-testid":"chat-input",className:"flex items-end justify-end grow gap-1 min-h-6 w-full",children:[t.jsx(f4,{ref:f,name:e,placeholder:T(u.SUGGESTIONS$WHAT_TO_BUILD),onKeyDown:k,onChange:R,onFocus:c,onBlur:g,onPaste:D,onDrop:h,onDragOver:I,onDragLeave:S,value:n,minRows:1,maxRows:l,"data-dragging-over":x,className:V("grow text-sm self-center placeholder:text-neutral-400 text-white resize-none outline-hidden ring-0","transition-all duration-200 ease-in-out",x?"bg-neutral-600/50 rounded-lg px-2":"bg-transparent",m)}),a&&t.jsxs("div",{className:v,children:[s==="submit"&&t.jsx(q4,{isDisabled:r,onClick:j}),s==="stop"&&t.jsx(J4,{isDisabled:r,onClick:C})]})]})}function B1({width:e=20,height:s=20,active:r}){return t.jsx("svg",{width:e,height:s,viewBox:`0 0 ${e} ${s}`,fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.204 15.0037L6.65511 9.99993L11.204 4.99617L12.1289 5.83701L8.34444 9.99993L12.1289 14.1628L11.204 15.0037Z",fill:r?"#D4D4D4":"#525252"})})}function H1({width:e=20,height:s=20,active:r}){return t.jsx("svg",{width:e,height:s,viewBox:`0 0 ${e} ${s}`,fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.79602 4.99634L13.3449 10.0001L8.79602 15.0038L7.87109 14.163L11.6556 10.0001L7.87109 5.83718L8.79602 4.99634Z",fill:r?"#D4D4D4":"#525252"})})}function G1({onClick:e,className:s}){return t.jsx("button",{type:"button",onClick:e,className:V("bg-neutral-400 rounded-full w-5 h-5 flex items-center justify-center",s),children:t.jsx(j1,{width:18,height:18})})}function Q4({src:e,size:s="small"}){return t.jsx("img",{role:"img",alt:"",src:e,className:V("rounded-sm object-cover",s==="small"&&"w-[62px] h-[62px]",s==="large"&&"w-[100px] h-[100px]")})}function e2({src:e,onRemove:s,size:r="small"}){return t.jsxs("div",{"data-testid":"image-preview",className:"relative w-fit shrink-0",children:[t.jsx(Q4,{src:e,size:r}),s&&t.jsx(G1,{onClick:s,className:"absolute right-[3px] top-[3px]"})]})}function V1({size:e="small",images:s,onRemove:r}){const a=N.useRef(null),[n,l]=N.useState(!1),[i,C]=N.useState(!0),[p,c]=N.useState(!1);N.useEffect(()=>{const d=a.current;if(d){const m=d.scrollWidth>d.clientWidth;l(m)}},[s]);const g=d=>{const m=d.currentTarget;C(m.scrollLeft===0),c(m.scrollLeft+m.clientWidth===m.scrollWidth)};return t.jsxs("div",{"data-testid":"image-carousel",className:"relative",children:[n&&t.jsx("div",{className:"absolute right-full transform top-1/2 -translate-y-1/2",children:t.jsx(B1,{active:!i})}),t.jsx("div",{ref:a,onScroll:g,className:V("flex overflow-x-auto",e==="small"&&"gap-2",e==="large"&&"gap-4"),children:s.map((d,m)=>t.jsx(e2,{size:e,src:d,onRemove:r?()=>r?.(m):void 0},m))}),n&&t.jsx("div",{className:"absolute left-full transform top-1/2 -translate-y-1/2",children:t.jsx(H1,{active:!p})})]})}const t2=e=>o.createElement("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.5 3.75C9.25736 3.75 8.25 4.75736 8.25 6V16.5C8.25 18.5711 9.92893 20.25 12 20.25C14.0711 20.25 15.75 18.5711 15.75 16.5V7H17.25V16.5C17.25 19.3995 14.8995 21.75 12 21.75C9.1005 21.75 6.75 19.3995 6.75 16.5V6C6.75 3.92893 8.42893 2.25 10.5 2.25C12.5711 2.25 14.25 3.92893 14.25 6V16C14.25 17.2426 13.2426 18.25 12 18.25C10.7574 18.25 9.75 17.2426 9.75 16V7H11.25V16C11.25 16.4142 11.5858 16.75 12 16.75C12.4142 16.75 12.75 16.4142 12.75 16V6C12.75 4.75736 11.7426 3.75 10.5 3.75Z",fill:"white"}));function s2({onUpload:e,label:s}){const r=a=>{a.target.files&&e(Array.from(a.target.files))};return t.jsxs("label",{className:"cursor-pointer py-[10px]",children:[s||t.jsx(t2,{"data-testid":"default-label",width:24,height:24}),t.jsx("input",{"data-testid":"upload-image-input",type:"file",multiple:!0,hidden:!0,onChange:r})]})}function r2({filename:e,onRemove:s}){return t.jsxs("div",{"data-testid":"file-item",className:"flex flex-row gap-x-1 items-center justify-start",children:[t.jsx(ls,{className:"h-4 w-4"}),t.jsx("code",{className:"text-sm flex-1 text-white truncate",children:e}),s&&t.jsx(G1,{onClick:s})]})}function z1({files:e,onRemove:s}){return t.jsx("div",{"data-testid":"file-list",className:V("flex flex-col gap-y-1.5 justify-start"),children:e.map((r,a)=>t.jsx(r2,{filename:r,onRemove:s?()=>s?.(a):void 0},a))})}const m1=e=>e.type.startsWith("image/"),a2=3*1024*1024,n2=3*1024*1024;function o2(e){const s=e.filter(r=>r.size>a2);if(s.length>0){const r=s.map(a=>a.name);return{isValid:!1,errorMessage:`Files exceeding 3MB are not allowed: ${r.join(", ")}`,oversizedFiles:r}}return{isValid:!0}}function l2(e,s=[]){const r=s.reduce((l,i)=>l+i.size,0),a=e.reduce((l,i)=>l+i.size,0),n=r+a;return n>n2?{isValid:!1,errorMessage:`Total file size would be ${(n/1048576).toFixed(1)}MB, exceeding the 3MB limit. Please select fewer or smaller files.`}:{isValid:!0}}function K1(e,s=[]){const r=o2(e);return r.isValid?l2(e,s):r}function i2({isDisabled:e,mode:s="submit",onSubmit:r,onStop:a,value:n,onChange:l}){const[i,C]=N.useState([]),[p,c]=N.useState([]),g=f=>{const x=K1(f,[...i,...p]);if(!x.isValid){fe(`Error: ${x.errorMessage}`);return}const w=f.filter(I=>!m1(I)),D=f.filter(I=>m1(I));c(I=>[...I,...w]),C(I=>[...I,...D])},d=(f,x)=>{const w=[...f];return w.splice(x,1),w},m=f=>{c(d(p,f))},v=f=>{C(d(i,f))},T=f=>{r(f,i,p),c([]),C([]),f&&l?.("")};return t.jsxs("div",{"data-testid":"interactive-chat-box",className:"flex flex-col gap-[10px]",children:[i.length>0&&t.jsx(V1,{size:"small",images:i.map(f=>URL.createObjectURL(f)),onRemove:v}),p.length>0&&t.jsx(z1,{files:p.map(f=>f.name),onRemove:m}),t.jsxs("div",{className:V("flex items-end gap-1","bg-tertiary border border-neutral-600 rounded-lg px-2","transition-colors duration-200","hover:border-neutral-500 focus-within:border-neutral-500"),children:[t.jsx(s2,{onUpload:g}),t.jsx(X4,{disabled:e,button:s,onChange:l,onSubmit:T,onStop:a,value:n,onFilesPaste:g,className:"py-[10px]",buttonClassName:"py-[10px]"})]})]})}const c2=()=>{const{conversationId:e}=te();return Ie({mutationFn:({feedback:s})=>ce.submitFeedback(e,s),onError:s=>{fe(s.message)},retry:2,retryDelay:500})},d2="1.0",u2="https://www.all-hands.dev/share";function C2({onClose:e,polarity:s}){const{t:r}=P(),a=()=>{Te(r(u.FEEDBACK$PASSWORD_COPIED_MESSAGE),{icon:"📋",position:"bottom-right"})},n=c=>{navigator.clipboard.writeText(c),a()},l=(c,g,d)=>{Te(t.jsxs("div",{className:"flex flex-col gap-1",children:[t.jsx("span",{children:c}),t.jsx("a",{"data-testid":"toast-share-url",className:"text-blue-500 underline",onClick:()=>n(d),href:g,target:"_blank",rel:"noreferrer",children:r(u.FEEDBACK$GO_TO_FEEDBACK)}),t.jsxs("span",{onClick:()=>n(d),className:"cursor-pointer",children:[r(u.FEEDBACK$PASSWORD),": ",d," ",t.jsxs("span",{className:"text-gray-500",children:["(",r(u.FEEDBACK$COPY_LABEL),")"]})]})]}),{duration:1e4})},{mutate:i,isPending:C}=c2(),p=async c=>{c?.preventDefault();const g=new FormData(c.currentTarget),d=g.get("email")?.toString()||"",m=g.get("permissions")?.toString()||"private";i({feedback:{version:d2,email:d,polarity:s,permissions:m,trajectory:[],token:""}},{onSuccess:T=>{const{message:f,feedback_id:x,password:w}=T.body,D=`${u2}?share_id=${x}`;l(f,D,w),e()}})};return t.jsxs("form",{onSubmit:p,className:"flex flex-col gap-6 w-full",children:[t.jsxs("label",{className:"flex flex-col gap-2",children:[t.jsx("span",{className:"text-xs text-neutral-400",children:r(u.FEEDBACK$EMAIL_LABEL)}),t.jsx("input",{required:!0,name:"email",type:"email",placeholder:r(u.FEEDBACK$EMAIL_PLACEHOLDER),className:"bg-[#27272A] px-3 py-[10px] rounded-sm"})]}),t.jsxs("div",{className:"flex gap-4 text-neutral-400",children:[t.jsxs("label",{className:"flex gap-2 cursor-pointer",children:[t.jsx("input",{name:"permissions",value:"private",type:"radio",defaultChecked:!0}),r(u.FEEDBACK$PRIVATE_LABEL)]}),t.jsxs("label",{className:"flex gap-2 cursor-pointer",children:[t.jsx("input",{name:"permissions",value:"public",type:"radio"}),r(u.FEEDBACK$PUBLIC_LABEL)]})]}),t.jsxs("div",{className:"flex gap-2",children:[t.jsx(ze,{type:"submit",variant:"primary",className:"grow",isDisabled:C,children:r(C?u.FEEDBACK$SUBMITTING_LABEL:u.FEEDBACK$SHARE_LABEL)}),t.jsx(ze,{type:"button",variant:"secondary",className:"grow",onClick:e,isDisabled:C,children:r(u.FEEDBACK$CANCEL_LABEL)})]}),C&&t.jsx("p",{className:"text-sm text-center text-neutral-400",children:r(u.FEEDBACK$SUBMITTING_MESSAGE)})]})}function f2({onClose:e,isOpen:s,polarity:r}){const{t:a}=P();return s?t.jsx(D1,{onClose:e,children:t.jsxs(vt,{className:"border border-tertiary",children:[t.jsx(Q5,{title:a(u.FEEDBACK$TITLE)}),t.jsx(es,{description:a(u.FEEDBACK$DESCRIPTION)}),t.jsx(C2,{onClose:e,polarity:r})]})}):null}function p2(){return t.jsxs("div",{className:"flex items-center space-x-1.5 bg-tertiary px-3 py-1.5 rounded-full",children:[t.jsx("span",{className:"w-1.5 h-1.5 bg-gray-400 rounded-full animate-[bounce_0.5s_infinite] translate-y-[1px]",style:{animationDelay:"0ms"}}),t.jsx("span",{className:"w-1.5 h-1.5 bg-gray-400 rounded-full animate-[bounce_0.5s_infinite] translate-y-[1px]",style:{animationDelay:"75ms"}}),t.jsx("span",{className:"w-1.5 h-1.5 bg-gray-400 rounded-full animate-[bounce_0.5s_infinite] translate-y-[1px]",style:{animationDelay:"150ms"}})]})}function m2(){return t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"})})}function h2(){return t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"})})}function h1({type:e,onClick:s}){const{t:r}=P(),a=r(e==="confirm"?u.CHAT_INTERFACE$USER_CONFIRMED:u.CHAT_INTERFACE$USER_REJECTED);return t.jsx(gt,{content:a,closeDelay:100,children:t.jsx("button",{"data-testid":`action-${e}-button`,type:"button","aria-label":r(e==="confirm"?u.ACTION$CONFIRM:u.ACTION$REJECT),className:"bg-tertiary rounded-full p-1 hover:bg-base-secondary",onClick:s,children:e==="confirm"?t.jsx(m2,{}):t.jsx(h2,{})})})}function at(){const{t:e}=P(),{send:s}=Ke(),r=a=>{const n=ut(a);s(n)};return t.jsxs("div",{className:"flex justify-between items-center pt-4",children:[t.jsx("p",{children:e(u.CHAT_INTERFACE$USER_ASK_CONFIRMATION)}),t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx(h1,{type:"confirm",onClick:()=>r(y.USER_CONFIRMED)}),t.jsx(h1,{type:"reject",onClick:()=>r(y.USER_REJECTED)})]})]})}const g2=e=>o.createElement("svg",{width:15,height:15,viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{d:"M11.6938 4.50616C11.6357 4.44758 11.5666 4.40109 11.4904 4.36936C11.4142 4.33763 11.3325 4.32129 11.25 4.32129C11.1675 4.32129 11.0858 4.33763 11.0097 4.36936C10.9335 4.40109 10.8644 4.44758 10.8063 4.50616L6.15003 9.16866L4.19378 7.20616C4.13345 7.14789 4.06224 7.10207 3.98421 7.07132C3.90617 7.04056 3.82284 7.02548 3.73898 7.02693C3.65512 7.02838 3.57236 7.04634 3.49544 7.07977C3.41851 7.1132 3.34893 7.16146 3.29065 7.22179C3.23238 7.28211 3.18656 7.35333 3.15581 7.43136C3.12505 7.5094 3.10997 7.59272 3.11142 7.67659C3.11287 7.76045 3.13083 7.84321 3.16426 7.92013C3.1977 7.99705 3.24595 8.06664 3.30628 8.12491L5.70628 10.5249C5.76438 10.5835 5.83351 10.63 5.90967 10.6617C5.98583 10.6935 6.06752 10.7098 6.15003 10.7098C6.23254 10.7098 6.31423 10.6935 6.39039 10.6617C6.46655 10.63 6.53568 10.5835 6.59378 10.5249L11.6938 5.42491C11.7572 5.36639 11.8078 5.29535 11.8425 5.21629C11.8771 5.13723 11.895 5.05185 11.895 4.96554C11.895 4.87922 11.8771 4.79385 11.8425 4.71478C11.8078 4.63572 11.7572 4.56469 11.6938 4.50616Z",fill:"white"})),x2=e=>o.createElement("svg",{width:15,height:15,viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{d:"M1.25 2.5C1.25 1.80964 1.80964 1.25 2.5 1.25H8.75C9.44036 1.25 10 1.80964 10 2.5V5H12.5C13.1904 5 13.75 5.55964 13.75 6.25V12.5C13.75 13.1904 13.1904 13.75 12.5 13.75H6.25C5.55964 13.75 5 13.1904 5 12.5V10H2.5C1.80964 10 1.25 9.44036 1.25 8.75V2.5ZM6.25 10V12.5H12.5V6.25H10V8.75C10 9.44036 9.44036 10 8.75 10H6.25ZM8.75 8.75V2.5L2.5 2.5V8.75H8.75Z",fill:"white"}));function b2({isHidden:e,isDisabled:s,onClick:r,mode:a}){const{t:n}=P();return t.jsxs("button",{hidden:e,disabled:s,"data-testid":"copy-to-clipboard",type:"button",onClick:r,"aria-label":n(a==="copy"?u.BUTTON$COPY:u.BUTTON$COPIED),className:"button-base p-1 cursor-pointer",children:[a==="copy"&&t.jsx(x2,{width:15,height:15}),a==="copied"&&t.jsx(g2,{width:15,height:15})]})}function _e({type:e,message:s,children:r,actions:a}){const[n,l]=N.useState(!1),[i,C]=N.useState(!1),p=async()=>{await navigator.clipboard.writeText(s),C(!0)};return N.useEffect(()=>{let c;return i&&(c=setTimeout(()=>{C(!1)},2e3)),()=>{clearTimeout(c)}},[i]),t.jsxs("article",{"data-testid":`${e}-message`,onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),className:V("rounded-xl relative w-fit","flex flex-col gap-2",e==="user"&&" max-w-[305px] p-4 bg-tertiary self-end",e==="agent"&&"mt-6 max-w-full bg-transparent"),children:[t.jsxs("div",{className:V("absolute -top-2.5 -right-2.5",n?"flex":"hidden","items-center gap-1"),children:[a?.map((c,g)=>t.jsx("button",{type:"button",onClick:c.onClick,className:"button-base p-1 cursor-pointer","aria-label":`Action ${g+1}`,children:c.icon},g)),t.jsx(b2,{isHidden:!n,isDisabled:i,onClick:p,mode:i?"copied":"copy"})]}),t.jsx("div",{className:"text-sm break-words",children:t.jsx(xt,{components:{code:ht,ul:mt,ol:pt,a:D5,p:as},remarkPlugins:[Ct,ft],children:s})}),r]})}const W1=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",...e},o.createElement("path",{d:"M201.4 374.6c12.5 12.5 32.8 12.5 45.3 0l160-160c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L224 306.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l160 160z"})),Z1=e=>o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",...e},o.createElement("path",{d:"M201.4 137.4c12.5-12.5 32.8-12.5 45.3 0l160 160c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L224 205.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l160-160z"}));function v2({errorId:e,defaultMessage:s}){const{t:r}=P(),[a,n]=N.useState(!1),i=!!e&&W.exists(e)?e:"CHAT_INTERFACE$AGENT_ERROR_MESSAGE";return t.jsxs("div",{className:"flex flex-col gap-2 border-l-2 pl-2 my-2 py-2 border-danger text-sm w-full",children:[t.jsxs("div",{className:"font-bold text-danger",children:[r(i),t.jsx("button",{type:"button",onClick:()=>n(C=>!C),className:"cursor-pointer text-left",children:a?t.jsx(Z1,{className:"h-4 w-4 ml-2 inline fill-danger"}):t.jsx(W1,{className:"h-4 w-4 ml-2 inline fill-danger"})})]}),a&&t.jsx(xt,{components:{code:ht,ul:mt,ol:pt},remarkPlugins:[Ct,ft],children:s})]})}function E2({event:e}){const{t:s}=P();let r;try{r=JSON.parse(e.content)}catch{r=e.content}const a=e.extras.arguments&&Object.keys(e.extras.arguments).length>0;return t.jsxs("div",{className:"flex flex-col gap-4",children:[a&&t.jsxs("div",{className:"flex flex-col gap-2",children:[t.jsx("div",{className:"flex items-center justify-between",children:t.jsx("h3",{className:"text-sm font-semibold text-gray-300",children:s("MCP_OBSERVATION$ARGUMENTS")})}),t.jsx("div",{className:"p-3 bg-gray-900 rounded-md overflow-auto text-gray-300 max-h-[200px] shadow-inner",children:t.jsx(Jt,{name:!1,src:e.extras.arguments,theme:e1,collapsed:1,displayDataTypes:!1})})]}),t.jsxs("div",{className:"flex flex-col gap-2",children:[t.jsx("div",{className:"flex items-center justify-between",children:t.jsx("h3",{className:"text-sm font-semibold text-gray-300",children:s("MCP_OBSERVATION$OUTPUT")})}),t.jsx("div",{className:"p-3 bg-gray-900 rounded-md overflow-auto text-gray-300 max-h-[300px] shadow-inner",children:typeof r=="object"&&r!==null?t.jsx(Jt,{name:!1,src:r,theme:e1,collapsed:1,displayDataTypes:!1}):t.jsx("pre",{className:"whitespace-pre-wrap",children:e.content.trim()||s("OBSERVATION$MCP_NO_OUTPUT")})})]})]})}const ct=e=>{const s=e.content.length>0,r=e.content.toLowerCase().includes("error:");switch(e.observation){case"run":{const a=e.extras.metadata.exit_code;return a===-1?"timeout":a===0?"success":"error"}case"run_ipython":case"read":case"edit":case"mcp":return!s||r?"error":"success";default:return"success"}},y2=e=>{const s=document.createElement("textarea");return s.innerHTML=e,s.value};function g1(e){const{children:s}=e,r=a=>{try{return y2(a)}catch(n){return R1.error(String(n)),a}};if(Array.isArray(s)){const a=s.map(n=>typeof n=="string"?r(n):n);return t.jsx("strong",{className:"font-mono",children:a})}return typeof s=="string"?t.jsx("strong",{className:"font-mono",children:r(s)}):t.jsx("strong",{className:"font-mono",children:s})}const N2=e=>{const s=document.createElement("textarea");return s.innerHTML=e,s.value},S2=e=>e?e.endsWith("/")||e.endsWith("\\")?!0:!(e.split(/[/\\]/).pop()||"").includes("."):!1,_2=e=>{if(!e)return"";const s=e.split(/[/\\]/),r=s[s.length-1];return S2(e)&&!r.endsWith("/")?`${r}/`:r};function x1(e){const{children:s}=e,r=a=>{try{const n=N2(a),l=_2(n);return t.jsx("span",{className:"font-mono",title:n,children:l})}catch(n){return R1.error(String(n)),t.jsx("span",{className:"font-mono",children:a})}};if(Array.isArray(s)){const a=s.map(n=>typeof n=="string"?r(n):n);return t.jsx("strong",{className:"font-mono",children:a})}return typeof s=="string"?t.jsx("strong",{children:r(s)}):t.jsx("strong",{className:"font-mono",children:s})}const Ae=1e3,Y1=e=>`\`\`\`json
${JSON.stringify(e,null,2)}
\`\`\``,q1=e=>{switch(e){case $.LOW:return W.t("SECURITY$LOW_RISK");case $.MEDIUM:return W.t("SECURITY$MEDIUM_RISK");case $.HIGH:return W.t("SECURITY$HIGH_RISK");case $.UNKNOWN:default:return W.t("SECURITY$UNKNOWN_RISK")}},T2=e=>{let{content:s}=e.args;return s.length>Ae&&(s=`${e.args.content.slice(0,Ae)}...`),`${e.args.path}
${s}`},w2=e=>{let s=`Command:
\`${e.args.command}\``;return e.args.confirmation_state==="awaiting_confirmation"&&(s+=`

${q1(e.args.security_risk)}`),s},I2=e=>{let s=`\`\`\`
${e.args.code}
\`\`\``;return e.args.confirmation_state==="awaiting_confirmation"&&(s+=`

${q1(e.args.security_risk)}`),s},A2=e=>`Browsing ${e.args.url}`,j2=e=>`**Action:**

\`\`\`python
${e.args.browser_actions}
\`\`\``,R2=e=>{const s=e.args.name||"",r=e.args.arguments||{};let a=`**MCP Tool Call:** ${s}

`;return e.args.thought&&(a+=`

**Thought:**
${e.args.thought}`),a+=`

**Arguments:**
\`\`\`json
${JSON.stringify(r,null,2)}
\`\`\``,a},O2=e=>e.args.thought,M2=e=>e.args.final_thought.trim(),L2=()=>"",D2=e=>{switch(e.action){case"read":case"edit":return L2();case"write":return T2(e);case"run":return w2(e);case"run_ipython":return I2(e);case"browse":return A2(e);case"browse_interactive":return j2(e);case"call_tool_mcp":return R2(e);case"think":return O2(e);case"finish":return M2(e);default:return Y1(e)}},$2=e=>`\`\`\`
${e.content}
\`\`\``,P2=e=>{let{content:s}=e;return s.length>Ae&&(s=`${s.slice(0,Ae)}...`),`Output:
\`\`\`sh
${s.trim()||W.t("OBSERVATION$COMMAND_NO_OUTPUT")}
\`\`\``},k2=(e,s)=>s?`\`\`\`diff
${e.extras.diff}
\`\`\``:e.content,F2=e=>{let s=`**URL:** ${e.extras.url}
`;return e.extras.error&&(s+=`

**Error:**
${e.extras.error}
`),s+=`

**Output:**
${e.content}`,s.length>Ae&&(s=`${s.slice(0,Ae)}...(truncated)`),s},U2=e=>{let s="";if(e.extras.recall_type==="workspace_context"){if(e.extras.repo_name&&(s+=`

**Repository:** ${e.extras.repo_name}`),e.extras.repo_directory&&(s+=`

**Directory:** ${e.extras.repo_directory}`),e.extras.date&&(s+=`

**Date:** ${e.extras.date}`),e.extras.runtime_hosts&&Object.keys(e.extras.runtime_hosts).length>0){s+=`

**Available Hosts**`;for(const[r,a]of Object.entries(e.extras.runtime_hosts))s+=`

- ${r} (port ${a})`}e.extras.repo_instructions&&(s+=`

**Repository Instructions:**

${e.extras.repo_instructions}`),e.extras.additional_agent_instructions&&(s+=`

**Additional Instructions:**

${e.extras.additional_agent_instructions}`)}if(e.extras.microagent_knowledge&&e.extras.microagent_knowledge.length>0){s+=`

**Triggered Microagent Knowledge:**`;for(const r of e.extras.microagent_knowledge)s+=`

- **${r.name}** (triggered by keyword: ${r.trigger})

\`\`\`
${r.content}
\`\`\``}if(e.extras.custom_secrets_descriptions&&Object.keys(e.extras.custom_secrets_descriptions).length>0){s+=`

**Custom Secrets**`;for(const[r,a]of Object.entries(e.extras.custom_secrets_descriptions))s+=`

- $${r}: ${a}`}return s},B2=e=>{switch(e.observation){case"read":return $2(e);case"edit":return k2(e,ct(e)==="success");case"run_ipython":case"run":return P2(e);case"browse":return F2(e);case"recall":return U2(e);default:return Y1(e)}},b1=e=>typeof e.path=="string",v1=e=>typeof e.command=="string",E1=(e,s)=>e?e.length>s?`${e.substring(0,s)}...`:e:"",Ue=e=>{let s="",r="";if(me(e)){const a=`ACTION_MESSAGE$${e.action.toUpperCase()}`;W.exists(a)?s=t.jsx(it,{i18nKey:a,values:{path:b1(e.args)&&e.args.path,command:v1(e.args)&&E1(e.args.command,80),mcp_tool_name:e.action==="call_tool_mcp"&&e.args.name},components:{path:t.jsx(x1,{}),cmd:t.jsx(g1,{})}}):s=e.action.toUpperCase(),r=D2(e)}if(We(e)){const a=`OBSERVATION_MESSAGE$${e.observation.toUpperCase()}`;W.exists(a)?s=t.jsx(it,{i18nKey:a,values:{path:b1(e.extras)&&e.extras.path,command:v1(e.extras)&&E1(e.extras.command,80),mcp_tool_name:e.observation==="mcp"&&e.extras.name},components:{path:t.jsx(x1,{}),cmd:t.jsx(g1,{})}}):s=e.observation.toUpperCase(),r=B2(e)}return{title:s??W.t("EVENT$UNKNOWN_EVENT"),details:r??W.t("EVENT$UNKNOWN_EVENT")}};function y1({title:e,details:s,success:r}){const[a,n]=N.useState(!1);return t.jsxs("div",{className:"flex flex-col gap-2 border-l-2 pl-2 my-2 py-2 border-neutral-300 text-sm w-full",children:[t.jsxs("div",{className:"flex items-center justify-between font-bold text-neutral-300",children:[t.jsxs("div",{children:[e,s&&t.jsx("button",{type:"button",onClick:()=>n(l=>!l),className:"cursor-pointer text-left",children:a?t.jsx(Z1,{className:"h-4 w-4 ml-2 inline fill-neutral-300"}):t.jsx(W1,{className:"h-4 w-4 ml-2 inline fill-neutral-300"})})]}),r&&t.jsx(lt,{status:r})]}),a&&(typeof s=="string"?t.jsx(xt,{components:{code:ht,ul:mt,ol:pt},remarkPlugins:[Ct,ft],children:s}):s)]})}var Y=(e=>(e.CREATING="creating",e.COMPLETED="completed",e.ERROR="error",e))(Y||{});function $e({status:e,conversationId:s,prUrl:r}){const{t:a}=P(),n=()=>{switch(e){case Y.CREATING:return a("MICROAGENT$STATUS_CREATING");case Y.COMPLETED:return a(r?"MICROAGENT$VIEW_YOUR_PR":"MICROAGENT$STATUS_COMPLETED");case Y.ERROR:return a("MICROAGENT$STATUS_ERROR");default:return""}},l=()=>{switch(e){case Y.CREATING:return t.jsx(Et,{size:"sm"});case Y.COMPLETED:return t.jsx(lt,{status:"success"});case Y.ERROR:return t.jsx(lt,{status:"error"});default:return null}},i=n(),C=!!s,p=!!r,c=()=>p?t.jsx("a",{href:r,target:"_blank",rel:"noopener noreferrer",className:"underline",children:i}):C?t.jsx("a",{href:`/conversations/${s}`,target:"_blank",rel:"noopener noreferrer",className:"underline",children:i}):t.jsx("span",{className:"underline",children:i});return t.jsxs("div",{className:"flex items-center gap-2 mt-2 p-2 text-sm",children:[l(),c()]})}const H2=e=>{const s=O1(e)?e.args.content:e.message;if(!e.args.file_urls||e.args.file_urls.length===0)return s;const r=W.t("CHAT_INTERFACE$AUGMENTED_PROMPT_FILES_TITLE");return s.split(r)[0]},G2=()=>{const{conversationId:e}=te(),s=Ks(),{t:r}=P();return Ie({mutationFn:({rating:a,eventId:n,reason:l})=>ce.submitConversationFeedback(e,a,n,l),onSuccess:(a,{eventId:n})=>{n&&s.invalidateQueries({queryKey:["feedback","exists",e,n]})},onError:a=>{console.error(r("FEEDBACK$FAILED_TO_SUBMIT"),a)}})},J1=o.createContext(void 0);function V2({children:e,value:s}){const r=bt(N.useRef(null)),a=s||r;return t.jsx(J1.Provider,{value:a,children:e})}const N1=1e4;function z2({eventId:e,initiallySubmitted:s=!1,initialRating:r,initialReason:a}){const{t:n}=P(),[l,i]=o.useState(r||null),[C,p]=o.useState(a||null),[c,g]=o.useState(!1),[d,m]=o.useState(null),[v,T]=o.useState(s),[f,x]=o.useState(0),w=o.useContext(J1),D=[n(u.FEEDBACK$REASON_MISUNDERSTOOD_INSTRUCTION),n(u.FEEDBACK$REASON_FORGOT_CONTEXT),n(u.FEEDBACK$REASON_UNNECESSARY_CHANGES),n(u.FEEDBACK$REASON_SHOULD_ASK_FIRST),n(u.FEEDBACK$REASON_OTHER)],I=w?.scrollDomToBottom,S=w?.autoScroll,{mutate:h}=G2();o.useEffect(()=>{T(s)},[s]),o.useEffect(()=>{r&&i(r)},[r]),o.useEffect(()=>{a&&p(a)},[a]);const j=(A,z)=>{h({rating:A,eventId:e,reason:z},{onSuccess:()=>{p(z||null),g(!1),T(!0)}})},k=A=>{if(!v)if(i(A),A<=3){g(!0),x(Math.ceil(N1/1e3));const z=setTimeout(()=>{j(A)},N1);m(z),I&&S&&setTimeout(()=>{I()},100)}else g(!1),j(A)},R=A=>{l&&d&&!v&&(clearTimeout(d),x(0),j(l,A))};o.useEffect(()=>{if(f>0&&c&&!v){const A=setTimeout(()=>{x(f-1)},1e3);return()=>clearTimeout(A)}return()=>{}},[f,c,v]),o.useEffect(()=>()=>{d&&clearTimeout(d)},[d]),o.useEffect(()=>{I&&S&&!v&&setTimeout(()=>{I()},100)},[I,S,v]),o.useEffect(()=>{I&&S&&c&&setTimeout(()=>{I()},100)},[I,S,c]);const _=A=>v?l&&l>=A?"text-yellow-400 cursor-not-allowed":"text-gray-300 opacity-50 cursor-not-allowed":l&&l>=A?"text-yellow-400":"text-gray-300 hover:text-yellow-200";return t.jsxs("div",{className:"mt-3 flex flex-col gap-1",children:[t.jsx("div",{className:"text-sm text-gray-500 mb-1",children:n(v?u.FEEDBACK$THANK_YOU_FOR_FEEDBACK:u.FEEDBACK$RATE_AGENT_PERFORMANCE)}),t.jsx("div",{className:"flex flex-col gap-1",children:t.jsxs("span",{className:"flex gap-2 items-center flex-wrap",children:[[1,2,3,4,5].map(A=>t.jsx("button",{type:"button",onClick:()=>k(A),disabled:v,className:V("text-xl transition-all",_(A)),"aria-label":`Rate ${A} stars`,children:t.jsx(is,{})},A)),v&&C&&l&&l<=3&&t.jsx("span",{className:"text-sm text-gray-500 italic",children:C})]})}),c&&!v&&t.jsxs("div",{className:"mt-1 flex flex-col gap-1",children:[t.jsx("div",{className:"text-xs text-gray-500 mb-1",children:n(u.FEEDBACK$SELECT_REASON)}),f>0&&t.jsx("div",{className:"text-xs text-gray-400 mb-1 italic",children:n(u.FEEDBACK$SELECT_REASON_COUNTDOWN,{countdown:f})}),t.jsx("div",{className:"flex flex-col gap-0.5",children:D.map(A=>t.jsx("button",{type:"button",onClick:()=>R(A),className:"text-sm text-left py-1 px-2 rounded hover:bg-gray-700 transition-colors",children:A},A))})]})]})}const K2=e=>{const{conversationId:s}=te(),{data:r}=yt();return he({queryKey:["feedback","exists",s,e],queryFn:()=>e?ce.checkFeedbackExists(s,e):{exists:!1},enabled:!!e&&r?.APP_MODE==="saas",staleTime:1e3*60*5,gcTime:1e3*60*15})},S1=e=>"thought"in e&&!!e.thought;function W2({event:e,hasObservationPair:s,isAwaitingUserConfirmation:r,isLastMessage:a,microagentStatus:n,microagentConversationId:l,microagentPRUrl:i,actions:C,isInLast10Actions:p}){const c=a&&e.source==="agent"&&r,{data:g}=yt(),{data:d={exists:!1},isLoading:m}=K2(e.id),v=()=>g?.APP_MODE!=="saas"||m||!(Zt(e)?p:a)?null:t.jsx(z2,{eventId:e.id,initiallySubmitted:d.exists,initialRating:d.rating,initialReason:d.reason});if(Zt(e))return t.jsxs("div",{children:[t.jsx(v2,{errorId:e.extras.error_id,defaultMessage:e.message}),n&&C&&t.jsx($e,{status:n,conversationId:l,prUrl:i}),v()]});if(s&&me(e))return S1(e.args)?t.jsxs("div",{children:[t.jsx(_e,{type:"agent",message:e.args.thought,actions:C}),n&&C&&t.jsx($e,{status:n,conversationId:l,prUrl:i})]}):n&&C?t.jsx($e,{status:n,conversationId:l,prUrl:i}):null;if(M1(e))return t.jsxs(t.Fragment,{children:[t.jsx(_e,{type:"agent",message:Ue(e).details,actions:C}),n&&C&&t.jsx($e,{status:n,conversationId:l,prUrl:i}),v()]});if(O1(e)||Yt(e)){const T=H2(e);return t.jsxs(t.Fragment,{children:[t.jsxs(_e,{type:e.source,message:T,actions:C,children:[e.args.image_urls&&e.args.image_urls.length>0&&t.jsx(V1,{size:"small",images:e.args.image_urls}),e.args.file_urls&&e.args.file_urls.length>0&&t.jsx(z1,{files:e.args.file_urls}),c&&t.jsx(at,{})]}),n&&C&&t.jsx($e,{status:n,conversationId:l,prUrl:i}),Yt(e)&&e.action==="message"&&v()]})}return V5(e)?t.jsx("div",{children:t.jsx(_e,{type:"agent",message:e.content})}):z5(e)?t.jsxs("div",{children:[t.jsx(y1,{title:Ue(e).title,details:t.jsx(E2,{event:e}),success:ct(e)}),c&&t.jsx(at,{})]}):t.jsxs("div",{children:[me(e)&&S1(e.args)&&t.jsx(_e,{type:"agent",message:e.args.thought}),t.jsx(y1,{title:Ue(e).title,details:Ue(e).details,success:We(e)?ct(e):void 0}),c&&t.jsx(at,{})]})}class Z2{static async getPrompt(s,r){const{data:a}=await ee.get(`/api/conversations/${s}/remember_prompt`,{params:{event_id:r}});return a.prompt}}const Y2=e=>{const{conversationId:s}=te();return he({queryKey:["memory","prompt",s,e],queryFn:()=>Z2.getPrompt(s,e),enabled:!!s,staleTime:1e3*60*5,gcTime:1e3*60*15})};function q2(){const{t:e}=P();return t.jsxs(vt,{children:[t.jsx("h2",{className:"font-bold text-[20px] leading-6 -tracking-[0.01em] flex items-center gap-2",children:e("MICROAGENT$ADD_TO_MICROAGENT")}),t.jsx(Et,{size:"lg"}),t.jsx("p",{children:e("MICROAGENT$WAIT_FOR_RUNTIME")})]})}function J2(){const{t:e}=P();return t.jsx("textarea",{required:!0,disabled:!0,defaultValue:"",placeholder:e("MICROAGENT$LOADING_PROMPT"),rows:6,className:V("bg-tertiary border border-[#717888] w-full rounded p-2 placeholder:italic placeholder:text-tertiary-alt resize-none","disabled:bg-[#2D2F36] disabled:border-[#2D2F36] disabled:cursor-not-allowed")})}const nt=e=>ce.getConversationUrl(e);class X1{static async getFiles(s,r){const a=`${nt(s)}/list-files`,{data:n}=await ee.get(a,{params:{path:r}});return n}static async getFile(s,r){const a=`${nt(s)}/select-file`,{data:n}=await ee.get(a,{params:{file:r}});return n.code}static async uploadFiles(s,r){const a=new FormData;for(const i of r)a.append("files",i);const n=`${nt(s)}/upload-files`;return(await ee.post(n,a,{headers:{"Content-Type":"multipart/form-data"}})).data}}const X2=e=>{const{conversationId:s}=te();return he({queryKey:["files","microagents",s,e],queryFn:()=>X1.getFiles(s,e),enabled:!!s,select:r=>r.map(a=>a.replace(e,""))})};function Q2({onClose:e,onLaunch:s,eventId:r,isLoading:a,selectedRepo:n}){const{t:l}=P(),{runtimeActive:i}=$5(),{data:C,isLoading:p}=Y2(r),{data:c,isLoading:g}=X2(`${n}/.openhands/microagents`),[d,m]=N.useState([]),v=f=>{const x=f.get("query-input")?.toString(),w=f.get("target-input")?.toString();x&&w&&s(x,w,d)},T=f=>{f.preventDefault();const x=new FormData(f.currentTarget);v(x)};return t.jsxs(D1,{onClose:e,children:[!i&&t.jsx(q2,{}),i&&t.jsxs(vt,{className:"items-start w-[728px]",children:[t.jsxs("div",{className:"flex items-center justify-between w-full",children:[t.jsxs("h2",{className:"font-bold text-[20px] leading-6 -tracking-[0.01em] flex items-center gap-2",children:[l("MICROAGENT$ADD_TO_MICROAGENT"),t.jsx("a",{href:"https://docs.all-hands.dev/usage/prompting/microagents-overview#microagents-overview",target:"_blank",rel:"noopener noreferrer",children:t.jsx(o1,{className:"text-primary"})})]}),t.jsx("button",{type:"button",onClick:e,children:t.jsx(j1,{})})]}),t.jsxs("form",{"data-testid":"launch-microagent-modal",onSubmit:T,className:"flex flex-col gap-6 w-full",children:[t.jsxs("label",{htmlFor:"query-input",className:"flex flex-col gap-2.5 w-full text-sm",children:[l("MICROAGENT$WHAT_TO_REMEMBER"),p&&t.jsx(J2,{}),!p&&t.jsx("textarea",{required:!0,"data-testid":"query-input",name:"query-input",defaultValue:C,placeholder:l("MICROAGENT$DESCRIBE_WHAT_TO_ADD"),rows:6,className:V("bg-tertiary border border-[#717888] w-full rounded p-2 placeholder:italic placeholder:text-tertiary-alt resize-none","disabled:bg-[#2D2F36] disabled:border-[#2D2F36] disabled:cursor-not-allowed")})]}),t.jsx(Ws,{testId:"target-input",name:"target-input",label:l("MICROAGENT$WHERE_TO_PUT"),placeholder:l("MICROAGENT$SELECT_FILE_OR_CUSTOM"),required:!0,allowsCustomValue:!0,isLoading:g,items:c?.map(f=>({key:f,label:f}))||[]}),t.jsxs("label",{htmlFor:"trigger-input",className:"flex flex-col gap-2.5 w-full text-sm",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[l("MICROAGENT$ADD_TRIGGERS"),t.jsx("a",{href:"https://docs.all-hands.dev/usage/prompting/microagents-keyword",target:"_blank",rel:"noopener noreferrer",children:t.jsx(o1,{className:"text-primary"})})]}),t.jsx(P5,{name:"trigger-input",value:d,placeholder:l("MICROAGENT$TYPE_TRIGGER_SPACE"),onChange:m})]}),t.jsxs("div",{className:"flex items-center justify-end gap-2",children:[t.jsx(ze,{type:"button",variant:"secondary",onClick:e,children:l("MICROAGENT$CANCEL")}),t.jsx(ze,{type:"submit",variant:"primary",isDisabled:a||p||g,children:l("MICROAGENT$LAUNCH")})]})]})]})]})}const er=[/https?:\/\/github\.com\/[^/\s]+\/[^/\s]+\/pull\/\d+/gi,/https?:\/\/gitlab\.com\/[^/\s]+\/[^/\s]+\/-\/merge_requests\/\d+/gi,/https?:\/\/[^/\s]*gitlab[^/\s]*\/[^/\s]+\/[^/\s]+\/-\/merge_requests\/\d+/gi,/https?:\/\/bitbucket\.org\/[^/\s]+\/[^/\s]+\/pull-requests\/\d+/gi,/https?:\/\/dev\.azure\.com\/[^/\s]+\/[^/\s]+\/_git\/[^/\s]+\/pullrequest\/\d+/gi,/https?:\/\/[^/\s]+\/[^/\s]+\/[^/\s]+\/(?:pull|pr)\/\d+/gi];function tr(e){const s=[];for(const r of er){const a=e.match(r);a&&s.push(...a)}return[...new Set(s)]}function sr(e){const s=tr(e);return s.length>0?s[0]:null}const rr=e=>o.createElement("svg",{id:"Layer_1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 22.3 18.66",...e},o.createElement("defs",null,o.createElement("style",null,`
      .st0 {
        stroke-miterlimit: 10;
      }

      .st0, .st1 {
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-width: 2px;
      }

      .st1 {
        stroke-linejoin: round;
      }
    `)),o.createElement("path",{className:"st1",d:"M15.15,12.54h3.26c1.58,0,2.93-1.29,2.9-2.88-.03-1.53-1.28-2.77-2.82-2.77-.04,0-.08,0-.11,0,.13-.44.16-.92.04-1.43-.27-1.17-1.27-2.05-2.46-2.17-.74-.07-1.43.14-1.97.55,0,0,0-.02,0-.03,0-1.56-1.26-2.82-2.82-2.82s-2.82,1.26-2.82,2.82c0,0,0,.02,0,.03-.54-.4-1.23-.62-1.97-.55-1.19.12-2.19,1-2.46,2.17-.12.5-.09.99.04,1.43-.04,0-.08,0-.11,0-1.56,0-2.82,1.26-2.82,2.82s1.26,2.82,2.82,2.82l1.29.03c.41,0,.74.34.74.75v1.85c0,1.38,1.12,2.5,2.5,2.5h.29c1.44,0,2.6-1.17,2.6-2.6V6.49"}),o.createElement("polyline",{className:"st0",points:"7.97 9.74 11.22 6.49 14.47 9.74"})),Q1=N.memo(({messages:e,isAwaitingUserConfirmation:s})=>{const{createConversationAndSubscribe:r,isPending:a}=k5(),{getOptimisticUserMessage:n}=k1(),{conversationId:l}=te(),{data:i}=J5(l),C=n(),[p,c]=N.useState(null),[g,d]=N.useState(!1),[m,v]=N.useState([]),T=N.useCallback(S=>me(S)?!!e.some(h=>We(h)&&h.cause===S.id):!1,[e]),f=N.useCallback(S=>m.find(j=>j.eventId===S)?.status||null,[m]),x=N.useCallback(S=>m.find(j=>j.eventId===S)?.conversationId||void 0,[m]),w=N.useCallback(S=>m.find(j=>j.eventId===S)?.prUrl||void 0,[m]),D=N.useCallback((S,h)=>{const j=R=>typeof R=="object"&&R!==null&&"error"in R&&R.error===!0,k=R=>Qe(R)&&qt(R)&&R.extras.agent_state===y.ERROR;if(j(S)||k(S))v(R=>R.map(_=>_.conversationId===h?{..._,status:Y.ERROR}:_));else if(Qe(S)&&qt(S))S.extras.agent_state===y.FINISHED&&v(R=>R.map(_=>_.conversationId===h?{..._,status:Y.COMPLETED}:_));else if(Qe(S)&&M1(S)){const R=sr(S.args.final_thought||"");R&&v(_=>_.map(A=>A.conversationId===h?{...A,status:Y.COMPLETED,prUrl:R}:A))}},[v]),I=(S,h,j)=>{const k=`Target file: ${h}

Description: ${S}

Triggers: ${j.join(", ")}`;!i||!i.selected_repository||!i.selected_branch||!i.git_provider||!p||r({query:S,conversationInstructions:k,repository:{name:i.selected_repository,branch:i.selected_branch,gitProvider:i.git_provider},onSuccessCallback:R=>{d(!1),v(_=>[..._.filter(A=>A.eventId!==p),{eventId:p,conversationId:R,status:Y.CREATING}])},onEventCallback:(R,_)=>{D(R,_)}})};return t.jsxs(t.Fragment,{children:[e.map((S,h)=>t.jsx(W2,{event:S,hasObservationPair:T(S),isAwaitingUserConfirmation:s,isLastMessage:e.length-1===h,microagentStatus:f(S.id),microagentConversationId:x(S.id),microagentPRUrl:w(S.id),actions:i?.selected_repository?[{icon:t.jsx(rr,{className:"w-[14px] h-[14px] text-white"}),onClick:()=>{c(S.id),d(!0)}}]:void 0,isInLast10Actions:e.length-1-h<10},h)),C&&t.jsx(_e,{type:"user",message:C}),i?.selected_repository&&g&&p&&Cs.createPortal(t.jsx(Q2,{onClose:()=>d(!1),onLaunch:I,selectedRepo:i.selected_repository.split("/").pop()||"",eventId:p,isLoading:a}),document.getElementById("modal-portal-exit")||document.body)]})},(e,s)=>e.messages.length===s.messages.length);Q1.displayName="Messages";function Ge({suggestion:e,onClick:s}){const{t:r}=P();return t.jsx("li",{className:"list-none border border-neutral-600 rounded-xl hover:bg-tertiary flex-1",children:t.jsx("button",{type:"button","data-testid":"suggestion",onClick:()=>s(e.value),className:"text-[16px] leading-6 -tracking-[0.01em] text-center w-full p-3 font-semibold cursor-pointer",children:r(e.label)})})}function ar({suggestions:e,onSuggestionClick:s}){return t.jsx("ul",{"data-testid":"suggestions",className:"flex flex-col gap-4 w-full",children:e.map((r,a)=>t.jsx(Ge,{suggestion:r,onClick:s},a))})}const nr=e=>o.createElement("svg",{width:88,height:104,viewBox:"0 0 88 104",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("g",{clipPath:"url(#clip0_8467_30797)"},o.createElement("path",{d:"M74.1747 56.6311C73.5609 56.6016 72.9881 56.2174 72.7425 55.6026C72.4064 54.7604 72.8098 53.7999 73.6428 53.457C76.361 52.3488 79.3422 51.7784 82.2621 51.8109C83.1594 51.8227 83.8843 52.5675 83.8726 53.4777C83.8609 54.388 83.1244 55.118 82.2241 55.1061C79.731 55.0766 77.1852 55.5642 74.8616 56.51C74.6365 56.6016 74.3998 56.64 74.1718 56.6282L74.1747 56.6311Z",fill:"white"}),o.createElement("path",{d:"M70.8756 52.133C70.5424 52.1152 70.2092 51.997 69.9315 51.7665C69.2329 51.1931 69.1248 50.1558 69.6918 49.4495C72.1294 46.4084 74.8184 43.5387 77.6916 40.9143C78.358 40.3055 79.3897 40.3557 79.9918 41.0325C80.5939 41.7064 80.5413 42.7496 79.8749 43.3584C77.1274 45.8675 74.5525 48.6131 72.223 51.5212C71.8839 51.9468 71.3783 52.1536 70.8756 52.13V52.133Z",fill:"white"}),o.createElement("path",{d:"M66.1371 48.9823C65.8916 48.9704 65.646 48.9025 65.4181 48.7695C64.6347 48.3232 64.36 47.3154 64.8013 46.5234C66.1868 44.0438 67.0344 41.2391 67.2507 38.4108C67.3208 37.5035 68.11 36.8238 69.0014 36.8947C69.8987 36.9657 70.571 37.7577 70.5008 38.665C70.2465 41.9721 69.2586 45.2496 67.6394 48.1459C67.3267 48.7074 66.7362 49.0118 66.14 48.9823H66.1371Z",fill:"white"}),o.createElement("path",{d:"M29.8105 34.0286C30.5938 34.4394 31.269 35.1191 31.8944 35.7722L38.1902 43.3085C36.8749 44.7211 35.545 46.1575 34.639 47.8686C34.2473 48.6104 33.9141 49.4291 33.2535 49.9433C32.5433 50.493 31.5963 50.5817 30.7136 50.7265C29.4802 50.9334 28.276 51.2939 27.1332 51.8052C27.1332 51.8052 19.7297 45.395 19.4023 43.8818C19.4023 43.8818 29.2142 33.7182 29.8105 34.0315V34.0286Z",fill:"#CF8329"}),o.createElement("path",{d:"M29.1701 35.1662C29.3776 35.2785 29.5676 35.4085 29.7634 35.5385C29.9593 35.6686 29.5413 35.3376 29.8453 35.6065C29.9447 35.6922 30.044 35.7779 30.1405 35.8666C30.4298 36.1296 30.7192 36.4074 30.9793 36.7C31.0582 36.7887 31.1313 36.8803 31.2073 36.9719L32.5693 38.6033C33.8232 40.1046 35.08 41.6089 36.3339 43.1102C36.6467 43.4856 36.9594 43.8609 37.2751 44.2362V42.3832C36.3485 43.3792 35.4395 44.387 34.627 45.4834C34.2441 46.0006 33.8875 46.5355 33.5806 47.1C33.4316 47.3749 33.2942 47.6586 33.1481 47.9364C33.075 48.0753 33.0019 48.2112 32.9201 48.3442C32.8938 48.3885 32.6512 48.7461 32.7973 48.554C32.7184 48.6575 32.6219 48.7462 32.5343 48.8437C32.4115 48.9501 32.4057 48.9619 32.5196 48.8703C32.4553 48.9146 32.391 48.956 32.3238 48.9944C32.2566 49.0328 32.0082 49.1481 32.2186 49.0624C32.0754 49.1215 31.9263 49.1629 31.7773 49.2013C31.2365 49.3431 30.6783 49.3993 30.1317 49.5027C28.872 49.7362 27.6649 50.147 26.4928 50.6642L28.0507 50.877C25.7767 48.9087 23.4852 46.8813 21.4831 44.6263C21.3399 44.4638 21.1967 44.2983 21.0593 44.1298C20.9979 44.053 20.9395 43.9762 20.8781 43.8993C20.7758 43.7752 21.1061 44.2126 20.9833 44.0412C20.957 44.0028 20.9278 43.9643 20.9015 43.9289C20.8167 43.8077 20.7349 43.6836 20.6647 43.5535C20.6413 43.5092 20.6238 43.4501 20.5917 43.4087C20.7963 43.6954 20.6998 43.7279 20.6589 43.5476L20.3228 44.8066C21.4013 43.6895 22.4915 42.5812 23.5875 41.4789C25.3734 39.682 27.1592 37.8762 29.0473 36.1887C29.2373 36.0173 29.4302 35.8488 29.6261 35.6833C29.7108 35.6095 29.7985 35.5385 29.8862 35.4676C29.9213 35.4381 29.9563 35.4115 29.9914 35.3849C30.1639 35.24 29.6816 35.6124 29.854 35.4912C29.971 35.4085 30.0879 35.3287 30.2048 35.2489C30.4181 35.107 30.0849 35.2903 30.0353 35.308C30.0499 35.3021 30.0645 35.2991 30.0762 35.2962C29.7664 35.3789 29.4916 35.311 29.173 35.1721C29.7927 35.444 30.5994 35.3553 30.9413 34.6785C31.2336 34.1022 31.1196 33.186 30.4532 32.8905C29.477 32.459 28.7785 33.0294 28.0507 33.6175C27.3697 34.1672 26.7296 34.7701 26.0924 35.3701C24.403 36.9601 22.7633 38.6033 21.1324 40.2553C20.2497 41.1479 19.3261 42.0197 18.4902 42.9566C17.4964 44.0737 18.762 45.4539 19.4898 46.2991C20.2585 47.1887 21.0973 48.0162 21.942 48.8289C23.3333 50.1677 24.7625 51.471 26.221 52.7359C26.7003 53.1497 27.2147 53.197 27.7788 52.9487C27.8578 52.9132 27.9367 52.8778 28.0156 52.8453C27.9045 52.8896 27.9191 52.8837 28.0624 52.8275C28.2027 52.7743 28.343 52.7182 28.4862 52.6679C28.7697 52.5645 29.0561 52.4729 29.3484 52.3872C29.9447 52.2128 30.5526 52.0798 31.1635 51.9793C32.4086 51.7754 33.6771 51.5183 34.5539 50.5135C35.4308 49.5086 35.878 48.2024 36.6437 47.1148C36.67 47.0764 36.9652 46.6774 36.8425 46.8399C36.9272 46.7306 37.012 46.6212 37.0997 46.5148C37.3014 46.2636 37.5089 46.0154 37.7193 45.773C38.1724 45.2499 38.6429 44.7446 39.1135 44.2392C39.625 43.6895 39.5782 42.9418 39.1135 42.3862C37.1172 39.9982 35.1239 37.6103 33.1276 35.2223C32.3706 34.318 31.4937 33.4579 30.4561 32.8935C29.8599 32.5684 29.0123 32.7457 28.6878 33.387C28.3634 34.0283 28.5359 34.8263 29.176 35.175L29.1701 35.1662Z",fill:"#231F20"}),o.createElement("path",{d:"M29.2868 6.20326C28.7373 6.80615 28.5094 7.66322 28.749 8.44639C29.7779 11.786 26.4108 17.5342 23.7072 17.4692C19.3873 17.3687 3.92852 19.7714 1.53766 40.4148C0.982323 45.2025 2.83246 56.1847 3.70931 60.3755C3.91683 61.3626 4.7206 62.1073 5.70851 62.2314C7.16407 62.4147 8.42088 61.2148 8.33319 59.7341C7.83047 51.3615 8.29227 37.9086 19.9426 47.3865C20.8662 48.1372 22.2019 48.0604 23.0466 47.2181L32.7007 37.5658C33.5161 36.7501 33.6272 35.4586 32.9608 34.5129C31.0493 31.791 28.3106 25.9245 35.2347 20.7851C35.9304 20.2679 37.6519 19.0119 39.5722 19.532C40.3321 19.7389 41.1447 19.5793 41.7351 19.0503L45.8884 15.3295C46.7214 14.5847 46.9172 13.3553 46.3677 12.3771C44.1932 8.52028 39.8674 4.06947 35.7521 1.74358C34.8139 1.21161 33.633 1.41553 32.9053 2.21644L29.2839 6.19734L29.2868 6.20326Z",fill:"#C9C7C7"}),o.createElement("path",{d:"M28.2728 5.17518C27.3462 6.23912 27.0247 7.5454 27.393 8.9167C27.4368 9.0822 27.4661 9.25066 27.4982 9.41911C27.5274 9.56688 27.4777 9.21815 27.4748 9.23588C27.4748 9.28317 27.4865 9.34227 27.4894 9.3866C27.4982 9.49004 27.5011 9.59053 27.5041 9.69396C27.507 9.86538 27.5041 10.0368 27.4924 10.2082C27.4865 10.3028 27.4748 10.3973 27.469 10.4919C27.4573 10.6131 27.393 10.6456 27.4865 10.3678C27.469 10.418 27.469 10.4801 27.4602 10.5333C27.3959 10.9264 27.3024 11.3135 27.1855 11.6948C27.127 11.8898 27.0598 12.0819 26.9896 12.274C26.9721 12.3243 26.9341 12.3893 26.9253 12.4395C26.9253 12.4454 27.0481 12.1528 26.9838 12.2947C26.9341 12.4011 26.8903 12.5104 26.8435 12.6168C26.6681 13.004 26.4694 13.3793 26.2531 13.7428C26.1566 13.9024 26.0573 14.0591 25.952 14.2157C25.8965 14.2985 25.841 14.3783 25.7825 14.458C25.7504 14.5024 25.7124 14.5467 25.6831 14.594C25.7094 14.5526 25.8936 14.3251 25.765 14.4817C25.5282 14.7684 25.2886 15.0491 25.0197 15.3062C24.9349 15.386 24.6631 15.5456 24.6309 15.6491C24.6339 15.6402 24.9524 15.4156 24.7683 15.5427C24.7391 15.5634 24.7128 15.584 24.6835 15.6047C24.6075 15.6579 24.5345 15.7082 24.4556 15.7555C24.327 15.8353 24.1808 15.8885 24.0551 15.9712C23.9441 16.0421 24.2743 15.8855 24.2568 15.8914C24.2188 15.9003 24.1808 15.921 24.1428 15.9357C24.0668 15.9594 23.9908 15.986 23.9148 16.0037C23.8593 16.0185 23.8008 16.0244 23.7453 16.0392C23.5612 16.0894 24.1136 16.0274 23.8564 16.0214C23.757 16.0214 23.6576 16.0214 23.5583 16.0214C22.6522 16.0126 21.752 16.1101 20.8547 16.2313C18.3644 16.5711 15.9356 17.3543 13.6821 18.4685C10.733 19.9255 8.08197 22.0652 6.04769 24.6689C3.27687 28.2124 1.54656 32.4268 0.660951 36.8392C0.25468 38.8193 -0.0229867 40.8289 0.000395797 42.8563C0.0208555 44.6325 0.169919 46.4058 0.359902 48.1701C0.754481 51.8555 1.31566 55.5261 2.00544 59.1642C2.13113 59.8262 2.21004 60.5502 2.45556 61.1797C3.01381 62.622 4.23555 63.5411 5.75833 63.6859C6.91869 63.7952 8.17258 63.2396 8.89159 62.3146C9.29201 61.7974 9.60768 61.2152 9.70413 60.5561C9.77428 60.0715 9.75966 59.6252 9.73336 59.1405C9.66029 57.7278 9.62229 56.3122 9.6486 54.8966C9.66321 54.1755 9.69244 53.4514 9.74505 52.7332C9.77135 52.3875 9.80058 52.0387 9.83858 51.6929C9.84735 51.6072 9.85612 51.5215 9.86781 51.4358C9.89704 51.1964 9.89704 51.2851 9.86196 51.4772C9.89411 51.2999 9.91165 51.1196 9.93795 50.9423C10.0373 50.295 10.163 49.6537 10.3325 49.0213C10.4115 48.7316 10.4962 48.445 10.5956 48.1613C10.6424 48.0253 10.7037 47.8923 10.7447 47.7564C10.7447 47.7475 10.5868 48.1287 10.6716 47.9366C10.6833 47.91 10.695 47.8805 10.7067 47.8539C10.7388 47.78 10.771 47.7091 10.8031 47.6382C10.9142 47.3958 11.0369 47.1594 11.1743 46.9318C11.2386 46.8225 11.3088 46.7161 11.3818 46.6097C11.4169 46.5594 11.4578 46.5092 11.49 46.459C11.5134 46.4205 11.2415 46.7663 11.3351 46.6629C11.3643 46.6304 11.3906 46.5949 11.4169 46.5594C11.5689 46.3762 11.7326 46.2048 11.9109 46.0482C11.9547 46.0097 12.0015 45.9743 12.0453 45.9358C12.1242 45.862 11.6858 46.19 11.9167 46.0393C12.019 45.9713 12.1213 45.9033 12.2294 45.8442C12.323 45.791 12.4194 45.7497 12.5159 45.7024C12.6854 45.6167 12.1359 45.8383 12.3785 45.7615C12.4428 45.7408 12.5042 45.7172 12.5685 45.6994C12.6825 45.664 12.7965 45.6374 12.9134 45.6137C12.9718 45.6019 13.0361 45.5989 13.0946 45.5842C12.9368 45.6255 12.7322 45.6226 12.9952 45.6019C13.2583 45.5812 13.5242 45.6019 13.7902 45.6196C13.9977 45.6344 13.4219 45.5576 13.6967 45.6078C13.7698 45.6196 13.8428 45.6314 13.913 45.6462C14.0767 45.6787 14.2374 45.7172 14.3952 45.7615C14.5414 45.8029 14.6875 45.8501 14.8337 45.8974C14.9155 45.927 14.9973 45.9565 15.0763 45.9861C15.123 46.0038 15.1698 46.0245 15.2166 46.0422C15.3832 46.1043 14.9243 45.9122 15.0879 45.989C15.4562 46.1664 15.8274 46.3319 16.184 46.5299C16.5757 46.7486 16.9585 46.985 17.3327 47.2362C17.5373 47.3722 17.7419 47.514 17.9435 47.6588C18.0488 47.7357 18.154 47.8096 18.2563 47.8894C18.303 47.9219 18.3469 47.9573 18.3936 47.9898C18.4287 48.0164 18.6859 48.2145 18.5164 48.0844C18.3673 47.9692 18.5252 48.0933 18.5486 48.111C18.6099 48.1613 18.6742 48.2115 18.7356 48.2617C18.8262 48.3327 18.9168 48.4066 19.0074 48.4775C19.3348 48.7346 19.6212 48.9178 20.0012 49.0745C20.6588 49.3464 21.3311 49.3966 22.0296 49.2961C22.8188 49.1838 23.5349 48.7671 24.099 48.2085C24.5637 47.7505 25.0197 47.2865 25.4815 46.8284C27.3141 44.996 29.1467 43.1667 30.9764 41.3343L33.148 39.1621C33.8436 38.4646 34.5159 37.7701 34.7292 36.7476C34.9689 35.6038 34.7 34.5369 34.057 33.5853C33.7735 33.1656 33.5104 32.7312 33.2737 32.282C33.1422 32.0308 33.0165 31.7736 32.8996 31.5106C32.8703 31.4456 32.844 31.3806 32.8148 31.3156C32.7213 31.0998 32.8265 31.3333 32.8616 31.4367C32.8119 31.289 32.7505 31.1441 32.6979 30.9964C32.4933 30.4083 32.3618 29.8142 32.2536 29.2025C32.2127 28.9719 32.2887 29.5601 32.2624 29.2704C32.2536 29.1906 32.2449 29.1138 32.239 29.034C32.2244 28.8626 32.2156 28.6941 32.2127 28.5227C32.2069 28.1947 32.2244 27.8725 32.2536 27.5445C32.2828 27.2164 32.2069 27.8253 32.2478 27.5918C32.2624 27.509 32.2741 27.4263 32.2887 27.3406C32.3208 27.1603 32.3618 26.98 32.4056 26.7997C32.4465 26.6401 32.4933 26.4835 32.543 26.3269C32.5722 26.2412 32.6014 26.1555 32.6307 26.0727C32.6482 26.0254 32.6745 25.9752 32.6862 25.9249C32.6745 25.9663 32.5547 26.2353 32.6336 26.055C32.7943 25.6885 32.9697 25.3309 33.1772 24.9851C33.2737 24.8226 33.3789 24.663 33.4841 24.5034C33.5426 24.4147 33.6069 24.332 33.6683 24.2433C33.7764 24.0808 33.4754 24.4827 33.601 24.332C33.642 24.2847 33.677 24.2345 33.7179 24.1842C34.019 23.8089 34.3464 23.4513 34.6912 23.1173C34.8578 22.9548 35.0303 22.7952 35.2057 22.6415C35.4015 22.4701 35.6295 22.21 35.8604 22.0948C35.8195 22.1154 35.6061 22.2869 35.837 22.1154C35.875 22.0859 35.913 22.0563 35.9539 22.0297C36.0036 21.9943 36.0504 21.9588 36.1 21.9233C36.2286 21.8317 36.3572 21.7401 36.4859 21.6544C36.7957 21.4505 37.1143 21.2791 37.4445 21.1165C37.731 20.9747 37.2224 21.1875 37.4621 21.1047C37.5614 21.0692 37.6608 21.0338 37.7602 21.0013C37.9063 20.9569 38.2922 20.7885 38.4412 20.8476C38.4588 20.8535 38.1489 20.8476 38.3155 20.8683C38.3594 20.8742 38.4149 20.8594 38.4588 20.8594C38.5727 20.8535 38.6867 20.8535 38.8036 20.8594C39.1544 20.8771 38.6429 20.8092 38.8913 20.8624C39.0287 20.8919 39.1661 20.9274 39.3034 20.9569C40.569 21.2347 41.7732 20.9097 42.7524 20.0615C43.0183 19.8309 43.2755 19.5916 43.5386 19.3581C44.4856 18.5099 45.4355 17.6617 46.3825 16.8105C46.6397 16.58 46.9232 16.3584 47.1424 16.0924C47.9871 15.0757 48.3583 13.669 47.917 12.3834C47.5604 11.349 46.8618 10.4033 46.2276 9.52551C45.5203 8.54432 44.7662 7.5986 43.9595 6.70016C42.3578 4.91511 40.5749 3.30442 38.64 1.89766C37.7368 1.24156 36.7518 0.496806 35.6821 0.151026C34.4107 -0.259772 32.9931 0.145115 32.0227 1.0406C31.3183 1.68783 30.7075 2.471 30.0644 3.17734C29.4594 3.83935 28.8573 4.50431 28.2523 5.16631C27.7437 5.72488 27.6619 6.67356 28.2523 7.21735C28.8076 7.73159 29.7371 7.81434 30.2807 7.21735C30.9004 6.53466 31.52 5.85492 32.1426 5.17223C32.7651 4.48953 33.3526 3.76251 34.0132 3.12414C34.2762 2.87294 33.8495 3.20098 34.0658 3.0739C34.1155 3.04435 34.434 2.84338 34.247 2.95864C34.0862 3.05913 34.3288 2.935 34.4019 2.91727C34.4574 2.90249 34.6474 2.82565 34.4633 2.89362C34.3025 2.95273 34.4574 2.89362 34.5276 2.89362C34.5773 2.89362 34.6269 2.89362 34.6766 2.89362C34.928 2.88476 34.6445 2.90545 34.6094 2.87294C34.6532 2.91431 34.8958 2.94387 34.9514 2.95569C35.1034 2.98524 34.852 2.92613 34.8432 2.90545C34.8491 2.92022 34.966 2.96455 34.9835 2.97342C35.1121 3.04139 35.2378 3.11528 35.3635 3.18916C35.8195 3.4581 36.2637 3.74477 36.6992 4.04622C36.9067 4.19104 37.1143 4.33585 37.3159 4.48658C37.427 4.56933 37.5381 4.65208 37.6462 4.73483C37.693 4.77029 37.7427 4.80576 37.7865 4.84418C37.5293 4.63435 37.7544 4.82053 37.8099 4.86487C39.5723 6.25685 41.1887 7.84389 42.6354 9.56688C42.8137 9.77967 42.9862 9.99246 43.1586 10.2082C43.2434 10.3146 43.3282 10.421 43.41 10.5303C43.4421 10.5747 43.4918 10.619 43.5152 10.6692C43.4655 10.5717 43.3837 10.4919 43.4889 10.6367C43.7987 11.0564 44.0998 11.482 44.3862 11.9194C44.6551 12.3331 44.9065 12.7557 45.1491 13.1843C45.2514 13.3616 45.0906 13.2079 45.1315 13.1193C45.1228 13.137 45.1725 13.2434 45.1783 13.2641C45.1929 13.3114 45.2017 13.3616 45.2163 13.4118C45.266 13.5951 45.1549 13.4414 45.2163 13.3409C45.19 13.3852 45.2221 13.5833 45.2251 13.6394C45.228 13.8167 45.152 13.6749 45.2426 13.5685C45.2163 13.601 45.2134 13.7192 45.2017 13.7635C45.19 13.8108 45.1695 13.8581 45.1608 13.9054C45.1228 14.0945 45.2864 13.666 45.1929 13.8345C45.1374 13.9379 45.0848 14.0413 45.0234 14.1418C44.8889 14.3664 45.2046 13.9763 45.0058 14.1536C44.9532 14.2009 44.9065 14.2541 44.8539 14.3014C44.7808 14.3694 44.7077 14.4314 44.6347 14.4965L43.4772 15.5309C42.615 16.3052 41.7498 17.0765 40.8876 17.8508C40.8262 17.907 40.7648 17.9602 40.7035 18.0163C40.4959 18.2055 40.911 17.9099 40.6655 18.0311C40.6216 18.0518 40.569 18.0784 40.5281 18.105C40.3673 18.2114 40.7707 18.04 40.5866 18.0843C40.4872 18.1079 40.3849 18.1345 40.2855 18.1641C40.1014 18.2173 40.531 18.1641 40.341 18.1552C40.2709 18.1523 40.2007 18.1611 40.1306 18.1552C40.1189 18.1552 39.9728 18.1523 39.9728 18.1464C39.9698 18.1198 40.2446 18.2055 40.0225 18.1434C39.9202 18.1139 39.8179 18.0902 39.7126 18.0695C39.3794 18.0016 39.0345 17.9572 38.6955 17.9484C38.1694 17.9365 37.6199 18.0163 37.1113 18.1611C35.384 18.6576 33.8378 19.8812 32.5781 21.1313C31.3183 22.3814 30.231 23.9921 29.6991 25.7654C28.8924 28.4636 29.5033 31.3451 30.8039 33.7744C31.0377 34.2118 31.2949 34.6345 31.5726 35.0423C31.6515 35.1576 31.7334 35.2699 31.8035 35.3881C31.7977 35.3792 31.9029 35.5565 31.897 35.5654C31.8824 35.5831 31.821 35.2758 31.8503 35.4442C31.8707 35.5595 31.9058 35.7132 31.9496 35.8196C32.014 35.9762 31.9058 35.6807 31.935 35.6925C31.9438 35.6954 31.9409 35.8964 31.9409 35.8846C31.9409 35.9289 31.9263 35.988 31.935 36.0294C31.9672 36.1979 31.9263 35.8757 31.9555 35.8994C31.9876 35.926 31.8824 36.1831 31.8766 36.2274C31.8561 36.4018 31.9175 36.0767 31.9321 36.1062C31.9438 36.1299 31.8415 36.2924 31.824 36.3249C31.8006 36.3693 31.7743 36.4077 31.748 36.452C31.6515 36.6175 31.6837 36.4225 31.8035 36.3811C31.7655 36.3929 31.6983 36.4993 31.6661 36.5289C31.6574 36.5377 31.6486 36.5466 31.6398 36.5555C31.5901 36.6057 31.5375 36.6559 31.4878 36.7062L30.8477 37.3475C29.1262 39.0675 27.4047 40.7905 25.6802 42.5106C24.4614 43.7282 23.2601 44.9665 22.0209 46.1664C21.9916 46.193 21.9624 46.2196 21.9361 46.2491C21.7549 46.4353 22.1699 46.1073 21.8835 46.2816C21.8426 46.3082 21.7987 46.3289 21.7578 46.3555C21.5386 46.4915 21.9507 46.3141 21.7929 46.3496C21.7023 46.3703 21.6087 46.4058 21.5152 46.4294C21.4217 46.453 21.296 46.4117 21.4626 46.4442C21.6204 46.4767 21.5181 46.4412 21.4538 46.4383C21.3837 46.4353 21.0534 46.4087 21.2551 46.4412C21.4684 46.4767 21.0651 46.3762 21.0154 46.3703C20.8751 46.3526 21.1177 46.4028 21.1352 46.4235C21.1148 46.3969 21.0329 46.3732 21.0037 46.3585C20.9453 46.3289 20.8927 46.2875 20.8342 46.258C20.691 46.1871 20.9394 46.323 20.9365 46.3467C20.9511 46.2728 20.1152 45.6994 20.0655 45.661C19.6563 45.3477 19.2413 45.0404 18.8145 44.7537C17.5051 43.87 16.0057 43.0455 14.4332 42.7854C13.1355 42.5726 11.7881 42.6731 10.6424 43.3883C9.62521 44.0237 8.85067 44.999 8.33625 46.0807C7.14082 48.5898 6.86608 51.4861 6.77547 54.2316C6.72871 55.6354 6.74917 57.0422 6.81055 58.446C6.82516 58.777 6.8427 59.111 6.86023 59.442C6.869 59.6193 6.86316 59.7996 6.87777 59.9739C6.89239 60.1631 6.945 59.7227 6.89239 59.903C6.87777 59.9503 6.87485 60.0005 6.86316 60.0478C6.85147 60.0951 6.83685 60.1424 6.82516 60.1867C6.74624 60.497 6.83978 60.1483 6.84562 60.1513C6.85731 60.1601 6.72286 60.3788 6.71117 60.4025C6.60595 60.6093 6.91869 60.2015 6.69071 60.432C6.65856 60.4645 6.45104 60.6418 6.45689 60.6596C6.45104 60.63 6.71702 60.5059 6.57672 60.565C6.5095 60.5916 6.43935 60.6537 6.37797 60.6891C6.33997 60.7098 6.28736 60.7276 6.25229 60.7542C6.12076 60.8517 6.37505 60.6714 6.37797 60.7039C6.38089 60.7276 6.08569 60.7808 6.05646 60.7926C5.92201 60.8428 6.15876 60.766 6.18214 60.7808C6.16168 60.7689 6.02139 60.7896 5.98631 60.7896C5.93663 60.7896 5.88694 60.7896 5.83725 60.7837C5.62389 60.7719 5.68234 60.6684 5.93663 60.8014C5.89863 60.7837 5.61219 60.7601 5.60635 60.7187C5.60635 60.7128 5.91909 60.8842 5.64142 60.7335C5.59173 60.7069 5.54497 60.6803 5.49528 60.6537C5.48651 60.6478 5.35499 60.565 5.35499 60.565C5.3696 60.5443 5.62389 60.8192 5.40467 60.5946C5.36668 60.5561 5.32576 60.5177 5.28776 60.4763C5.26146 60.4497 5.23807 60.4231 5.21469 60.3936C5.14454 60.3079 5.165 60.3374 5.28192 60.4823C5.28776 60.4793 5.09778 60.2133 5.11532 60.1897C5.12116 60.1808 5.21469 60.5355 5.12993 60.2222C5.11532 60.166 5.09778 60.1128 5.08609 60.0567C5.05101 59.9148 5.02471 59.7671 4.99548 59.6222C4.86395 58.9691 4.73827 58.316 4.61844 57.6628C4.30862 55.9694 4.02803 54.273 3.77375 52.5707C3.7036 52.1008 3.63637 51.6309 3.57207 51.161C3.54284 50.9393 3.51069 50.7206 3.48146 50.499C3.46685 50.3807 3.44931 50.2596 3.4347 50.1414C3.42885 50.0911 3.42301 50.0379 3.41424 49.9877C3.43178 50.0793 3.44347 50.2271 3.42301 50.0497C3.3061 49.104 3.19795 48.1583 3.10734 47.2096C2.94659 45.5191 2.82091 43.8168 2.84721 42.1204C2.85306 41.7717 2.86475 41.423 2.88813 41.0772C2.8969 40.9294 2.90859 40.7817 2.92321 40.6339C2.93198 40.5571 2.93782 40.4832 2.94659 40.4093C2.95536 40.3354 2.96413 40.2645 2.97289 40.1906C2.98459 40.0931 2.98459 40.0931 2.97289 40.1965L2.99043 40.0517C3.16288 38.7779 3.39086 37.513 3.68898 36.2629C3.95496 35.1517 4.27647 34.0522 4.66228 32.9794C4.75581 32.7223 4.85226 32.4652 4.95164 32.2081C4.9721 32.1549 4.99548 32.1017 5.01594 32.0455C5.01009 32.0574 4.91949 32.279 4.98379 32.1283C5.04225 31.9894 5.1007 31.8475 5.15916 31.7086C5.36375 31.2328 5.58004 30.7659 5.81387 30.3048C6.24937 29.4359 6.73748 28.5937 7.27527 27.7868C7.39511 27.6095 7.51494 27.4322 7.64062 27.2549C7.70785 27.1603 7.77215 27.0687 7.83938 26.9771C7.8686 26.9386 7.89783 26.9002 7.92706 26.8588C8.12289 26.5929 7.778 27.0509 7.89783 26.8973C8.17258 26.5515 8.4444 26.2057 8.73083 25.8717C9.27155 25.2482 9.84735 24.6571 10.4553 24.0985C10.7505 23.8266 11.0545 23.5665 11.3643 23.3124C11.4403 23.2503 11.5163 23.1883 11.5952 23.1291C11.8495 22.9252 11.3818 23.2887 11.5864 23.1351C11.7413 23.0198 11.8933 22.9045 12.0511 22.7922C13.2232 21.9499 14.4829 21.2288 15.7982 20.6378C15.9414 20.5727 16.0846 20.5107 16.2278 20.4516C16.4617 20.3511 16.3535 20.4279 16.1782 20.4693C16.2658 20.4486 16.3564 20.3984 16.4383 20.3659C16.7364 20.2476 17.0375 20.1383 17.3385 20.0349C17.9085 19.8398 18.4843 19.6654 19.0688 19.5177C19.5862 19.3847 20.1093 19.2694 20.6384 19.1748C20.8839 19.1305 21.1294 19.0921 21.3749 19.0566C21.4334 19.0478 21.4918 19.0418 21.5503 19.033C21.752 19.0034 21.1411 19.0832 21.41 19.0507C21.5211 19.0389 21.6321 19.0241 21.7461 19.0123C22.6113 18.9207 23.5027 18.9916 24.3591 18.8527C25.9725 18.5926 27.2205 17.2804 28.1558 16.0214C29.1584 14.6708 29.9095 13.0158 30.2018 11.349C30.3596 10.4476 30.3947 9.58462 30.2544 8.68027C30.2281 8.51181 30.193 8.34335 30.1492 8.17785C30.12 8.07441 30.0907 7.97098 30.0674 7.86754C30.0089 7.62224 30.0498 8.0685 30.0703 7.82025C30.0703 7.79661 30.0761 7.61042 30.0791 7.61337C30.0995 7.62224 30.0206 7.87936 30.0703 7.72568C30.082 7.6843 30.1813 7.39468 30.1697 7.37399C30.1813 7.39468 30.0469 7.61337 30.1346 7.48334C30.1638 7.44196 30.1813 7.38876 30.2077 7.34443C30.2252 7.31488 30.3216 7.18189 30.3216 7.16711C30.3246 7.19075 30.0966 7.44787 30.2866 7.22917C30.7805 6.66174 30.8828 5.73079 30.2866 5.17814C29.7429 4.67572 28.7872 4.56933 28.2581 5.17814L28.2728 5.17518Z",fill:"#231F20"}),o.createElement("path",{d:"M73.8511 93.2459C76.3501 90.5949 78.6357 87.6218 80.3543 84.3886C81.9327 81.4244 83.3648 77.6828 82.3331 74.293C81.655 72.0647 80.0328 70.0964 78.1973 68.7339C77.2649 68.0424 76.1572 67.5163 75.0231 67.2799C74.5847 67.1883 74.1492 67.1439 73.7166 67.1292C73.8657 65.5155 73.629 63.8812 72.983 62.3828C72.1559 60.4677 70.6009 58.8541 68.8122 57.8345C67.3683 57.0099 65.7754 56.5844 64.1561 56.4927C64.083 53.0674 61.9085 49.787 58.9711 48.1556C57.875 47.5468 56.6737 47.1419 55.4374 46.9321C54.8528 43.4743 51.9066 40.6607 48.6623 39.6352C44.9357 38.459 40.7269 39.384 37.6053 41.6774C35.6499 43.1108 34.0804 44.9165 32.6921 46.8907C32.2069 46.8907 31.7246 46.9084 31.2453 46.9646C30.0001 47.1094 28.8427 47.4049 27.6853 47.8926C26.6711 48.3181 25.6948 48.8767 24.7625 49.4589C21.3837 51.5661 18.4258 54.5067 16.7773 58.2039C15.2516 61.6233 14.8103 65.3944 15.2692 69.1063C15.7777 73.232 17.1953 77.1716 19.1156 80.8362C21.2259 84.8615 23.9499 88.5675 26.9955 91.9189C29.1613 94.301 31.5492 96.4614 34.171 98.3173C36.8308 100.203 39.7536 101.719 42.8488 102.724C46.1165 103.785 49.6181 104.213 53.0407 103.906C57.1238 103.537 61.0433 102.227 64.6442 100.262C68.0259 98.4149 71.203 96.0683 73.8569 93.2489L73.8511 93.2459ZM78.3259 72.8449C78.3259 72.8449 78.3464 72.8685 78.3551 72.8833C78.5071 73.0724 78.3668 72.9099 78.3259 72.8449ZM76.7534 71.2372C76.8177 71.2844 76.8733 71.3229 76.7534 71.2372V71.2372Z",fill:"#FFE165"}),o.createElement("path",{d:"M31.0027 59.1286C30.3333 61.8092 30.9384 64.6877 32.3588 67.0166C33.9664 69.6498 36.4712 71.7215 39.0667 73.3056C41.4137 74.736 44.1904 75.7379 46.9671 75.469C49.7437 75.2 52.4766 73.8819 53.9526 71.3817C55.2503 69.1858 55.5104 66.0649 54.0081 63.8957C53.1137 62.6042 51.816 61.818 50.4715 61.0733C50.1909 60.9166 49.9103 60.76 49.6356 60.5945C49.4894 60.5058 49.3462 60.4142 49.203 60.3197C49.1329 60.2724 49.0627 60.2221 48.9926 60.1748C48.6535 59.9414 49.2059 60.3699 48.8961 60.098C48.6535 59.8852 48.4197 59.6695 48.2034 59.4271C48.0894 59.3 47.9842 59.1641 47.8731 59.037C47.7241 58.8656 48.0017 59.2262 47.8731 59.037C47.8117 58.9483 47.7474 58.8626 47.689 58.774C46.8735 57.5741 46.2977 56.2294 45.6226 54.9497C45.0234 53.8148 44.3102 52.748 43.4509 51.7934C41.8755 50.0408 39.8763 48.7493 37.7076 47.8834C35.6821 47.0736 33.4081 46.7101 31.2394 46.9613C29.9943 47.1061 28.8369 47.4017 27.6794 47.8893C26.6652 48.3149 25.689 48.8734 24.7566 49.4557C21.3778 51.5628 18.42 54.5034 16.7715 58.2006C15.2458 61.62 14.8044 65.3911 15.2633 69.1031C15.7719 73.2288 17.1894 77.1683 19.1097 80.833C21.22 84.8582 23.9441 88.5643 26.9896 91.9157C29.1554 94.2977 31.5434 96.4581 34.1651 98.3141C36.7869 100.17 39.7477 101.716 42.843 102.721C46.1107 103.782 49.6122 104.21 53.0348 103.903C57.118 103.533 61.0375 102.224 64.6384 100.259C68.0201 98.4116 71.1972 96.065 73.8511 93.2456C76.3501 90.5946 78.6357 87.6215 80.3543 84.3883C81.9327 81.4241 83.3648 77.6825 82.3331 74.2927C81.655 72.0644 80.0328 70.0961 78.1973 68.7336C77.2649 68.0421 76.1572 67.516 75.0231 67.2796C73.6669 66.9988 72.3575 67.117 71.0189 67.4303C66.357 68.5268 62.2241 71.4083 59.0616 74.9784C56.6971 77.65 54.3004 80.8803 54.8177 84.6809C55.148 87.1073 56.9835 89.2499 59.0207 90.4557C60.0671 91.0763 61.2479 91.4635 62.4638 91.5315C63.6125 91.5965 64.805 91.2773 65.863 90.8458C68.2685 89.8617 70.2531 88.1446 72.036 86.2709C72.5563 85.7241 72.6176 84.7548 72.036 84.2198C71.4544 83.6849 70.5629 83.6347 70.0076 84.2198C69.5399 84.7104 69.0635 85.1922 68.5637 85.6503C68.3591 85.8394 68.1487 86.0256 67.9382 86.2059C67.8213 86.3064 67.7015 86.4068 67.5816 86.5044C67.5232 86.5517 67.4618 86.5989 67.4034 86.6492C67.1812 86.8295 67.5436 86.5487 67.3946 86.6551C66.9386 86.9831 66.4827 87.3053 65.9946 87.586C65.7695 87.7161 65.5386 87.8372 65.3048 87.9495C65.1966 88.0027 65.0885 88.05 64.9803 88.1003C64.7465 88.2067 65.0066 88.0884 65.0446 88.0766C64.9657 88.0973 64.8868 88.1387 64.8079 88.1682C64.3227 88.3515 63.82 88.4904 63.3114 88.5849C63.2442 88.5968 63.1799 88.6086 63.1127 88.6204C62.8262 88.6736 63.1916 88.5997 63.1974 88.6115C63.1974 88.6086 62.8671 88.6352 62.8321 88.6352C62.7239 88.6352 62.6187 88.6352 62.5106 88.6322C62.4492 88.6322 62.3878 88.6234 62.3264 88.6204C62.0634 88.6056 62.3644 88.6382 62.3703 88.6293C62.344 88.6766 61.7419 88.4963 61.6834 88.4815C61.5782 88.452 61.47 88.4194 61.3677 88.384C61.3064 88.3633 61.2479 88.3397 61.1865 88.319C60.9352 88.2333 61.3794 88.4135 61.2187 88.3337C60.7627 88.1032 60.3185 87.8816 59.9005 87.5831C59.9005 87.5831 59.7456 87.4856 59.7397 87.4649C59.7397 87.4737 60.0291 87.7013 59.8099 87.5181C59.7222 87.4442 59.6316 87.3703 59.5439 87.2934C59.3335 87.1073 59.1318 86.9122 58.9389 86.7053C58.8804 86.6433 58.4683 86.2443 58.4946 86.1793C58.4917 86.1881 58.6817 86.4423 58.5531 86.2532C58.518 86.2 58.4771 86.1468 58.442 86.0936C58.3602 85.9753 58.2842 85.8512 58.2111 85.7271C58.0913 85.5232 57.9802 85.3133 57.8838 85.0976C57.8721 85.074 57.8165 84.9291 57.8107 84.9262C57.8224 84.9262 57.9159 85.2188 57.8633 85.0533C57.8165 84.8966 57.7581 84.7459 57.7172 84.5863C57.6821 84.4445 57.6529 84.3026 57.6265 84.1607C57.6207 84.1282 57.5973 83.945 57.6207 84.1282C57.6441 84.3233 57.6207 84.0898 57.6149 84.0396C57.5915 83.747 57.5915 83.4515 57.6149 83.1589C57.6207 83.085 57.6295 83.0111 57.6353 82.9372C57.6499 82.7303 57.5915 83.1766 57.6324 82.9727C57.6587 82.8456 57.6762 82.7156 57.7025 82.5885C57.7639 82.2959 57.8458 82.0063 57.9393 81.7225C57.9627 81.6516 57.989 81.5807 58.0153 81.5098C58.1059 81.2556 58.0124 81.5009 57.9977 81.5452C58.0357 81.4063 58.1205 81.2645 58.1819 81.1374C58.2374 81.0221 58.2988 80.9069 58.3602 80.7946C58.5765 80.4015 58.8161 80.0232 59.0616 79.6508C59.237 79.3878 59.4153 79.1307 59.6024 78.8736C59.7017 78.7347 59.807 78.5987 59.9063 78.4598C59.6053 78.8765 59.8216 78.5721 59.8917 78.4805C59.9531 78.4007 60.0145 78.3239 60.0759 78.2441C60.6224 77.5525 61.2041 76.8846 61.812 76.2462C62.5164 75.5103 63.2588 74.8129 64.0392 74.1656C64.2233 74.0119 64.4104 73.8612 64.6004 73.7135C64.8108 73.548 64.5741 73.7401 64.5419 73.7578C64.5975 73.7282 64.6559 73.6721 64.7085 73.6307C64.8342 73.5361 64.9628 73.4416 65.0914 73.3499C65.5444 73.0249 66.0092 72.7175 66.4856 72.4279C66.962 72.1382 67.4793 71.8486 67.9908 71.5915C68.2451 71.4644 68.4994 71.3432 68.7595 71.225C68.8998 71.163 69.0401 71.1039 69.1804 71.0418C68.894 71.1659 69.274 71.0034 69.3704 70.9679C69.9579 70.7433 70.5541 70.5453 71.1621 70.3798C71.4544 70.3 71.7467 70.2291 72.0419 70.164C72.1821 70.1345 72.3195 70.1049 72.4598 70.0783C72.5358 70.0636 72.6118 70.0517 72.6849 70.037C72.9479 69.9808 72.5153 70.0488 72.6849 70.034C72.9421 70.0133 73.1993 69.9926 73.4565 69.9956C73.5909 69.9956 73.7254 70.0044 73.8598 70.0133C73.9095 70.0163 73.9592 70.0251 74.0089 70.0281C74.1843 70.0399 73.7312 69.9808 73.9914 70.0281C74.2369 70.0724 74.4795 70.1197 74.7192 70.1877C74.8448 70.2231 74.9734 70.2645 75.0962 70.3059C75.143 70.3236 75.1897 70.3384 75.2365 70.3561C75.558 70.4684 74.9471 70.2202 75.2599 70.365C75.7334 70.5807 76.1835 70.832 76.6073 71.1334C76.6482 71.163 76.6891 71.1925 76.73 71.2221C76.9434 71.3757 76.692 71.1984 76.6687 71.1718C76.7534 71.2694 76.8879 71.3491 76.9843 71.4319C77.1919 71.6122 77.3935 71.8043 77.5864 72.0023C77.7793 72.2003 77.9459 72.3865 78.1125 72.5875C78.1914 72.682 78.2674 72.7796 78.3464 72.8741C78.5188 73.0899 78.3171 72.8505 78.3054 72.818C78.3259 72.883 78.4194 72.9746 78.4604 73.0337C78.7702 73.4741 79.0566 73.9381 79.3021 74.4168C79.3401 74.4937 79.3781 74.5705 79.4132 74.6503C79.4804 74.801 79.4863 75.005 79.3986 74.5971C79.4336 74.7597 79.5155 74.9222 79.5652 75.0848C79.6149 75.2473 79.6499 75.3951 79.6821 75.5547C79.6967 75.6285 79.7113 75.7024 79.7259 75.7793C79.7376 75.8443 79.7873 76.2019 79.7493 75.9182C79.7113 75.6345 79.7493 75.933 79.7551 75.995C79.7639 76.0837 79.7698 76.1723 79.7727 76.261C79.7815 76.4413 79.7844 76.6215 79.7815 76.7989C79.7785 76.9762 79.7698 77.1417 79.7581 77.3131C79.7522 77.4047 79.7435 77.4993 79.7347 77.5909C79.7318 77.6264 79.7259 77.6619 79.723 77.6944C79.6996 77.9663 79.7785 77.3368 79.7347 77.6087C79.609 78.3682 79.4424 79.1129 79.2057 79.8459C79.1443 80.0321 79.0829 80.2153 79.0157 80.3985C78.9835 80.4842 78.9514 80.57 78.9192 80.6586C78.8929 80.7266 78.7468 81.096 78.8432 80.8537C78.9397 80.6113 78.7935 80.966 78.776 81.0133C78.738 81.099 78.7029 81.1876 78.665 81.2733C78.5773 81.4743 78.4867 81.6723 78.3931 81.8674C78.0161 82.6653 77.5923 83.4396 77.1422 84.1992C76.692 84.9587 76.1922 85.7241 75.6778 86.46C75.4265 86.8206 75.1693 87.1752 74.9062 87.5269C74.7104 87.79 75.0699 87.3141 74.9121 87.521C74.8448 87.6097 74.7776 87.6983 74.7075 87.787C74.573 87.9614 74.4356 88.1357 74.2983 88.3101C73.3162 89.5455 72.2728 90.7365 71.1621 91.8595C70.7178 92.3087 70.2648 92.7461 69.7942 93.1658C69.5984 93.3402 69.3996 93.5116 69.195 93.68C68.9524 93.881 68.7069 94.0761 68.4585 94.2711C68.4117 94.3066 68.365 94.342 68.3211 94.3775C68.1516 94.5135 68.5228 94.2238 68.3533 94.3539C68.2159 94.4573 68.0785 94.5637 67.9412 94.6671C66.1699 95.9941 64.2993 97.191 62.3294 98.1929C61.8822 98.4205 61.4291 98.6392 60.9702 98.846C60.754 98.9436 60.5377 99.0381 60.3185 99.1327C60.26 99.1564 60.2015 99.18 60.146 99.2066C59.9999 99.2687 60.2746 99.1505 60.2746 99.1534C60.2746 99.1889 59.9326 99.2894 59.8859 99.3071C59.0061 99.6499 58.1088 99.9514 57.1998 100.206C56.3493 100.442 55.487 100.637 54.619 100.785C54.4144 100.82 54.2098 100.853 54.0052 100.882C53.9584 100.888 53.5638 100.941 53.821 100.909C54.0783 100.876 53.6661 100.927 53.596 100.936C53.181 100.983 52.7659 101.018 52.348 101.045C51.5354 101.095 50.7199 101.104 49.9045 101.071C49.507 101.057 49.1066 101.03 48.709 100.995C48.5045 100.977 48.3028 100.956 48.1011 100.933C48.0427 100.927 47.7211 100.882 47.9813 100.918C48.2414 100.953 47.8497 100.9 47.7854 100.891C46.2451 100.67 44.7253 100.3 43.2492 99.8006C42.8897 99.6795 42.5302 99.5494 42.1766 99.4135C42.0041 99.3485 41.8346 99.2775 41.6651 99.2096C41.4956 99.1416 41.969 99.3366 41.8054 99.2687C41.7732 99.2539 41.7381 99.2391 41.7031 99.2273C41.6008 99.1859 41.5014 99.1416 41.3991 99.0973C40.6976 98.7929 40.0078 98.4619 39.3327 98.1043C38.0203 97.4097 36.7548 96.6236 35.5418 95.7636C35.232 95.5449 34.928 95.3203 34.6269 95.0927C34.5626 95.0425 34.4954 94.9922 34.4311 94.942C34.2879 94.8356 34.7088 95.1607 34.5685 95.0484C34.5305 95.0188 34.4954 94.9893 34.4574 94.9627C34.3084 94.8474 34.1622 94.7322 34.0161 94.6139C33.4491 94.1559 32.8908 93.683 32.3472 93.1954C31.254 92.2142 30.2164 91.1739 29.2197 90.0922C28.4715 89.2795 27.7466 88.446 27.0481 87.589C26.709 87.1723 26.3729 86.7526 26.0456 86.327C25.9608 86.2177 25.879 86.1083 25.7942 85.999C25.6305 85.7862 25.955 86.2147 25.7474 85.9369C25.5721 85.7035 25.3996 85.47 25.2301 85.2335C24.0201 83.5578 22.9035 81.8112 21.9156 79.9937C21.4334 79.107 20.9803 78.2056 20.5653 77.2836C20.3636 76.8403 20.1736 76.394 19.9895 75.9477C19.8931 75.7113 20.0246 76.0127 20.0246 76.0364C20.0246 75.995 19.969 75.8945 19.9574 75.865C19.9164 75.7645 19.8784 75.664 19.8404 75.5635C19.7469 75.3212 19.6563 75.0759 19.5686 74.8306C18.9548 73.1106 18.4755 71.3373 18.192 69.5286C18.1569 69.304 18.1394 69.0765 18.0955 68.8548C18.0955 68.843 18.1335 69.1681 18.116 69.0114C18.1101 68.9553 18.1014 68.8991 18.0955 68.843C18.0809 68.7189 18.0663 68.5918 18.0546 68.4647C18.0137 68.0362 17.9815 67.6047 17.964 67.1761C17.9289 66.3191 17.9435 65.4591 18.0137 64.605C18.0283 64.4188 18.0488 64.2296 18.0692 64.0434C18.1101 63.6711 18.0371 64.2503 18.0692 64.0139C18.0868 63.8957 18.1014 63.7774 18.1218 63.6563C18.1832 63.2484 18.2621 62.8435 18.3527 62.4416C18.5339 61.6407 18.7707 60.8516 19.0659 60.0862C19.2003 59.7374 18.9899 60.2605 19.0717 60.0655C19.1068 59.9798 19.1419 59.8941 19.1799 59.8084C19.2705 59.6015 19.364 59.3946 19.4605 59.1907C19.6417 58.8154 19.8346 58.4459 20.045 58.0854C20.2555 57.7248 20.4805 57.3613 20.7202 57.0126C20.8371 56.8412 20.957 56.6727 21.0797 56.5072C21.2902 56.2205 20.9131 56.72 21.0739 56.5161C21.1528 56.4156 21.2288 56.3181 21.3077 56.2205C21.8806 55.5112 22.506 54.8463 23.1754 54.2316C23.5203 53.9124 23.8768 53.608 24.2422 53.3124C24.3211 53.2474 24.4263 53.1854 24.4965 53.1115C24.2276 53.404 24.4497 53.1469 24.5637 53.0642C24.7683 52.9135 24.9758 52.7627 25.1833 52.6179C26.0514 52.015 26.9575 51.4742 27.8928 50.9836C28.1237 50.8624 28.3575 50.7412 28.5943 50.6289C28.6966 50.5787 28.8018 50.5314 28.907 50.4841C28.9567 50.4634 29.1554 50.3748 28.9596 50.4605C28.7492 50.5521 29.0151 50.4398 29.059 50.4221C29.5266 50.2447 30.0089 50.1029 30.4999 49.9935C30.7367 49.9403 30.9734 49.9019 31.2102 49.8605C31.4469 49.8192 31.1634 49.8605 31.1342 49.8694C31.1897 49.8546 31.2686 49.8546 31.3271 49.8458C31.4557 49.831 31.5843 49.8192 31.7129 49.8103C32.2273 49.7719 32.7447 49.7719 33.2591 49.8014C33.5017 49.8162 33.7413 49.8428 33.9839 49.8665C34.1447 49.8812 33.8582 49.8517 33.8612 49.8517C33.9109 49.8458 33.9985 49.8724 34.0541 49.8812C34.1973 49.9049 34.3434 49.9285 34.4866 49.9551C34.9835 50.0497 35.4746 50.1738 35.9568 50.3245C36.1906 50.3984 36.4245 50.4782 36.6525 50.5639C36.6963 50.5817 37.1026 50.7324 36.8921 50.6555C36.7051 50.5846 36.8658 50.6437 36.9009 50.6614C36.9915 50.6999 37.0821 50.7383 37.1727 50.7797C37.6374 50.9895 38.0905 51.223 38.5289 51.483C38.9673 51.7431 39.3882 52.0298 39.8003 52.3283C39.9523 52.4376 39.8529 52.2928 39.7302 52.2721C39.7623 52.278 39.8705 52.3815 39.8851 52.3933C39.9757 52.4642 40.0634 52.5381 40.1511 52.612C40.3498 52.7805 40.5456 52.9578 40.7356 53.1381C41.101 53.4868 41.4488 53.8592 41.7703 54.2493C41.8521 54.3498 41.934 54.4532 42.0158 54.5537C42.159 54.734 41.972 54.4739 41.9603 54.4769C41.9661 54.4769 42.083 54.6453 42.0743 54.6364C42.2379 54.867 42.3929 55.1034 42.5419 55.3428C43.258 56.5102 43.7987 57.7721 44.4534 58.9749C45.1666 60.2842 46.0376 61.5077 47.2213 62.4209C48.3086 63.2603 49.5771 63.7863 50.6995 64.5695C50.7346 64.5961 50.9538 64.7498 50.8018 64.6375C50.6498 64.5252 50.8602 64.6877 50.8953 64.7143C51.0064 64.8089 51.1174 64.9064 51.2197 65.0098C51.3396 65.131 51.4506 65.2581 51.5617 65.3852C51.6728 65.5123 51.5413 65.4738 51.512 65.3202C51.5208 65.3704 51.5968 65.4443 51.6231 65.4886C51.6962 65.6009 51.7605 65.7191 51.8218 65.8403C51.8511 65.8994 51.8803 65.9615 51.9095 66.0206C51.9826 66.1713 51.8774 66.1122 51.8832 65.9526C51.8803 66.0413 51.9709 66.2038 51.9943 66.2866C52.0381 66.4314 52.0732 66.5821 52.1024 66.7299C52.117 66.8038 52.1463 67.1407 52.1404 66.9515C52.1346 66.7417 52.1521 67.1791 52.1521 67.2293C52.1609 67.5101 52.1434 67.785 52.1229 68.0628C52.1083 68.2637 52.1697 67.8116 52.1287 68.0066C52.1171 68.0568 52.1112 68.11 52.1024 68.1632C52.0761 68.3021 52.0469 68.441 52.0148 68.577C51.9855 68.6982 51.9504 68.8164 51.9154 68.9346C51.8949 69.0026 51.8715 69.0705 51.8481 69.1356C51.7897 69.307 51.8043 69.3011 51.8569 69.1356C51.7809 69.3809 51.6348 69.6262 51.5091 69.8508C51.4477 69.9572 51.3834 70.0636 51.3162 70.167C51.2782 70.2261 51.2373 70.2822 51.1993 70.3414C51.0093 70.631 51.3425 70.1759 51.211 70.3266C51.0298 70.5364 50.8515 70.7433 50.6498 70.9354C50.5387 71.0418 50.4247 71.1393 50.3078 71.2398C50.2055 71.3314 50.2318 71.3905 50.3458 71.2162C50.3137 71.2664 50.2114 71.3137 50.1617 71.3491C49.9132 71.5235 49.656 71.6802 49.3901 71.822C49.2732 71.8841 49.1562 71.9432 49.0364 71.9993C48.9692 72.0318 48.899 72.0614 48.8289 72.0909C48.6389 72.1796 49.1504 71.9786 48.8055 72.0969C48.2443 72.2949 47.689 72.422 47.1073 72.5372C46.9028 72.5786 47.3412 72.5165 47.1337 72.5372C47.0781 72.5431 47.0197 72.549 46.9641 72.5549C46.8151 72.5697 46.6631 72.5815 46.5111 72.5875C46.21 72.6022 45.909 72.5993 45.6079 72.5875C45.4589 72.5786 45.3098 72.5668 45.1608 72.552C45.0965 72.5461 44.8919 72.5136 45.19 72.5579C45.1286 72.549 45.0672 72.5402 45.0058 72.5313C44.661 72.4781 44.3161 72.4042 43.977 72.3156C43.6994 72.2446 43.4217 72.1589 43.1499 72.0673C42.995 72.0141 42.84 71.958 42.6851 71.8988C42.6091 71.8693 42.5302 71.8397 42.4542 71.8072C42.6442 71.8841 42.5156 71.8309 42.4016 71.7806C41.8141 71.5206 41.2471 71.2221 40.6947 70.897C40.1423 70.5719 39.6045 70.2143 39.0784 69.8389C38.9439 69.7414 38.8095 69.6439 38.678 69.5464C38.6137 69.4991 38.2834 69.2449 38.4967 69.4134C38.2074 69.1858 37.9239 68.9553 37.6462 68.7159C37.0909 68.2371 36.5589 67.7288 36.065 67.185C35.8341 66.9308 35.6178 66.6619 35.4015 66.3959C35.2933 66.2629 35.4103 66.4107 35.4278 66.4314C35.3752 66.3575 35.3197 66.2836 35.267 66.2068C35.1472 66.0383 35.0332 65.864 34.9221 65.6896C34.7585 65.4266 34.6036 65.1576 34.4603 64.8828C34.3931 64.7498 34.3259 64.6168 34.2645 64.4808C34.2411 64.4276 34.0833 64.0671 34.1564 64.2444C34.2294 64.4217 34.0862 64.0553 34.0658 63.9991C34.0161 63.8572 33.9693 63.7154 33.9255 63.5706C33.8436 63.3016 33.7735 63.0268 33.7179 62.7519C33.6916 62.6219 33.6683 62.4948 33.6449 62.3648C33.6332 62.2909 33.6215 62.217 33.6127 62.1431C33.6566 62.4475 33.6244 62.2407 33.6215 62.1756C33.604 61.8949 33.5806 61.6141 33.5835 61.3333C33.5864 61.0526 33.604 60.7718 33.6332 60.4911C33.5981 60.8073 33.6536 60.3965 33.6712 60.299C33.7004 60.1512 33.7355 60.0034 33.7706 59.8557C33.9547 59.1198 33.5075 58.2509 32.7476 58.0824C31.9876 57.914 31.1926 58.3307 30.9939 59.1168L31.0027 59.1286Z",fill:"#231F20"}),o.createElement("path",{d:"M54.0603 64.8212C54.1363 64.7118 54.2152 64.6054 54.2942 64.4961C54.5484 64.1355 54.11 64.7236 54.3818 64.3779C54.5192 64.2035 54.6595 64.0291 54.8027 63.8577C55.1476 63.4499 55.5101 63.0568 55.89 62.6844C56.2086 62.3741 56.5389 62.0756 56.8809 61.7948C56.9101 61.7712 57.255 61.4993 56.9919 61.7003C57.0796 61.6323 57.1702 61.5673 57.2579 61.5023C57.4917 61.3338 57.7285 61.1713 57.9711 61.0176C58.3452 60.7812 58.731 60.5654 59.1285 60.3674C59.3536 60.258 59.5815 60.1398 59.8154 60.0541C59.5202 60.1635 59.7189 60.0925 59.7803 60.0719C59.9206 60.0216 60.058 59.9684 60.1982 59.9182C60.587 59.7852 60.9786 59.6729 61.3791 59.5842C61.5603 59.5428 61.7444 59.5074 61.9256 59.4749C62.0162 59.4601 62.1098 59.4483 62.2004 59.4305C62.3027 59.4158 62.2734 59.4187 62.1127 59.4394C62.1858 59.4305 62.2618 59.4217 62.3348 59.4128C62.7411 59.3714 63.1503 59.3507 63.5595 59.3567C63.7436 59.3567 63.9307 59.3655 64.1148 59.3773C64.2259 59.3833 64.3369 59.3921 64.448 59.4039C64.5035 59.4098 64.8689 59.469 64.524 59.4099C64.9274 59.4808 65.3249 59.5517 65.7194 59.6611C65.8977 59.7113 66.076 59.7674 66.2514 59.8266C66.3595 59.862 66.4647 59.9034 66.57 59.9448C66.2455 59.8266 66.643 59.9773 66.7103 60.0098C67.0815 60.1812 67.4439 60.3792 67.7888 60.6009C67.9525 60.7073 68.1132 60.8196 68.271 60.9348C68.5896 61.1624 68.0401 60.728 68.3354 60.988C68.4523 61.0915 68.575 61.189 68.689 61.2954C68.9754 61.5584 69.2473 61.8421 69.4957 62.1436C69.5337 62.1909 69.7909 62.4953 69.5512 62.2027C69.6243 62.2913 69.6886 62.3918 69.7529 62.4894C69.8844 62.6815 70.0072 62.8824 70.1212 63.0834C70.2352 63.2844 70.3316 63.4971 70.4339 63.707C70.6034 64.0528 70.3404 63.4262 70.469 63.7897C70.507 63.8991 70.5479 64.0084 70.583 64.1207C70.659 64.3601 70.7233 64.6054 70.7759 64.8537C70.7964 64.9482 70.811 65.0458 70.8285 65.1403C70.8519 65.2999 70.8519 65.297 70.8314 65.1256C70.8402 65.2054 70.849 65.2852 70.8548 65.3679C70.8987 65.8644 70.8987 66.3639 70.8607 66.8604C70.8607 66.8958 70.811 67.3125 70.8548 66.9963C70.8373 67.1204 70.8197 67.2475 70.7964 67.3717C70.7437 67.6613 70.6765 67.945 70.5976 68.2287C70.393 68.9587 70.8753 69.8364 71.6206 70.0019C72.3659 70.1674 73.1551 69.7478 73.3743 68.9676C73.9822 66.7894 73.8712 64.4576 72.9768 62.383C72.1496 60.4679 70.5947 58.8542 68.8059 57.8346C65.2401 55.7984 60.7623 56.1589 57.2374 58.0917C54.9372 59.3537 53.0666 61.2451 51.5613 63.3848C51.1258 64.0055 51.459 65.031 52.1021 65.3649C52.8532 65.7521 53.5927 65.4832 54.0603 64.8182V64.8212Z",fill:"#231F20"}),o.createElement("path",{d:"M64.1126 57.5835C64.5393 53.7799 62.2157 49.9586 58.9684 48.1558C57.3054 47.2337 55.3968 46.7697 53.5028 46.7608C51.4276 46.752 49.4342 47.3342 47.5812 48.2533C45.3248 49.3734 43.4162 51.1378 42.174 53.3514C41.8028 54.0104 42.022 54.9739 42.7147 55.3315C43.4074 55.6891 44.2755 55.494 44.673 54.7847C44.8074 54.5453 44.9477 54.3089 45.0968 54.0813C45.1465 54.0074 45.1962 53.9306 45.2488 53.8567C45.2692 53.8272 45.5352 53.4725 45.3306 53.7326C45.6258 53.3484 45.9473 52.9878 46.2951 52.648C46.45 52.4943 46.6108 52.3465 46.7774 52.2047C46.8768 52.119 46.9791 52.0362 47.0784 51.9535C47.4 51.6875 46.9265 52.0539 47.134 51.9091C47.5519 51.6224 47.9641 51.3358 48.4083 51.0875C48.8 50.8688 49.2062 50.6767 49.6125 50.4905C50.0188 50.3043 49.3553 50.5851 49.7733 50.4226C49.858 50.3901 49.9457 50.3546 50.0305 50.325C50.2789 50.2305 50.5303 50.1448 50.7846 50.065C51.1499 49.9527 51.524 49.861 51.9011 49.7931C51.9742 49.7783 52.5499 49.6778 52.1875 49.7399C52.4009 49.7044 52.6259 49.6926 52.8451 49.6808C53.231 49.6571 53.6168 49.6571 53.9997 49.6749C54.2013 49.6837 54.403 49.7044 54.6047 49.7221C54.8677 49.7487 54.6134 49.7192 54.5696 49.7162C54.7128 49.7221 54.8648 49.7606 55.0051 49.7872C55.3646 49.8551 55.7241 49.9408 56.0749 50.0472C56.2678 50.1063 56.4607 50.1714 56.6477 50.2423C56.7266 50.2718 56.9722 50.3546 56.6623 50.2423C56.7822 50.2837 56.8991 50.3457 57.016 50.3989C57.3667 50.5644 57.7058 50.7506 58.0331 50.9604C58.188 51.058 58.3342 51.1644 58.4833 51.2678C58.7872 51.4806 58.2377 51.058 58.5212 51.2974C58.6031 51.3683 58.6879 51.4363 58.7697 51.5072C59.0678 51.7702 59.3513 52.0539 59.6115 52.3554C59.7284 52.4913 59.854 52.6273 59.9534 52.778C59.7518 52.4795 59.892 52.7012 59.9447 52.778C60.0177 52.8814 60.0879 52.9878 60.1551 53.0942C60.3889 53.4577 60.5935 53.8419 60.7718 54.235C60.801 54.303 60.9735 54.6133 60.7981 54.2853C60.8478 54.3769 60.8741 54.4892 60.9092 54.5867C60.9793 54.7906 61.0407 54.9945 61.0962 55.2014C61.1518 55.4083 61.1956 55.6181 61.2336 55.828C61.2453 55.893 61.3155 56.2506 61.2482 55.8812C61.2658 55.9846 61.2716 56.0939 61.2804 56.1974C61.3155 56.6584 61.3038 57.1195 61.2512 57.5776C61.1664 58.3341 61.9702 59.0582 62.6863 59.0286C63.5368 58.9932 64.0308 58.3873 64.1214 57.5776L64.1126 57.5835Z",fill:"#231F20"}),o.createElement("path",{d:"M55.5453 48.2413C55.5833 44.1954 52.3068 40.7878 48.6621 39.6382C44.9355 38.4619 40.7266 39.3869 37.6051 41.6803C35.4743 43.2437 33.7966 45.2475 32.3148 47.4345C31.891 48.061 32.2037 49.0777 32.8555 49.4146C33.595 49.7958 34.3578 49.5416 34.8138 48.8678C35.0944 48.4541 35.3808 48.0462 35.6789 47.6472C35.486 47.9073 35.7578 47.5468 35.7754 47.5231C35.8309 47.4522 35.8864 47.3813 35.9449 47.3074C36.129 47.0769 36.3161 46.8493 36.509 46.6247C37.1608 45.8681 37.8652 45.1529 38.6339 44.5145C38.7216 44.4407 38.8093 44.3697 38.8969 44.2988C38.6456 44.4998 38.9992 44.222 39.0255 44.2042C39.2097 44.0683 39.3967 43.9382 39.5867 43.8141C40.0281 43.5215 40.4869 43.2555 40.9634 43.0221C41.1212 42.9452 41.2819 42.8743 41.4427 42.8004C41.773 42.6497 41.1504 42.9068 41.4895 42.7797C41.5567 42.7561 41.621 42.7295 41.6882 42.7029C42.0916 42.5522 42.5037 42.4251 42.9187 42.3216C43.2695 42.2359 43.626 42.168 43.9826 42.1089C44.3392 42.0497 43.6845 42.1325 44.0499 42.1C44.158 42.0911 44.2661 42.0763 44.3772 42.0675C44.5964 42.0497 44.8127 42.0379 45.0319 42.035C45.3973 42.0261 45.7655 42.0379 46.1309 42.0675C46.2361 42.0763 46.356 42.0763 46.4612 42.1C46.1192 42.0172 46.4699 42.1 46.5342 42.1118C46.7535 42.1473 46.9697 42.1887 47.186 42.2389C47.566 42.3276 47.943 42.4369 48.3113 42.5699C48.399 42.6024 48.4925 42.629 48.5744 42.6704C48.2529 42.5108 48.5919 42.6822 48.6474 42.7058C48.8491 42.7975 49.0449 42.895 49.2378 43.0043C49.3986 43.093 49.5564 43.1876 49.7084 43.2881C49.8019 43.3472 49.8926 43.4092 49.9832 43.4742C49.9978 43.4831 50.293 43.6929 50.0971 43.5511C49.9013 43.4092 50.1878 43.625 50.2024 43.6397C50.2871 43.7107 50.3719 43.7816 50.4537 43.8555C50.6174 44.0033 50.7752 44.154 50.9302 44.3136C51.0851 44.4732 51.2283 44.6357 51.3686 44.8042C51.4358 44.8869 51.5147 44.9697 51.5732 45.0583C51.3657 44.7362 51.5439 45.0258 51.5936 45.0997C51.8567 45.481 52.0876 45.8829 52.2776 46.3025C52.298 46.3469 52.3536 46.4385 52.3623 46.4946C52.2893 46.3055 52.2805 46.2878 52.336 46.4355C52.3594 46.5006 52.3828 46.5656 52.4062 46.6306C52.4705 46.8256 52.5289 47.0207 52.5757 47.2217C52.5991 47.3192 52.6195 47.4197 52.6371 47.5202C52.6605 47.6443 52.7628 47.8423 52.6488 47.5497C52.7218 47.7389 52.6838 48.0433 52.6809 48.2442C52.6751 49.0038 53.3473 49.7278 54.116 49.6953C54.8847 49.6628 55.5423 49.057 55.5511 48.2442L55.5453 48.2413Z",fill:"#231F20"}),o.createElement("path",{d:"M45.2517 74.8135C45.1201 77.3817 46.5318 79.9499 48.4872 81.5192C50.5799 83.1979 53.1929 84.0106 55.8527 83.6383C56.598 83.5348 57.0861 82.5536 56.8757 81.865C56.6243 81.0375 55.9199 80.7183 55.122 80.8306C55.499 80.7774 55.0226 80.8336 54.9495 80.8395C54.8414 80.8454 54.7303 80.8513 54.6222 80.8513C54.3825 80.8513 54.1428 80.8425 53.9032 80.8218C53.5349 80.7893 54.0464 80.8602 53.7658 80.807C53.6547 80.7863 53.5407 80.7686 53.4297 80.7479C53.1666 80.6947 52.9065 80.6297 52.6493 80.5499C52.4272 80.4819 52.2079 80.4021 51.9917 80.3194C52.3161 80.4464 51.9186 80.2809 51.8543 80.2514C51.7315 80.1923 51.6088 80.1332 51.486 80.0682C51.2785 79.9588 51.0739 79.8406 50.8781 79.7135C50.7465 79.6278 50.6179 79.5362 50.4893 79.4475C50.2292 79.2702 50.6852 79.6189 50.4221 79.3973C50.0188 79.0545 49.6417 78.688 49.3027 78.2772C49.2121 78.1678 49.0601 78.0319 49.2442 78.2033C49.139 78.1028 49.0572 77.9314 48.9782 77.8073C48.8526 77.6093 48.7356 77.4024 48.6304 77.1926C48.569 77.0714 48.5193 76.9473 48.458 76.8261C48.4171 76.7286 48.4258 76.7552 48.4872 76.9089C48.4609 76.8379 48.4346 76.767 48.4083 76.6961C48.3352 76.4833 48.2709 76.2705 48.2183 76.0518C48.1832 75.9099 48.1598 75.7651 48.1306 75.6203C48.0751 75.3277 48.1423 75.8804 48.1248 75.5582C48.1102 75.31 48.1043 75.0647 48.116 74.8135C48.154 74.0539 47.4291 73.3299 46.6809 73.3624C45.8713 73.3978 45.2867 74.0037 45.2458 74.8135H45.2517Z",fill:"#231F20"}),o.createElement("path",{d:"M51.9237 66.1125C51.9062 66.0652 51.8916 66.0238 51.8887 65.9854V65.9795C51.8887 66.0534 51.9091 66.1007 51.9237 66.1125Z",fill:"#231F20"}),o.createElement("path",{d:"M19.9688 75.8741C19.9688 75.8741 19.9775 75.8918 19.9834 75.9036C19.9746 75.8859 19.9717 75.877 19.9688 75.8711V75.8741Z",fill:"#231F20"}),o.createElement("path",{d:"M64.5596 73.7491C64.5596 73.7491 64.5566 73.7491 64.5537 73.7521C64.5596 73.7491 64.5654 73.7432 64.5712 73.7402C64.5683 73.7402 64.5625 73.7461 64.5596 73.7491Z",fill:"#231F20"}),o.createElement("path",{d:"M59.8413 78.5459C59.6718 78.7794 59.7653 78.6493 59.8413 78.5459V78.5459Z",fill:"#231F20"}),o.createElement("path",{d:"M79.4657 74.7805C79.454 74.7479 79.4452 74.7154 79.4336 74.6859C79.4306 74.677 79.4277 74.6681 79.4219 74.6593C79.4219 74.6563 79.4189 74.6534 79.416 74.6475C79.4715 74.8928 79.4832 74.8691 79.4628 74.7805H79.4657Z",fill:"#231F20"}),o.createElement("path",{d:"M51.6053 65.4747C51.576 65.4363 51.5351 65.389 51.5205 65.3506C51.5205 65.3506 51.5205 65.3477 51.5176 65.3477C51.541 65.4393 51.5965 65.4866 51.6082 65.4777L51.6053 65.4747Z",fill:"#231F20"}),o.createElement("path",{d:"M24.4206 53.2158C24.3592 53.2808 24.3767 53.2602 24.4206 53.2158V53.2158Z",fill:"#231F20"}),o.createElement("path",{d:"M36.8629 50.6501C36.7781 50.6176 36.819 50.6324 36.8629 50.6501V50.6501Z",fill:"#231F20"}),o.createElement("path",{d:"M74.997 100.895C74.047 100.315 67.8858 93.5387 64.9834 89.9863C66.0444 87.8673 71.7643 80.1685 75.9323 80.8926C75.9323 80.8926 85.6769 88.7273 86.5684 90.3646C87.7726 92.5723 80.6964 101.613 74.994 100.895H74.997Z",fill:"#CF8329"}),o.createElement("path",{d:"M75.637 99.7537C75.6107 99.736 75.5844 99.7182 75.5581 99.7005C75.3974 99.5941 75.8884 99.9724 75.716 99.8217C75.6341 99.7508 75.5494 99.6798 75.4704 99.6089C75.2337 99.3961 75.0057 99.1715 74.7807 98.9469C74.1376 98.3056 73.5122 97.6465 72.8925 96.9845C71.2937 95.2733 69.7271 93.5297 68.1897 91.7623C67.4152 90.8728 66.6494 89.9743 65.9012 89.0611L66.1116 90.6363C66.4156 90.0305 66.7985 89.4631 67.1843 88.9104C67.31 88.7331 67.4356 88.5558 67.5642 88.3784C67.6344 88.2809 67.7016 88.1775 67.7805 88.0858C67.7718 88.0977 67.5701 88.3607 67.7045 88.1863C67.7513 88.1272 67.7981 88.0652 67.8448 88.0061C68.1722 87.5834 68.5112 87.1697 68.859 86.7648C69.581 85.9255 70.3468 85.1246 71.1681 84.3857C71.3785 84.1995 71.589 84.0163 71.8082 83.839C71.89 83.771 71.9952 83.7119 72.0683 83.6321C71.8315 83.8892 71.9602 83.7148 72.0537 83.6469C72.118 83.5996 72.1852 83.5493 72.2495 83.502C72.6616 83.2065 73.0884 82.9317 73.5355 82.6952C73.6554 82.6332 73.7752 82.5711 73.898 82.5149C73.9418 82.4943 73.9857 82.4765 74.0295 82.4558C74.3101 82.3258 73.9418 82.4854 74.0149 82.4617C74.2546 82.3849 74.4855 82.2933 74.731 82.2342C74.8479 82.2046 74.9677 82.1869 75.0846 82.1633C75.2805 82.1219 74.959 82.1899 74.9473 82.181C74.9706 82.2017 75.1314 82.1633 75.1694 82.1633C75.2834 82.1573 75.3974 82.1544 75.5114 82.1603C75.5523 82.1603 75.5932 82.1662 75.6341 82.1662C75.9615 82.178 75.3974 82.1278 75.6107 82.1603L75.0233 81.8204C77.9987 84.2143 80.9741 86.6436 83.7654 89.2562C84.3587 89.8118 84.9257 90.3822 85.4577 90.9969C85.5337 91.0856 85.2209 90.6275 85.3729 90.8876C85.3963 90.9289 85.4226 90.9733 85.4489 91.0117C85.6068 91.2363 85.3671 90.6748 85.4051 90.8964C85.4109 90.926 85.4197 90.9614 85.4285 90.991C85.4723 91.124 85.3437 91.1388 85.408 90.793C85.3846 90.9201 85.3992 91.1181 85.3992 91.2511C85.3905 90.7989 85.4255 91.0619 85.3905 91.2186C85.3671 91.3279 85.3379 91.4343 85.3057 91.5407C85.2765 91.6382 85.2443 91.7358 85.2093 91.8333C85.1976 91.8658 85.1829 91.8983 85.1713 91.9338C85.0953 92.1495 85.3115 91.6116 85.2151 91.8244C85.1099 92.0608 85.0047 92.2943 84.8848 92.5219C84.6335 93.0007 84.3441 93.4617 84.0343 93.905C83.9495 94.0262 83.8648 94.1444 83.7771 94.2626C83.7215 94.3365 83.5432 94.5375 83.8092 94.2242C83.7595 94.2833 83.7157 94.3454 83.6689 94.4045C83.4702 94.6586 83.2627 94.9098 83.0493 95.1522C82.6167 95.6516 82.1578 96.1275 81.6756 96.5767C81.4154 96.819 81.1495 97.0554 80.8747 97.283C80.7286 97.4042 80.5678 97.5165 80.4275 97.6436C80.714 97.3835 80.5181 97.5726 80.4421 97.6288C80.3545 97.6938 80.2668 97.7588 80.1791 97.8209C79.5945 98.2376 78.9807 98.6159 78.3377 98.9321C78.177 99.0119 78.0133 99.0828 77.8496 99.1567C77.5953 99.269 78.139 99.0474 77.8788 99.1449C77.7853 99.1804 77.6918 99.2158 77.5982 99.2483C77.2972 99.3547 76.9932 99.4434 76.6834 99.5143C76.5197 99.5498 76.3561 99.5764 76.1924 99.6059C75.9352 99.6532 76.5022 99.5853 76.2421 99.5971C76.1485 99.603 76.055 99.6148 75.9644 99.6178C75.6429 99.6355 75.3185 99.6237 74.9999 99.5852C74.3247 99.5055 73.6758 100.244 73.7051 100.894C73.7372 101.672 74.2779 102.118 74.9999 102.204C76.9085 102.431 78.8668 101.704 80.486 100.738C82.2104 99.7094 83.7625 98.3292 85.0631 96.7924C86.1855 95.4654 87.2406 93.9789 87.7813 92.3002C87.9888 91.6589 88.0824 90.9437 87.9216 90.2787C87.7784 89.6965 87.3838 89.2266 86.9922 88.7922C85.9955 87.6839 84.8497 86.6998 83.7303 85.7186C82.3654 84.5217 80.9741 83.3513 79.577 82.1928C78.7148 81.4776 77.8467 80.7683 76.9757 80.062C76.888 79.991 76.8032 79.9231 76.7126 79.8551C76.2538 79.5005 75.5377 79.4945 74.9736 79.5507C73.974 79.6512 73.0387 79.9822 72.156 80.4639C70.3701 81.4362 68.8269 82.8962 67.4707 84.4034C66.2782 85.7274 65.1529 87.1579 64.2235 88.6858C64.0949 88.8986 63.9692 89.1114 63.8581 89.333C63.5775 89.8916 63.6886 90.4413 64.0686 90.9082C65.4949 92.6519 66.9826 94.3424 68.4849 96.0181C69.8966 97.5933 71.323 99.1626 72.8165 100.661C73.293 101.137 73.784 101.669 74.3569 102.026C74.9297 102.384 75.8153 102.148 76.1252 101.533C76.4554 100.88 76.2567 100.132 75.637 99.7448V99.7537Z",fill:"#231F20"})),o.createElement("defs",null,o.createElement("clipPath",{id:"clip0_8467_30797"},o.createElement("rect",{width:88,height:104,fill:"white"}))));u.SUGGESTIONS$HACKER_NEWS;u.SUGGESTIONS$HELLO_WORLD;u.SUGGESTIONS$TODO_APP;const or="INCREASE_TEST_COVERAGE",lr=`I want to increase the test coverage of the repository in the current directory.

Please investigate the repo to figure out what language is being used, and where tests are located, if there are any.

If there are no tests already in the repo, add a very basic test, using typical testing strategies for the language involved.

If there are existing tests, find a function or method which lacks adequate unit tests, and add unit tests for it. Be sure to respect the projects existing test structures.

Make sure the tests pass before you finish.`,ir="AUTO_MERGE_PRS",cr="Please add a GitHub action to this repository which automatically merges pull requests from Dependabot so long as the tests are passing.",dr="FIX_README",ur=`Please look at the README and make the following improvements, if they make sense:
* correct any typos that you find
* add missing language annotations on codeblocks
* if there are references to other files or other sections of the README, turn them into links
* make sure the readme has an h1 title towards the top
* make sure any existing sections in the readme are appropriately separated with headings

If there are no obvious ways to improve the README, make at least one small change to make the wording clearer or friendlier`,Cr="CLEAN_DEPENDENCIES",fr=`Examine the dependencies of the current codebase. Make sure you can run the code and any tests.

Then run any commands necessary to update all dependencies to the latest versions, and make sure the code continues to run correctly and the tests pass. If changes need to be made to the codebase, go ahead and make those changes. You can look up documentation for new versions using the browser if you need to.

If a particular dependency update is causing trouble (e.g. breaking changes that you can't fix), you can revert it and send a message to the user explaining why.

Additionally, if you're able to prune any dependencies that are obviously unused, please do so. You may use third party tools to check for unused dependencies.`,pr="ADD_DOCS",mr="Investigate the documentation in the root of the current repo. Please add a CODE_OF_CONDUCT.md and CONTRIBUTORS.md with good defaults if they are not present. Use information in the README to inform the CONTRIBUTORS doc. If there is no LICENSE currently in the repo, please add the Apache 2.0 license. Add links to all these documents into the README",hr="ADD_DOCKERFILE",gr=`Investigate the current repo to understand the installation instructions. Then create a Dockerfile that runs the application, using best practices like arguments and multi-stage builds wherever appropriate.

If there is an existing Dockerfile, and there are ways to improve it according to best practices, do so.`,xr={[or]:lr,[ir]:cr,[dr]:ur,[Cr]:fr,[pr]:mr,[hr]:gr},br={repo:xr};function vr({onSuggestionsClick:e}){const{t:s}=P();return t.jsxs("div",{"data-testid":"chat-suggestions",className:"flex flex-col gap-6 h-full px-4 items-center justify-center",children:[t.jsxs("div",{className:"flex flex-col items-center p-4 bg-tertiary rounded-xl w-full",children:[t.jsx(nr,{width:45,height:54}),t.jsx("span",{className:"font-semibold text-[20px] leading-6 -tracking-[0.01em] gap-1",children:s(u.LANDING$TITLE)})]}),t.jsx(ar,{suggestions:Object.entries(br.repo).slice(0,4).map(([r,a])=>({label:r,value:a})),onSuggestionClick:e})]})}function Er({onSuggestionsClick:e}){const{t:s}=P(),{providers:r}=F1(),{data:a}=ke(),[n,l]=N.useState(!1),i=r.length>0,C=a?.git_provider,p=C==="gitlab",c=C==="bitbucket",g=p?"merge request":"pull request",d=p?"MR":"PR",m=()=>p?"GitLab":c?"Bitbucket":"GitHub",v={pushToBranch:`Please push the changes to a remote branch on ${m()}, but do NOT create a ${g}. Check your current branch name first - if it's main, master, deploy, or another common default branch name, create a new branch with a descriptive name related to your changes. Otherwise, use the exact SAME branch name as the one you are currently on.`,createPR:`Please push the changes to ${m()} and open a ${g}. Please create a meaningful branch name that describes the changes. If a ${g} template exists in the repository, please follow it when creating the ${d} description.`,pushToPR:`Please push the latest changes to the existing ${g}.`};return t.jsx("div",{className:"flex flex-col gap-2 mb-2",children:i&&a?.selected_repository&&t.jsx("div",{className:"flex flex-row gap-2 justify-center w-full",children:n?t.jsx(Ge,{suggestion:{label:s(u.ACTION$PUSH_CHANGES_TO_PR),value:v.pushToPR},onClick:T=>{we.capture("push_to_pr_button_clicked"),e(T)}}):t.jsxs(t.Fragment,{children:[t.jsx(Ge,{suggestion:{label:s(u.ACTION$PUSH_TO_BRANCH),value:v.pushToBranch},onClick:T=>{we.capture("push_to_branch_button_clicked"),e(T)}}),t.jsx(Ge,{suggestion:{label:s(u.ACTION$PUSH_CREATE_PR),value:v.createPR},onClick:T=>{we.capture("create_pr_button_clicked"),e(T),l(!0)}})]})})})}const yr=()=>Ie({mutationFn:e=>ce.getTrajectory(e)}),dt=(e,s)=>{const r=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=URL.createObjectURL(r),n=document.createElement("a");n.href=a,n.download=s,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(a)};function Nr(){return typeof window<"u"&&"showSaveFilePicker"in window}async function Sr(e,s){const r=s||{};if(!Nr()){dt(r,`trajectory-${e}.json`);return}try{const a={suggestedName:`trajectory-${e}.json`,types:[{description:"JSON File",accept:{"application/json":[".json"]}}]},l=await(await window.showSaveFilePicker(a)).createWritable();await l.write(JSON.stringify(s,null,2)),await l.close()}catch(a){a instanceof Error&&a.name!=="AbortError"&&dt(r,`trajectory-${e}.json`)}}function _r({message:e}){return t.jsx("div",{className:"w-full rounded-lg p-2 text-black border border-red-800 bg-red-500",children:W.exists(e)?t.jsx(it,{i18nKey:e,components:{a:t.jsx(I5,{className:"underline font-bold cursor-pointer",to:"/settings/billing",children:"link"})}}):e})}const _1=["system","agent_state_changed","change_agent_state"],Tr=["recall"],wr=e=>me(e)?K5(e)&&e.source==="user"?!1:!_1.concat(Tr).includes(e.action):We(e)?W5(e)&&e.source==="user"?!1:!_1.includes(e.observation):!0,Ir=()=>Ie({mutationKey:["upload-files"],mutationFn:e=>X1.uploadFiles(e.conversationId,e.files),onSuccess:async()=>{},meta:{disableToast:!0}});function Ar(e,s){return e?"github":s?"replay":"direct"}function T1(){const{getErrorMessage:e}=Z5(),{send:s,isLoadingMessages:r,parsedEvents:a}=Ke(),{setOptimisticUserMessage:n,getOptimisticUserMessage:l}=k1(),{t:i}=P(),C=N.useRef(null),{scrollDomToBottom:p,onChatBodyScroll:c,hitBottom:g,autoScroll:d,setAutoScroll:m,setHitBottom:v}=bt(C),{data:T}=yt(),{curAgentState:f}=pe(L=>L.agent),[x,w]=N.useState("positive"),[D,I]=N.useState(!1),[S,h]=N.useState(null),{selectedRepository:j,replayJson:k}=pe(L=>L.initialQuery),R=A5(),{mutate:_}=yr(),{mutateAsync:A}=Ir(),z=l(),ge=e(),Z=a.filter(wr),de=N.useMemo(()=>a.some(L=>me(L)&&L.source==="agent"&&L.action!=="system"),[a]),se=async(L,ae,ve)=>{const q=[...ae],J=[...ve];Z.length===0?we.capture("initial_query_submitted",{entry_point:Ar(j!==null,k!==null),query_character_length:L.length,replay_json_size:k?.length}):we.capture("user_message_sent",{session_message_count:Z.length,current_message_length:L.length});const Oe=[...q,...J],E=K1(Oe);if(!E.isValid){fe(`Error: ${E.errorMessage}`);return}const Ee=q.map(ye=>V4(ye)),Me=await Promise.all(Ee),ne=new Date().toISOString(),{skipped_files:G,uploaded_files:X}=J.length>0?await A({conversationId:R.conversationId,files:J}):{skipped_files:[],uploaded_files:[]};G.forEach(ye=>fe(ye.reason));const F=`${i("CHAT_INTERFACE$AUGMENTED_PROMPT_FILES_TITLE")}: ${X.join(`

`)}`,Fe=X.length>0?`${L}

${F}`:L;s(Y4(Fe,Me,X,ne)),n(L),h(null)},Q=()=>{we.capture("stop_button_clicked"),s(ut(y.STOPPED))},re=async L=>{I(!0),w(L)},Re=()=>{if(!R.conversationId){fe(i(u.CONVERSATION$DOWNLOAD_ERROR));return}_(R.conversationId,{onSuccess:async L=>{await Sr(R.conversationId??i(u.CONVERSATION$UNKNOWN),L.trajectory)},onError:()=>{fe(i(u.CONVERSATION$DOWNLOAD_ERROR))}})},xe=f===y.AWAITING_USER_INPUT||f===y.FINISHED,be={scrollRef:C,autoScroll:d,setAutoScroll:m,scrollDomToBottom:p,hitBottom:g,setHitBottom:v,onChatBodyScroll:c};return t.jsx(V2,{value:be,children:t.jsxs("div",{className:"h-full flex flex-col justify-between",children:[!de&&!z&&!Z.some(L=>me(L)&&L.source==="user")&&t.jsx(vr,{onSuggestionsClick:h}),t.jsxs("div",{ref:C,onScroll:L=>c(L.currentTarget),className:"scrollbar scrollbar-thin scrollbar-thumb-gray-400 scrollbar-thumb-rounded-full scrollbar-track-gray-800 hover:scrollbar-thumb-gray-300 flex flex-col grow overflow-y-auto overflow-x-hidden px-4 pt-4 gap-2 fast-smooth-scroll",children:[r&&t.jsx("div",{className:"flex justify-center",children:t.jsx(Nt,{size:"small"})}),!r&&t.jsx(Q1,{messages:Z,isAwaitingUserConfirmation:f===y.AWAITING_USER_CONFIRMATION}),xe&&de&&!z&&t.jsx(Er,{onSuggestionsClick:L=>se(L,[],[])})]}),t.jsxs("div",{className:"flex flex-col gap-[6px] px-4 pb-4",children:[t.jsxs("div",{className:"flex justify-between relative",children:[t.jsx(Z4,{onPositiveFeedback:()=>re("positive"),onNegativeFeedback:()=>re("negative"),onExportTrajectory:()=>Re(),isSaasMode:T?.APP_MODE==="saas"}),t.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2 bottom-0",children:f===y.RUNNING&&t.jsx(p2,{})}),!g&&t.jsx(os,{onClick:p})]}),ge&&t.jsx(_r,{message:ge}),t.jsx(i2,{onSubmit:se,onStop:Q,isDisabled:f===y.LOADING||f===y.AWAITING_USER_CONFIRMATION,mode:f===y.RUNNING?"stop":"submit",value:S??void 0,onChange:h})]}),T?.APP_MODE!=="saas"&&t.jsx(f2,{isOpen:D,onClose:()=>I(!1),polarity:x})]})})}const jr=()=>{const{conversationId:e}=te(),s=Zs(),r=he({queryKey:["conversation_config",e],queryFn:()=>{if(!e)throw new Error("No conversation ID");return ce.getRuntimeId(e)},enabled:s&&!!e,staleTime:1e3*60*5,gcTime:1e3*60*15});return N.useEffect(()=>{if(r.data){const{runtime_id:a}=r.data;console.log("Runtime ID: %c%s","background: #444; color: #ffeb3b; font-weight: bold; padding: 2px 4px; border-radius: 4px;",a)}},[r.data]),r};function Rr(e){return ie({attr:{viewBox:"0 0 16 16",fill:"currentColor"},child:[{tag:"path",attr:{fillRule:"evenodd",clipRule:"evenodd",d:"M7.976 10.072l4.357-4.357.62.618L8.284 11h-.618L3 6.333l.619-.618 4.357 4.357z"},child:[]}]})(e)}function Or(e){return ie({attr:{viewBox:"0 0 16 16",fill:"currentColor"},child:[{tag:"path",attr:{fillRule:"evenodd",clipRule:"evenodd",d:"M5.928 7.976l4.357 4.357-.618.62L5 8.284v-.618L9.667 3l.618.619-4.357 4.357z"},child:[]}]})(e)}function Mr(e){return ie({attr:{viewBox:"0 0 16 16",fill:"currentColor"},child:[{tag:"path",attr:{fillRule:"evenodd",clipRule:"evenodd",d:"M10.072 8.024L5.715 3.667l.618-.62L11 7.716v.618L6.333 13l-.618-.619 4.357-4.357z"},child:[]}]})(e)}function Lr(e){return ie({attr:{viewBox:"0 0 16 16",fill:"currentColor"},child:[{tag:"path",attr:{fillRule:"evenodd",clipRule:"evenodd",d:"M8.024 5.928l-4.357 4.357-.62-.618L7.716 5h.618L13 9.667l-.619.618-4.357-4.357z"},child:[]}]})(e)}function Dr(e){return ie({attr:{viewBox:"0 0 16 16",fill:"currentColor"},child:[{tag:"path",attr:{d:"M4.708 5.578L2.061 8.224l2.647 2.646-.708.708-3-3V7.87l3-3 .708.708zm7-.708L11 5.578l2.647 2.646L11 10.87l.708.708 3-3V7.87l-3-3zM4.908 13l.894.448 5-10L9.908 3l-5 10z"},child:[]}]})(e)}function w1({icon:e,onClick:s,ariaLabel:r,testId:a=""}){return t.jsx(He,{type:"button",variant:"flat",onPress:s,className:"cursor-pointer text-[12px] bg-transparent aspect-square px-0 min-w-[20px] h-[20px]","aria-label":r,"data-testid":a,children:e})}var e5=(e=>(e.HORIZONTAL="horizontal",e.VERTICAL="vertical",e))(e5||{});function $r({firstChild:e,firstClassName:s,secondChild:r,secondClassName:a,className:n,orientation:l,initialSize:i}){const[C,p]=o.useState(i),[c,g]=o.useState(null),d=o.useRef(null),m=o.useRef(null),[v,T]=o.useState("split"),f=l==="horizontal";o.useEffect(()=>{if(c==null||!d.current)return;const h=R=>{const _=f?R.clientX:R.clientY;return C+_-c},j=R=>{R.preventDefault();const _=`${h(R)}px`,{current:A}=d;A&&(f?(A.style.width=_,A.style.minWidth=_):(A.style.height=_,A.style.minHeight=_))},k=R=>{R.preventDefault(),d.current&&(d.current.style.transition=""),m.current&&(m.current.style.transition=""),p(h(R)),g(null),document.removeEventListener("mousemove",j),document.removeEventListener("mouseup",k)};return document.addEventListener("mousemove",j),document.addEventListener("mouseup",k),()=>{document.removeEventListener("mousemove",j),document.removeEventListener("mouseup",k)}},[c,C,l]);const x=h=>{h.preventDefault(),d.current&&(d.current.style.transition="none"),m.current&&(m.current.style.transition="none");const j=f?h.clientX:h.clientY;g(j)},w=()=>{const h={overflow:"hidden"};if(v==="collapsed")h.opacity=0,h.width=0,h.minWidth=0,h.height=0,h.minHeight=0;else if(v==="split"){const j=`${C}px`;f?(h.width=j,h.minWidth=j):(h.height=j,h.minHeight=j)}else h.flexGrow=1;return h},D=()=>{const h={overflow:"hidden"};return v==="filled"?(h.opacity=0,h.width=0,h.minWidth=0,h.height=0,h.minHeight=0):h.flexGrow=1,h},I=()=>{T(v==="split"?"collapsed":"split")},S=()=>{T(v==="split"?"filled":"split")};return t.jsxs("div",{className:et("flex",!f&&"flex-col",n),children:[t.jsx("div",{ref:d,className:et(s,"transition-all ease-soft-spring"),style:w(),children:e}),t.jsxs("div",{className:`${f?"cursor-ew-resize w-3 flex-col":"cursor-ns-resize h-3 flex-row"} shrink-0 flex justify-center items-center`,onMouseDown:v==="split"?x:void 0,children:[t.jsx(w1,{icon:f?t.jsx(Or,{}):t.jsx(Lr,{}),ariaLabel:"Collapse",onClick:I}),t.jsx(w1,{icon:f?t.jsx(Mr,{}):t.jsx(Rr,{}),ariaLabel:"Expand",onClick:S})]}),t.jsx("div",{ref:m,className:et(a,"transition-all ease-soft-spring"),style:D(),children:r})]})}const Be=new Map,ot={error:(e,s)=>{if(Be.has(e))return;const r=Te(s,{duration:4e3,style:{background:"#ef4444",color:"#fff"},iconTheme:{primary:"#ef4444",secondary:"#fff"}});Be.set(e,r)},success:(e,s,r=4e3)=>{if(Be.has(e))return;const a=Te.success(s,{duration:r,style:{background:"#333",color:"#fff"},iconTheme:{primary:"#333",secondary:"#fff"}});Be.set(e,a)},settingsChanged:e=>{Te(e,{position:"bottom-right",className:"bg-tertiary",duration:us(e,5e3),icon:"⚙️",style:{background:"#333",color:"#fff"}})},info:e=>{Te(e,{position:"top-center",className:"bg-tertiary",style:{background:"#333",color:"#fff",lineBreak:"anywhere"}})}};function Pr({className:e}){return t.jsxs("svg",{width:"39",height:"39",viewBox:"0 0 39 39",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[t.jsx("mask",{id:"mask0_6001_732",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"39",height:"39",children:t.jsx("rect",{width:"38.9711",height:"39",rx:"1.90143",fill:"black"})}),t.jsx("g",{mask:"url(#mask0_6001_732)",children:t.jsx("rect",{width:"38.9711",height:"39",rx:"4.96091",fill:"url(#paint0_linear_6001_732)"})}),t.jsx("g",{clipPath:"url(#clip0_6001_732)",children:t.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M30.6946 22.9468L24.6617 19.3906C23.0017 18.412 21.9826 16.6281 21.9826 14.7005V7.64124C21.9826 6.24917 20.8546 5.12061 19.4631 5.12061H19.2448C17.8533 5.12061 16.7253 6.24917 16.7253 7.64124V14.6683C16.7253 16.5959 15.7062 18.3799 14.0461 19.3584L7.95872 22.9468C6.70795 23.6841 6.29135 25.2963 7.02841 26.5476C7.76534 27.7989 9.37687 28.2157 10.6276 27.4783L16.5643 23.9788C18.269 22.9739 20.3843 22.9739 22.089 23.9788L28.0256 27.4783C29.2764 28.2155 30.8878 27.7989 31.6249 26.5476C32.3618 25.2963 31.9453 23.6842 30.6946 22.9468ZM10.6709 11.2274L13.5534 12.9268C14.8042 13.6641 15.2206 15.2762 14.4836 16.5275L14.4835 16.5276C13.7464 17.7789 12.135 18.1955 10.8843 17.4581L8.0018 15.7588C6.75106 15.0215 6.33462 13.4094 7.07166 12.1581L7.07173 12.158C7.80876 10.9067 9.42018 10.4901 10.6709 11.2274ZM30.6885 15.7597L27.806 17.459C26.5552 18.1963 24.9438 17.7797 24.2068 16.5284L24.2067 16.5283C23.4697 15.277 23.8861 13.6649 25.1368 12.9276L28.0193 11.2283C29.2701 10.4909 30.8815 10.9075 31.6185 12.1588L31.6186 12.1589C32.3556 13.4102 31.9392 15.0223 30.6885 15.7597ZM21.9766 27.6046V30.9518C21.9766 32.4042 20.7997 33.5815 19.3479 33.5815H19.3478C17.8961 33.5815 16.7192 32.4042 16.7192 30.9518V27.6046C16.7192 26.1522 17.8961 24.9749 19.3478 24.9749H19.3479C20.7997 24.9749 21.9766 26.1522 21.9766 27.6046Z",fill:"url(#paint1_linear_6001_732)"})}),t.jsxs("defs",{children:[t.jsxs("linearGradient",{id:"paint0_linear_6001_732",x1:"0",y1:"0",x2:"39.1786",y2:"39.1496",gradientUnits:"userSpaceOnUse",children:[t.jsx("stop",{stopColor:"#6360FD"}),t.jsx("stop",{offset:"1",stopColor:"#4541EC"})]}),t.jsxs("linearGradient",{id:"paint1_linear_6001_732",x1:"32.1372",y1:"33.5815",x2:"7.91553",y2:"6.29303",gradientUnits:"userSpaceOnUse",children:[t.jsx("stop",{stopColor:"#DDDDDD"}),t.jsx("stop",{offset:"1",stopColor:"white"})]}),t.jsx("clipPath",{id:"clip0_6001_732",children:t.jsx("rect",{width:"28.4724",height:"28.4936",fill:"white",transform:"translate(5.08594 5.08813)"})})]})]})}const kr=()=>{const e=new Date,s=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),a=String(e.getDate()).padStart(2,"0"),n=String(e.getHours()).padStart(2,"0"),l=String(e.getMinutes()).padStart(2,"0"),i=String(e.getSeconds()).padStart(2,"0");return`${s}-${r}-${a}-${n}-${l}-${i}`};class Pe{static async getPolicy(){const{data:s}=await ee.get("/api/security/policy");return s.policy}static async getRiskSeverity(){const{data:s}=await ee.get("/api/security/settings");return s.RISK_SEVERITY}static async getTraces(){const{data:s}=await ee.get("/api/security/export-trace");return s}static async updatePolicy(s){await ee.post("/api/security/policy",{policy:s})}static async updateRiskSeverity(s){await ee.post("/api/security/settings",{RISK_SEVERITY:s})}}const Fr=e=>{const s=he({queryKey:["policy"],queryFn:Pe.getPolicy}),{isFetching:r,isSuccess:a,data:n}=s;return N.useEffect(()=>{!r&&a&&n&&e?.onSuccess(n)},[r,a,n,e?.onSuccess]),s},Ur=e=>{const s=he({queryKey:["risk_severity"],queryFn:Pe.getRiskSeverity}),{isFetching:r,isSuccess:a,data:n}=s;return N.useEffect(()=>{!r&&a&&n&&e?.onSuccess(n)},[r,a,n,e?.onSuccess]),s},Br=e=>{const s=he({queryKey:["traces"],queryFn:Pe.getTraces,enabled:!1}),{isFetching:r,isSuccess:a,data:n}=s;return N.useEffect(()=>{!r&&a&&n&&e?.onSuccess(n)},[r,a,n,e?.onSuccess]),s};function Hr(){const{t:e}=P(),{logs:s}=pe(x=>x.securityAnalyzer),[r,a]=N.useState("logs"),[n,l]=N.useState(""),[i,C]=N.useState($.MEDIUM),p=N.useRef(null);Fr({onSuccess:l}),Ur({onSuccess:x=>{C(x===0?$.LOW:x||$.MEDIUM)}});const{refetch:c}=Br({onSuccess:x=>{ot.info(e(u.INVARIANT$TRACE_EXPORTED_MESSAGE));const w=`openhands-trace-${kr()}.json`;dt(x,w)}}),{mutate:g}=Ie({mutationFn:x=>Pe.updatePolicy(x.policy),onSuccess:()=>{ot.info(e(u.INVARIANT$POLICY_UPDATED_MESSAGE))}}),{mutate:d}=Ie({mutationFn:x=>Pe.updateRiskSeverity(x.riskSeverity),onSuccess:()=>{ot.info(e(u.INVARIANT$SETTINGS_UPDATED_MESSAGE))}});bt(p);const m=N.useCallback(x=>{switch(x){case $.LOW:return"text-green-500";case $.MEDIUM:return"text-yellow-500";case $.HIGH:return"text-red-500";case $.UNKNOWN:default:return"text-gray-500"}},[]),v=N.useCallback(x=>{switch(x){case $.LOW:return e(u.SECURITY_ANALYZER$LOW_RISK);case $.MEDIUM:return e(u.SECURITY_ANALYZER$MEDIUM_RISK);case $.HIGH:return e(u.SECURITY_ANALYZER$HIGH_RISK);case $.UNKNOWN:default:return e(u.SECURITY_ANALYZER$UNKNOWN_RISK)}},[e]),T=N.useCallback((x,w)=>{w.editor.defineTheme("my-theme",{base:"vs-dark",inherit:!0,rules:[],colors:{"editor.background":"#171717"}}),w.editor.setTheme("my-theme")},[]),f={logs:t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex justify-between items-center border-b border-neutral-600 mb-4 p-4",children:[t.jsx("h2",{className:"text-2xl",children:e(u.INVARIANT$LOG_LABEL)}),t.jsx(He,{onPress:()=>c(),className:"bg-tertiary",children:e(u.INVARIANT$EXPORT_TRACE_LABEL)})]}),t.jsx("div",{className:"flex-1 p-4 max-h-screen overflow-y-auto fast-smooth-scroll",ref:p,children:s.map((x,w)=>t.jsxs("div",{className:`mb-2 p-2 rounded-lg ${x.confirmed_changed&&x.confirmation_state==="confirmed"?"border-green-800":"border-red-800"}`,style:{backgroundColor:"rgba(128, 128, 128, 0.2)",borderWidth:x.confirmed_changed?"2px":"0"},children:[t.jsxs("p",{className:"text-sm relative break-words",children:[x.content,(x.confirmation_state==="awaiting_confirmation"||x.confirmed_changed)&&t.jsx(F4,{className:"absolute top-0 right-0"})]}),t.jsx("p",{className:`text-xs ${m(x.security_risk)}`,children:v(x.security_risk)})]},w))})]}),policy:t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex justify-between items-center border-b border-neutral-600 mb-4 p-4",children:[t.jsx("h2",{className:"text-2xl",children:e(u.INVARIANT$POLICY_LABEL)}),t.jsx(He,{className:"bg-tertiary",onPress:()=>g({policy:n}),children:e(u.INVARIANT$UPDATE_POLICY_LABEL)})]}),t.jsx("div",{className:"flex grow items-center justify-center",children:t.jsx(Ys,{path:"policy.py",height:"100%",onMount:T,value:n,onChange:x=>l(x||"")})})]}),settings:t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex justify-between items-center border-b border-neutral-600 mb-4 p-4",children:[t.jsx("h2",{className:"text-2xl",children:e(u.INVARIANT$SETTINGS_LABEL)}),t.jsx(He,{className:"bg-tertiary",onPress:()=>d({riskSeverity:i}),children:e(u.INVARIANT$UPDATE_SETTINGS_LABEL)})]}),t.jsx("div",{className:"flex grow p-4",children:t.jsxs("div",{className:"flex flex-col w-full",children:[t.jsx("p",{className:"mb-2",children:e(u.INVARIANT$ASK_CONFIRMATION_RISK_SEVERITY_LABEL)}),t.jsxs(T4,{placeholder:e(u.SECURITY$SELECT_RISK_SEVERITY),value:i,onChange:x=>C(Number(x.target.value)),className:m(i),selectedKeys:new Set([i.toString()]),"aria-label":e(u.SECURITY$SELECT_RISK_SEVERITY),children:[t.jsx(Le,{"aria-label":e(u.SECURITY$UNKNOWN_RISK),className:m($.UNKNOWN),children:v($.UNKNOWN)},$.UNKNOWN),t.jsx(Le,{"aria-label":e(u.SECURITY$LOW_RISK),className:m($.LOW),children:v($.LOW)},$.LOW),t.jsx(Le,{"aria-label":e(u.SECURITY$MEDIUM_RISK),className:m($.MEDIUM),children:v($.MEDIUM)},$.MEDIUM),t.jsx(Le,{"aria-label":e(u.SECURITY$HIGH_RISK),className:m($.HIGH),children:v($.HIGH)},$.HIGH),t.jsx(Le,{"aria-label":e(u.SECURITY$DONT_ASK_CONFIRMATION),children:e(u.INVARIANT$DONT_ASK_FOR_CONFIRMATION_LABEL)},$.HIGH+1)]})]})})]})};return t.jsxs("div",{className:"flex flex-1 w-full h-full",children:[t.jsxs("div",{className:"w-60 bg-base-secondary border-r border-r-neutral-600 p-4 flex-shrink-0",children:[t.jsxs("div",{className:"text-center mb-2",children:[t.jsx(Pr,{className:"mx-auto mb-1"}),t.jsx("b",{children:e(u.INVARIANT$INVARIANT_ANALYZER_LABEL)})]}),t.jsxs("p",{className:"text-[0.6rem]",children:[e(u.INVARIANT$INVARIANT_ANALYZER_MESSAGE)," ",t.jsx("a",{className:"underline",href:"https://github.com/invariantlabs-ai/invariant",target:"_blank",rel:"noreferrer",children:e(u.INVARIANT$CLICK_TO_LEARN_MORE_LABEL)})]}),t.jsx("hr",{className:"border-t border-neutral-600 my-2"}),t.jsxs("ul",{className:"space-y-2",children:[t.jsx("div",{className:`cursor-pointer p-2 rounded-sm ${r==="logs"&&"bg-neutral-600"}`,onClick:()=>a("logs"),children:e(u.INVARIANT$LOG_LABEL)}),t.jsx("div",{className:`cursor-pointer p-2 rounded-sm ${r==="policy"&&"bg-neutral-600"}`,onClick:()=>a("policy"),children:e(u.INVARIANT$POLICY_LABEL)}),t.jsx("div",{className:`cursor-pointer p-2 rounded-sm ${r==="settings"&&"bg-neutral-600"}`,onClick:()=>a("settings"),children:e(u.INVARIANT$SETTINGS_LABEL)})]})]}),t.jsx("div",{className:"flex flex-col min-h-0 w-full overflow-y-auto bg-base",children:f[r]})]})}const I1={invariant:Hr};function Gr({isOpen:e,onOpenChange:s,securityAnalyzer:r}){const{t:a}=P(),n=r&&I1[r]?I1[r]:()=>t.jsx("div",{children:a(u.SECURITY$UNKNOWN_ANALYZER_LABEL)});return t.jsx(ts,{isOpen:e&&!!r,contentClassName:"max-w-[80%] h-[80%]",bodyClassName:"px-0 py-0 max-h-[100%]",onOpenChange:s,title:"",children:t.jsx(n,{})})}function Vr(e="OpenHands"){const{data:s}=ke(),r=o.useRef(null);o.useEffect(()=>(s?.title?(r.current=s.title,document.title=`${s.title} | ${e}`):document.title=e,()=>{document.title=e}),[s,e])}function zr(e){return ie({attr:{version:"1.1",viewBox:"0 0 32 32"},child:[{tag:"path",attr:{d:"M26.852 15.281l-9.848-9.848c-0.567-0.567-1.487-0.567-2.054 0l-2.045 2.045 2.594 2.594c0.603-0.204 1.294-0.067 1.775 0.413 0.483 0.483 0.619 1.181 0.41 1.786l2.5 2.5c0.605-0.209 1.303-0.074 1.786 0.41 0.675 0.675 0.675 1.769 0 2.444s-1.769 0.675-2.445 0c-0.508-0.508-0.633-1.254-0.376-1.88l-2.332-2.332v6.136c0.164 0.082 0.32 0.19 0.457 0.327 0.675 0.675 0.675 1.769 0 2.445-0.675 0.675-1.77 0.675-2.444 0-0.675-0.676-0.675-1.77 0-2.445 0.167-0.167 0.36-0.293 0.566-0.377v-6.193c-0.206-0.084-0.399-0.209-0.566-0.377-0.511-0.511-0.634-1.262-0.372-1.889l-2.557-2.558-6.753 6.752c-0.567 0.568-0.567 1.488 0 2.055l9.849 9.848c0.567 0.567 1.486 0.567 2.054 0l9.802-9.802c0.567-0.567 0.567-1.488 0-2.055z"},child:[]}]})(e)}function Kr(){const{t:e}=P();return t.jsx("span",{className:"text-[11px] leading-5 text-base bg-neutral-400 px-1 rounded-xl",children:e(u.BADGE$BETA)})}function Wr({to:e,label:s,icon:r,isBeta:a,isLoading:n,rightContent:l}){return t.jsx(j5,{end:!0,to:e,className:V("px-2 border-b border-r border-neutral-600 bg-base-secondary flex-1","first-of-type:rounded-tl-xl last-of-type:rounded-tr-xl last-of-type:border-r-0","flex items-center gap-2 h-full min-h-[36px]"),children:({isActive:i})=>t.jsxs("div",{className:"flex items-center justify-between w-full",children:[t.jsxs("div",{className:"flex items-center gap-1 min-w-0",children:[t.jsx("div",{className:V(i&&"text-logo"),children:r}),t.jsx("span",{className:"truncate",children:s}),a&&t.jsx(Kr,{})]}),t.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[l,n&&t.jsx(Nt,{size:"small"})]})]})},e)}function Zr({scrollLeft:e,canScrollLeft:s}){return t.jsx("button",{type:"button",onClick:e,disabled:!s,className:Ve("cursor-pointer absolute left-0 z-10 bg-base-secondary border-r border-neutral-600 h-full px-2 flex items-center justify-center","hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed","rounded-tl-xl"),children:t.jsx(B1,{width:16,height:16,active:s})})}function Yr({scrollRight:e,canScrollRight:s}){return t.jsx("button",{type:"button",onClick:e,disabled:!s,className:Ve("cursor-pointer absolute right-0 z-10 bg-base-secondary border-l border-neutral-600 h-full px-2 flex items-center justify-center","hover:bg-neutral-700 disabled:opacity-50 disabled:cursor-not-allowed","rounded-tr-xl"),children:t.jsx(H1,{width:16,height:16,active:s})})}const qr=({elementRef:e,callback:s})=>{o.useEffect(()=>{const r=new ResizeObserver(a=>{for(const n of a)s(n.contentRect.width)});return e.current&&r.observe(e.current),()=>{r.disconnect()}},[])};function Jr({label:e,labels:s,children:r,className:a}){const[n,l]=o.useState(0),[i,C]=o.useState(!1),[p,c]=o.useState(!1),g=o.useRef(null),d=o.useRef(null);qr({elementRef:g,callback:l});const m=()=>{if(d.current){const{scrollLeft:x,scrollWidth:w,clientWidth:D}=d.current;C(x>0),c(x<w-D)}};o.useEffect(()=>{m()},[s,n]);const v=()=>{d.current&&d.current.scrollBy({left:-200,behavior:"smooth"})},T=()=>{d.current&&d.current.scrollBy({left:200,behavior:"smooth"})},f=n<598&&s&&s.length>0;return t.jsxs("div",{ref:g,className:Ve("bg-base-secondary border border-neutral-600 rounded-xl flex flex-col h-full w-full",a),children:[s&&t.jsxs("div",{className:"relative flex items-center h-[36px] w-full",children:[f&&t.jsx(Zr,{scrollLeft:v,canScrollLeft:i}),t.jsx("div",{ref:d,className:Ve("flex text-xs overflow-x-auto scrollbar-hide w-full",f&&"mx-8"),onScroll:m,children:s.map(({label:x,to:w,icon:D,isBeta:I,isLoading:S,rightContent:h})=>t.jsx(Wr,{to:w,label:x,icon:D,isBeta:I,isLoading:S,rightContent:h},w))}),f&&t.jsx(Yr,{scrollRight:T,canScrollRight:p})]}),!s&&e&&t.jsx("div",{className:"px-2 h-[36px] border-b border-neutral-600 text-xs flex items-center",children:e}),t.jsx("div",{className:"overflow-hidden flex-grow rounded-b-xl",children:r})]})}function Xr(){const{t:e}=P(),{activeHost:s}=qs();return t.jsxs("div",{className:"flex items-center justify-between w-full",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"flex items-center gap-2",children:e(u.APP$TITLE)}),t.jsx("span",{className:"border rounded-md text- px-1 font-bold",children:"BETA"})]}),s&&t.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]})}const Qr=o.lazy(()=>je(()=>import("./changes-tab-BaITyvjM.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16]))),e3=o.lazy(()=>je(()=>import("./browser-tab-DQj3XIBz.js"),__vite__mapDeps([17,1,2,14,15,16,4,9,18]))),t3=o.lazy(()=>je(()=>import("./jupyter-tab-BN0K47b7.js"),__vite__mapDeps([19,1,2,20,21,14,15,16,12]))),s3=o.lazy(()=>je(()=>import("./served-tab-jFMthLLa.js"),__vite__mapDeps([22,1,23,4,24,25,6,7,8,9,11,2,12,13,14,15,16]))),r3=o.lazy(()=>je(()=>import("./terminal-tab-Ea_WHo_u.js"),__vite__mapDeps([26,27,1]))),a3=o.lazy(()=>je(()=>import("./vscode-tab-UrEftLrh.js"),__vite__mapDeps([28,1,2,14,12,6,7,8,9,29,11,13,15,16])));function n3({conversationPath:e}){const r=R5().pathname,a=r===e,n=r===`${e}/browser`,l=r===`${e}/jupyter`,i=r===`${e}/served`,C=r===`${e}/terminal`,p=r===`${e}/vscode`;return t.jsx("div",{className:"h-full w-full relative",children:t.jsxs(o.Suspense,{fallback:t.jsx("div",{className:"flex items-center justify-center h-full",children:t.jsx(Nt,{size:"large"})}),children:[t.jsx("div",{className:`absolute inset-0 ${a?"block":"hidden"}`,children:t.jsx(Qr,{})}),t.jsx("div",{className:`absolute inset-0 ${n?"block":"hidden"}`,children:t.jsx(e3,{})}),t.jsx("div",{className:`absolute inset-0 ${l?"block":"hidden"}`,children:t.jsx(t3,{})}),t.jsx("div",{className:`absolute inset-0 ${i?"block":"hidden"}`,children:t.jsx(s3,{})}),t.jsx("div",{className:`absolute inset-0 ${C?"block":"hidden"}`,children:t.jsx(r3,{})}),t.jsx("div",{className:`absolute inset-0 ${p?"block":"hidden"}`,children:t.jsx(a3,{})})]})})}const o3=e=>o.createElement("svg",{width:25,height:24,viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.5835 12C3.5835 7.16751 7.501 3.25 12.3335 3.25C17.166 3.25 21.0835 7.16751 21.0835 12C21.0835 16.8325 17.166 20.75 12.3335 20.75C7.501 20.75 3.5835 16.8325 3.5835 12ZM5.85099 8.75C6.75105 6.95823 8.37212 5.5911 10.3334 5.02939C9.86889 5.56854 9.46926 6.20527 9.14301 6.90523C8.87786 7.47408 8.657 8.09299 8.48676 8.75H5.85099ZM5.29611 10.25C5.15721 10.8104 5.0835 11.3966 5.0835 12C5.0835 12.6034 5.15721 13.1896 5.29611 13.75H8.19667C8.12231 13.1834 8.0835 12.598 8.0835 12C8.0835 11.4019 8.12231 10.8165 8.19666 10.25H5.29611ZM9.71122 10.25C9.62798 10.8092 9.5835 11.3951 9.5835 12C9.5835 12.6049 9.62799 13.1908 9.71123 13.75H14.9558C15.039 13.1908 15.0835 12.6049 15.0835 12C15.0835 11.3952 15.039 10.8092 14.9558 10.25H9.71122ZM16.4703 10.25C16.5447 10.8165 16.5835 11.4019 16.5835 12C16.5835 12.5981 16.5447 13.1835 16.4703 13.75H19.3709C19.5098 13.1896 19.5835 12.6034 19.5835 12C19.5835 11.3966 19.5098 10.8104 19.3709 10.25H16.4703ZM18.816 8.75H16.1802C16.01 8.09302 15.7891 7.47413 15.524 6.90529C15.1977 6.2053 14.7981 5.56854 14.3335 5.02937C16.2948 5.59107 17.9159 6.95821 18.816 8.75ZM14.6229 8.75H10.0441C10.1736 8.31962 10.3276 7.91428 10.5026 7.53893C10.998 6.47611 11.6415 5.69218 12.3335 5.23257C13.0254 5.69217 13.669 6.47613 14.1644 7.53899C14.3394 7.91433 14.4934 8.31964 14.6229 8.75ZM5.85099 15.25H8.48677C8.66634 15.943 8.90223 16.5936 9.18703 17.1879C9.50463 17.8505 9.8893 18.455 10.3336 18.9706C8.37219 18.409 6.75107 17.0418 5.85099 15.25ZM14.6229 15.25H10.0441C10.1827 15.7106 10.3494 16.1424 10.5397 16.5396C11.0309 17.5645 11.6596 18.3198 12.3336 18.7674C13.0255 18.3078 13.6691 17.5239 14.1644 16.4611C14.3394 16.0857 14.4934 15.6804 14.6229 15.25ZM15.524 17.0948C15.7891 16.5259 16.01 15.907 16.1802 15.25H18.816C17.9159 17.0418 16.2949 18.4089 14.3336 18.9706C14.7981 18.4315 15.1977 17.7947 15.524 17.0948Z",fill:"currentColor"})),l3=e=>o.createElement("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("g",{transform:"scale(0.85) translate(1.8, 1.8)"},o.createElement("path",{d:"M19.5 1.5C18.67 1.5 18 2.17 18 3C18 3.83 18.67 4.5 19.5 4.5C20.33 4.5 21 3.83 21 3C21 2.17 20.33 1.5 19.5 1.5Z",fill:"currentColor"}),o.createElement("path",{d:"M12 18C8.5 18 5.5 16.8 4 15C4 18.3137 7.13401 21 12 21C16.866 21 20 18.3137 20 15C18.5 16.8 15.5 18 12 18Z",fill:"currentColor"}),o.createElement("path",{d:"M12 6C15.5 6 18.5 7.2 20 9C20 5.68629 16.866 3 12 3C7.13401 3 4 5.68629 4 9C5.5 7.2 8.5 6 12 6Z",fill:"currentColor"}),o.createElement("path",{d:"M7.5 21C6.67 21 6 21.67 6 22.5C6 23.33 6.67 24 7.5 24C8.33 24 9 23.33 9 22.5C9 21.67 8.33 21 7.5 21Z",fill:"currentColor"}),o.createElement("path",{d:"M4.5 5.5C3.67 5.5 3 4.83 3 4C3 3.17 3.67 2.5 4.5 2.5C5.33 2.5 6 3.17 6 4C6 4.83 5.33 5.5 4.5 5.5Z",fill:"currentColor"}))),i3=e=>o.createElement("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},o.createElement("path",{d:"M6 5C5.44772 5 5 5.44772 5 6V18C5 18.5523 5.44772 19 6 19H18C18.5523 19 19 18.5523 19 18V6C19 5.44772 18.5523 5 18 5H6ZM6 6H18V18H6V6Z",fill:"currentColor"}),o.createElement("path",{d:"M8.14645 9.64645C7.95118 9.84171 7.95118 10.1583 8.14645 10.3536L10.7929 13L8.14645 15.6464C7.95118 15.8417 7.95118 16.1583 8.14645 16.3536C8.34171 16.5488 8.65829 16.5488 8.85355 16.3536L11.8536 13.3536C12.0488 13.1583 12.0488 12.8417 11.8536 12.6464L8.85355 9.64645C8.65829 9.45118 8.34171 9.45118 8.14645 9.64645Z",fill:"currentColor"}),o.createElement("path",{d:"M13 16C12.7239 16 12.5 16.2239 12.5 16.5C12.5 16.7761 12.7239 17 13 17H16C16.2761 17 16.5 16.7761 16.5 16.5C16.5 16.2239 16.2761 16 16 16H13Z",fill:"currentColor"}));function A1(){const{curAgentState:e}=pe(n=>n.agent),{conversationId:s}=te(),{t:r}=P(),a=`/conversations/${s}`;return t.jsx(Jr,{className:"h-full w-full",labels:[{label:"Changes",to:"",icon:t.jsx(zr,{className:"w-6 h-6"})},{label:t.jsx("div",{className:"flex items-center gap-1",children:r(u.VSCODE$TITLE)}),to:"vscode",icon:t.jsx(Dr,{className:"w-5 h-5"}),rightContent:B5.includes(e)?null:t.jsx(cs,{className:"w-3 h-3 text-neutral-400 cursor-pointer",onClick:async n=>{if(n.preventDefault(),n.stopPropagation(),s)try{const l=await ce.getVSCodeUrl(s);if(l.vscode_url){const i=Js(l.vscode_url);i&&window.open(i,"_blank")}}catch{}}})},{label:r(u.WORKSPACE$TERMINAL_TAB_LABEL),to:"terminal",icon:t.jsx(i3,{})},{label:"Jupyter",to:"jupyter",icon:t.jsx(l3,{})},{label:t.jsx(Xr,{}),to:"served",icon:t.jsx(ds,{})},{label:t.jsx("div",{className:"flex items-center gap-1",children:r(u.BROWSER$TITLE)}),to:"browser",icon:t.jsx(o3,{})}],children:t.jsx("div",{className:"h-full w-full",children:t.jsx(n3,{conversationPath:a})})})}function c3(){jr();const{data:e}=L1(),{conversationId:s}=te(),{data:r,isFetched:a,refetch:n}=ke(),{data:l}=q5(),{providers:i}=F1(),C=L5(),p=M5();Vr();const[c,g]=N.useState(window.innerWidth);N.useEffect(()=>{a&&!r&&l?(fe("This conversation does not exist, or you do not have permission to access it."),p("/")):r?.status==="STOPPED"&&ce.startConversation(r.conversation_id,i).then(()=>n())},[r?.conversation_id,a,l,i]),N.useEffect(()=>{C(Xt()),C(Qt())},[s]),G4(()=>{C(Xt()),C(Qt())});function d(){g(window.innerWidth)}N.useEffect(()=>(window.addEventListener("resize",d),()=>{window.removeEventListener("resize",d)}),[]);const{isOpen:m,onOpen:v,onOpenChange:T}=m4();function f(){return c<=1024?t.jsxs("div",{className:"flex flex-col gap-3 overflow-auto w-full",children:[t.jsx("div",{className:"rounded-xl overflow-hidden border border-neutral-600 w-full bg-base-secondary min-h-[494px]",children:t.jsx(T1,{})}),t.jsx("div",{className:"h-full w-full min-h-[494px]",children:t.jsx(A1,{})})]}):t.jsx($r,{orientation:e5.HORIZONTAL,className:"grow h-full min-h-0 min-w-0",initialSize:500,firstClassName:"rounded-xl overflow-hidden border border-neutral-600 bg-base-secondary",secondClassName:"flex flex-col overflow-hidden",firstChild:t.jsx(T1,{}),secondChild:t.jsx(A1,{})})}return t.jsx(Y5,{conversationId:s,children:t.jsx(F5,{children:t.jsx(U5,{children:t.jsxs("div",{"data-testid":"app-route",className:"flex flex-col h-full gap-3",children:[t.jsx("div",{className:"flex h-full overflow-auto",children:f()}),t.jsx(H4,{setSecurityOpen:v,showSecurityLock:!!e?.SECURITY_ANALYZER}),e&&t.jsx(Gr,{isOpen:m,onOpenChange:T,securityAnalyzer:e.SECURITY_ANALYZER})]})})})})}function d3(){return t.jsx(c3,{})}const l6=O5(d3);export{l6 as default};
