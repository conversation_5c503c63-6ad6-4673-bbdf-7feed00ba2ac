const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/browser-ponyfill-BAuVh-jK.js","assets/chunk-C37GKA54-CBbYr_fP.js"])))=>i.map(i=>d[i]);
import{i as N}from"./i18next-CO45VQzB.js";import{_ as z}from"./preload-helper-BXl3LOEh.js";import{s as K,b as W}from"./i18nInstance-DBIXdvxg.js";function j(o){"@babel/helpers - typeof";return j=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},j(o)}function H(){return typeof XMLHttpRequest=="function"||(typeof XMLHttpRequest>"u"?"undefined":j(XMLHttpRequest))==="object"}function Q(o){return!!o&&typeof o.then=="function"}function B(o){return Q(o)?o:Promise.resolve(o)}function L(o,e){var n=Object.keys(o);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(o);e&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(o,i).enumerable})),n.push.apply(n,t)}return n}function q(o){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?L(Object(n),!0).forEach(function(t){G(o,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(n)):L(Object(n)).forEach(function(t){Object.defineProperty(o,t,Object.getOwnPropertyDescriptor(n,t))})}return o}function G(o,e,n){return(e=J(e))in o?Object.defineProperty(o,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):o[e]=n,o}function J(o){var e=Z(o,"string");return h(e)=="symbol"?e:e+""}function Z(o,e){if(h(o)!="object"||!o)return o;var n=o[Symbol.toPrimitive];if(n!==void 0){var t=n.call(o,e);if(h(t)!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(o)}function h(o){"@babel/helpers - typeof";return h=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(o)}var g=typeof fetch=="function"?fetch:void 0;typeof global<"u"&&global.fetch?g=global.fetch:typeof window<"u"&&window.fetch&&(g=window.fetch);var S;H()&&(typeof global<"u"&&global.XMLHttpRequest?S=global.XMLHttpRequest:typeof window<"u"&&window.XMLHttpRequest&&(S=window.XMLHttpRequest));var P;typeof ActiveXObject=="function"&&(typeof global<"u"&&global.ActiveXObject?P=global.ActiveXObject:typeof window<"u"&&window.ActiveXObject&&(P=window.ActiveXObject));typeof g!="function"&&(g=void 0);if(!g&&!S&&!P)try{z(()=>import("./browser-ponyfill-BAuVh-jK.js").then(o=>o.b),__vite__mapDeps([0,1])).then(function(o){g=o.default}).catch(function(){})}catch{}var x=function(e,n){if(n&&h(n)==="object"){var t="";for(var i in n)t+="&"+encodeURIComponent(i)+"="+encodeURIComponent(n[i]);if(!t)return e;e=e+(e.indexOf("?")!==-1?"&":"?")+t.slice(1)}return e},A=function(e,n,t,i){var r=function(u){if(!u.ok)return t(u.statusText||"Error",{status:u.status});u.text().then(function(l){t(null,{status:u.status,data:l})}).catch(t)};if(i){var a=i(e,n);if(a instanceof Promise){a.then(r).catch(t);return}}typeof fetch=="function"?fetch(e,n).then(r).catch(t):g(e,n).then(r).catch(t)},D=!1,V=function(e,n,t,i){e.queryStringParams&&(n=x(n,e.queryStringParams));var r=q({},typeof e.customHeaders=="function"?e.customHeaders():e.customHeaders);typeof window>"u"&&typeof global<"u"&&typeof global.process<"u"&&global.process.versions&&global.process.versions.node&&(r["User-Agent"]="i18next-http-backend (node/".concat(global.process.version,"; ").concat(global.process.platform," ").concat(global.process.arch,")")),t&&(r["Content-Type"]="application/json");var a=typeof e.requestOptions=="function"?e.requestOptions(t):e.requestOptions,s=q({method:t?"POST":"GET",body:t?e.stringify(t):void 0,headers:r},D?{}:a),u=typeof e.alternateFetch=="function"&&e.alternateFetch.length>=1?e.alternateFetch:void 0;try{A(n,s,i,u)}catch(l){if(!a||Object.keys(a).length===0||!l.message||l.message.indexOf("not implemented")<0)return i(l);try{Object.keys(a).forEach(function(f){delete s[f]}),A(n,s,i,u),D=!0}catch(f){i(f)}}},Y=function(e,n,t,i){t&&h(t)==="object"&&(t=x("",t).slice(1)),e.queryStringParams&&(n=x(n,e.queryStringParams));try{var r=S?new S:new P("MSXML2.XMLHTTP.3.0");r.open(t?"POST":"GET",n,1),e.crossDomain||r.setRequestHeader("X-Requested-With","XMLHttpRequest"),r.withCredentials=!!e.withCredentials,t&&r.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),r.overrideMimeType&&r.overrideMimeType("application/json");var a=e.customHeaders;if(a=typeof a=="function"?a():a,a)for(var s in a)r.setRequestHeader(s,a[s]);r.onreadystatechange=function(){r.readyState>3&&i(r.status>=400?r.statusText:null,{status:r.status,data:r.responseText})},r.send(t)}catch(u){console&&console.log(u)}},ee=function(e,n,t,i){if(typeof t=="function"&&(i=t,t=void 0),i=i||function(){},g&&n.indexOf("file:")!==0)return V(e,n,t,i);if(H()||typeof ActiveXObject=="function")return Y(e,n,t,i);i(new Error("No fetch and no xhr implementation found!"))};function v(o){"@babel/helpers - typeof";return v=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(o)}function E(o,e){var n=Object.keys(o);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(o);e&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(o,i).enumerable})),n.push.apply(n,t)}return n}function k(o){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?E(Object(n),!0).forEach(function(t){R(o,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach(function(t){Object.defineProperty(o,t,Object.getOwnPropertyDescriptor(n,t))})}return o}function te(o,e){if(!(o instanceof e))throw new TypeError("Cannot call a class as a function")}function ne(o,e){for(var n=0;n<e.length;n++){var t=e[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(o,X(t.key),t)}}function oe(o,e,n){return e&&ne(o.prototype,e),Object.defineProperty(o,"prototype",{writable:!1}),o}function R(o,e,n){return(e=X(e))in o?Object.defineProperty(o,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):o[e]=n,o}function X(o){var e=ie(o,"string");return v(e)=="symbol"?e:e+""}function ie(o,e){if(v(o)!="object"||!o)return o;var n=o[Symbol.toPrimitive];if(n!==void 0){var t=n.call(o,e);if(v(t)!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(o)}var re=function(){return{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(n){return JSON.parse(n)},stringify:JSON.stringify,parsePayload:function(n,t,i){return R({},t,i||"")},parseLoadPayload:function(n,t){},request:ee,reloadInterval:typeof window<"u"?!1:60*60*1e3,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}},M=function(){function o(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};te(this,o),this.services=e,this.options=n,this.allOptions=t,this.type="backend",this.init(e,n,t)}return oe(o,[{key:"init",value:function(n){var t=this,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(this.services=n,this.options=k(k(k({},re()),this.options||{}),i),this.allOptions=r,this.services&&this.options.reloadInterval){var a=setInterval(function(){return t.reload()},this.options.reloadInterval);v(a)==="object"&&typeof a.unref=="function"&&a.unref()}}},{key:"readMulti",value:function(n,t,i){this._readAny(n,n,t,t,i)}},{key:"read",value:function(n,t,i){this._readAny([n],n,[t],t,i)}},{key:"_readAny",value:function(n,t,i,r,a){var s=this,u=this.options.loadPath;typeof this.options.loadPath=="function"&&(u=this.options.loadPath(n,i)),u=B(u),u.then(function(l){if(!l)return a(null,{});var f=s.services.interpolator.interpolate(l,{lng:n.join("+"),ns:i.join("+")});s.loadUrl(f,a,t,r)})}},{key:"loadUrl",value:function(n,t,i,r){var a=this,s=typeof i=="string"?[i]:i,u=typeof r=="string"?[r]:r,l=this.options.parseLoadPayload(s,u);this.options.request(this.options,n,l,function(f,c){if(c&&(c.status>=500&&c.status<600||!c.status))return t("failed loading "+n+"; status code: "+c.status,!0);if(c&&c.status>=400&&c.status<500)return t("failed loading "+n+"; status code: "+c.status,!1);if(!c&&f&&f.message){var d=f.message.toLowerCase(),p=["failed","fetch","network","load"].find(function(O){return d.indexOf(O)>-1});if(p)return t("failed loading "+n+": "+f.message,!0)}if(f)return t(f,!1);var b,w;try{typeof c.data=="string"?b=a.options.parse(c.data,i,r):b=c.data}catch{w="failed parsing "+n+" to json"}if(w)return t(w,!1);t(null,b)})}},{key:"create",value:function(n,t,i,r,a){var s=this;if(this.options.addPath){typeof n=="string"&&(n=[n]);var u=this.options.parsePayload(t,i,r),l=0,f=[],c=[];n.forEach(function(d){var p=s.options.addPath;typeof s.options.addPath=="function"&&(p=s.options.addPath(d,t));var b=s.services.interpolator.interpolate(p,{lng:d,ns:t});s.options.request(s.options,b,u,function(w,O){l+=1,f.push(w),c.push(O),l===n.length&&typeof a=="function"&&a(f,c)})})}}},{key:"reload",value:function(){var n=this,t=this.services,i=t.backendConnector,r=t.languageUtils,a=t.logger,s=i.language;if(!(s&&s.toLowerCase()==="cimode")){var u=[],l=function(c){var d=r.toResolveHierarchy(c);d.forEach(function(p){u.indexOf(p)<0&&u.push(p)})};l(s),this.allOptions.preload&&this.allOptions.preload.forEach(function(f){return l(f)}),u.forEach(function(f){n.allOptions.ns.forEach(function(c){i.read(f,c,"read",null,null,function(d,p){d&&a.warn("loading namespace ".concat(c," for language ").concat(f," failed"),d),!d&&p&&a.log("loaded namespace ".concat(c," for language ").concat(f),p),i.loaded("".concat(f,"|").concat(c),d,p)})})})}}}])}();M.type="backend";const{slice:ae,forEach:se}=[];function ue(o){return se.call(ae.call(arguments,1),e=>{if(e)for(const n in e)o[n]===void 0&&(o[n]=e[n])}),o}function fe(o){return typeof o!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(n=>n.test(o))}const T=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,ce=function(o,e){const t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},i=encodeURIComponent(e);let r=`${o}=${i}`;if(t.maxAge>0){const a=t.maxAge-0;if(Number.isNaN(a))throw new Error("maxAge should be a Number");r+=`; Max-Age=${Math.floor(a)}`}if(t.domain){if(!T.test(t.domain))throw new TypeError("option domain is invalid");r+=`; Domain=${t.domain}`}if(t.path){if(!T.test(t.path))throw new TypeError("option path is invalid");r+=`; Path=${t.path}`}if(t.expires){if(typeof t.expires.toUTCString!="function")throw new TypeError("option expires is invalid");r+=`; Expires=${t.expires.toUTCString()}`}if(t.httpOnly&&(r+="; HttpOnly"),t.secure&&(r+="; Secure"),t.sameSite)switch(typeof t.sameSite=="string"?t.sameSite.toLowerCase():t.sameSite){case!0:r+="; SameSite=Strict";break;case"lax":r+="; SameSite=Lax";break;case"strict":r+="; SameSite=Strict";break;case"none":r+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return t.partitioned&&(r+="; Partitioned"),r},_={create(o,e,n,t){let i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};n&&(i.expires=new Date,i.expires.setTime(i.expires.getTime()+n*60*1e3)),t&&(i.domain=t),document.cookie=ce(o,e,i)},read(o){const e=`${o}=`,n=document.cookie.split(";");for(let t=0;t<n.length;t++){let i=n[t];for(;i.charAt(0)===" ";)i=i.substring(1,i.length);if(i.indexOf(e)===0)return i.substring(e.length,i.length)}return null},remove(o,e){this.create(o,"",-1,e)}};var le={name:"cookie",lookup(o){let{lookupCookie:e}=o;if(e&&typeof document<"u")return _.read(e)||void 0},cacheUserLanguage(o,e){let{lookupCookie:n,cookieMinutes:t,cookieDomain:i,cookieOptions:r}=e;n&&typeof document<"u"&&_.create(n,o,t,i,r)}},de={name:"querystring",lookup(o){let{lookupQuerystring:e}=o,n;if(typeof window<"u"){let{search:t}=window.location;!window.location.search&&window.location.hash?.indexOf("?")>-1&&(t=window.location.hash.substring(window.location.hash.indexOf("?")));const r=t.substring(1).split("&");for(let a=0;a<r.length;a++){const s=r[a].indexOf("=");s>0&&r[a].substring(0,s)===e&&(n=r[a].substring(s+1))}}return n}},pe={name:"hash",lookup(o){let{lookupHash:e,lookupFromHashIndex:n}=o,t;if(typeof window<"u"){const{hash:i}=window.location;if(i&&i.length>2){const r=i.substring(1);if(e){const a=r.split("&");for(let s=0;s<a.length;s++){const u=a[s].indexOf("=");u>0&&a[s].substring(0,u)===e&&(t=a[s].substring(u+1))}}if(t)return t;if(!t&&n>-1){const a=i.match(/\/([a-zA-Z-]*)/g);return Array.isArray(a)?a[typeof n=="number"?n:0]?.replace("/",""):void 0}}}return t}};let m=null;const C=()=>{if(m!==null)return m;try{if(m=typeof window<"u"&&window.localStorage!==null,!m)return!1;const o="i18next.translate.boo";window.localStorage.setItem(o,"foo"),window.localStorage.removeItem(o)}catch{m=!1}return m};var ge={name:"localStorage",lookup(o){let{lookupLocalStorage:e}=o;if(e&&C())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(o,e){let{lookupLocalStorage:n}=e;n&&C()&&window.localStorage.setItem(n,o)}};let y=null;const I=()=>{if(y!==null)return y;try{if(y=typeof window<"u"&&window.sessionStorage!==null,!y)return!1;const o="i18next.translate.boo";window.sessionStorage.setItem(o,"foo"),window.sessionStorage.removeItem(o)}catch{y=!1}return y};var he={name:"sessionStorage",lookup(o){let{lookupSessionStorage:e}=o;if(e&&I())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(o,e){let{lookupSessionStorage:n}=e;n&&I()&&window.sessionStorage.setItem(n,o)}},me={name:"navigator",lookup(o){const e=[];if(typeof navigator<"u"){const{languages:n,userLanguage:t,language:i}=navigator;if(n)for(let r=0;r<n.length;r++)e.push(n[r]);t&&e.push(t),i&&e.push(i)}return e.length>0?e:void 0}},ye={name:"htmlTag",lookup(o){let{htmlTag:e}=o,n;const t=e||(typeof document<"u"?document.documentElement:null);return t&&typeof t.getAttribute=="function"&&(n=t.getAttribute("lang")),n}},ve={name:"path",lookup(o){let{lookupFromPathIndex:e}=o;if(typeof window>"u")return;const n=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(n)?n[typeof e=="number"?e:0]?.replace("/",""):void 0}},be={name:"subdomain",lookup(o){let{lookupFromSubdomainIndex:e}=o;const n=typeof e=="number"?e+1:1,t=typeof window<"u"&&window.location?.hostname?.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(t)return t[n]}};let U=!1;try{document.cookie,U=!0}catch{}const $=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];U||$.splice(1,1);const we=()=>({order:$,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:o=>o});class F{constructor(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,n)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=e,this.options=ue(n,this.options||{},we()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=i=>i.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=t,this.addDetector(le),this.addDetector(de),this.addDetector(ge),this.addDetector(he),this.addDetector(me),this.addDetector(ye),this.addDetector(ve),this.addDetector(be),this.addDetector(pe)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,n=[];return e.forEach(t=>{if(this.detectors[t]){let i=this.detectors[t].lookup(this.options);i&&typeof i=="string"&&(i=[i]),i&&(n=n.concat(i))}}),n=n.filter(t=>t!=null&&!fe(t)).map(t=>this.options.convertDetectedLanguage(t)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?n:n.length>0?n[0]:null}cacheUserLanguage(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;n&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||n.forEach(t=>{this.detectors[t]&&this.detectors[t].cacheUserLanguage(e,this.options)}))}}F.type="languageDetector";const Se={type:"3rdParty",init(o){K(o.options.react),W(o)}},je=[{label:"English",value:"en"},{label:"日本語",value:"ja"},{label:"简体中文",value:"zh-CN"},{label:"繁體中文",value:"zh-TW"},{label:"한국어",value:"ko-KR"},{label:"Norsk",value:"no"},{label:"Arabic",value:"ar"},{label:"Deutsch",value:"de"},{label:"Français",value:"fr"},{label:"Italiano",value:"it"},{label:"Português",value:"pt"},{label:"Español",value:"es"},{label:"Türkçe",value:"tr"},{label:"Українська",value:"uk"}];N.use(M).use(F).use(Se).init({fallbackLng:"en",debug:!1,load:"currentOnly"});export{je as A};
