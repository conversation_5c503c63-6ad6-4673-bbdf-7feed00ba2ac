import{w as e,j as t,O as n,M as a,L as i,S as o,e as r}from"./chunk-C37GKA54-CBbYr_fP.js";import{O as c}from"./index-cxP66Ws3.js";function l({children:s}){return t.jsxs("html",{lang:"en",children:[t.jsxs("head",{children:[t.jsx("meta",{charSet:"utf-8"}),t.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),t.jsx(a,{}),t.jsx(i,{})]}),t.jsxs("body",{children:[s,t.jsx(o,{}),t.jsx(r,{}),t.jsx(c,{})]})]})}const d=()=>[{title:"OpenHands"},{name:"description",content:"Let's Start Building!"}],m=e(function(){return t.jsx(n,{})});export{l as Layout,m as default,d as meta};
